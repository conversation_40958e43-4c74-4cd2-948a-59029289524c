{"name": "sportwrench", "version": "0.0.0", "private": true, "dependencies": {"StickyTableHeaders": "~0.1.12", "angular": "~1.5.6", "angular-animate": "1.3.20", "angular-bootstrap": "0.13.4", "angular-cookies": "~1.5.8", "angular-dragdrop": "~1.0.12", "angular-google-maps": "~2.3.3", "angular-loading-bar": "~0.7.1", "angular-resource": "~1.2.26", "angular-route": "~1.2.18", "angular-sanitize": "~1.2.18", "angular-toastr": "1.5", "angular-ui-router": "0.3.1", "angular-ui-utils": "bower-mask", "angular-utils-ui-breadcrumbs": "~0.2.1", "bootstrap-sass-official": "~3.2.0", "bootstrap-ui-datetime-picker": "~2.4.0", "ckeditor": "4.22.1", "font-awesome": "~4.7.0", "jquery": "2.0.3", "js-xls": "~0.7.1", "js-xlsx": "~0.7.10", "moment": "^2.24.0", "ng-ckeditor": "~0.2.1", "ng-table": "~0.5.4", "ngInfiniteScroll": "1.0.0", "ngSelectable": "~1.0.4", "ngstorage": "~0.3.0", "oclazyload": "0.4", "skeuocard": "~1.1.0", "ui-bootstrap": "~0.13.4", "underscore": "1.8.3", "angular-file-upload": "~2.3.3", "angular-simple-logger": "~0.1.7", "angular-clipboard": "^1.6.2", "moment-timezone": "^0.5.23", "compass-mixins": "^1.0.2", "angular-ui-select": "^0.19.8", "angular-bootstrap-colorpicker": "^3.0.32", "angularjs-dropdown-multiselect": "^2.0.0-beta.10"}, "resolutions": {"angular": "~1.5.6", "jquery": "2.0.3", "angular-animate": "1.3.20", "intro.js": "1.1.0", "angular-bootstrap": "0.13.4", "angular-google-maps": "~2.3.3", "angular-cookies": "1.5.8"}, "devDependencies": {"angular-utils-ui-breadcrumbs": "~0.2.1"}}