'use strict';

/**
 * The script converts a purchase of tickets made in the Basic Ticketing mode into an Assigned-mode purchase
 * A use case: 
 * An EO accidentally has opened ticketing purchases in the wrong mode: Basic instead of Assigned. 
 *
 * The launch example: node swt-convert-basic-to-assigned.js --db=postgres://<user>:<pswd>@<host>:<port>/<db> --purchase=1234567 --s3Key=<key> --s3Secret=<secret> --s3Bucket=<bucket>
 */



/**
 * A short description of the algorithm: 
 * 
 * I. If there's only one ticket in a purchase:
 * (A single "purchase_ticket" row with a value of 1 in the "quantity" column)
 *
 * 1. Make a duplicate of the "purchase" row
 * 2. Make the duplicate a payment-row
 * 3. Make the initial row a ticket-row 
 *
 * The goal of this algorithm is to make the existing receipt and the qr code working after the
 * conversion.
 * 
 * II. If there're multiple tickets
 * 
 * 1. For the first ticket: 
 * 1.1 Apply the steps from the p."I" above
 * 1.2 Create a "purchase_ticket" row for the created ticket-row
 * 2. For all the rest tickets: 
 * 2.1 Create a ticket-row
 * 2.2 Generate QR code and upload it to S3 
 * 2.3 Create a new "purchase_ticket" row for the created in the p.2.1 ticket-row
 * 3. remove all initially-created "purchase_ticket" rows (the rows of the basic mode purchase)
 *
 * NOTES: 
 * A payment-row: a "purchase" row with "is_payment" = TRUE and "is_ticket" = FALSE. 
 * A ticket-row: a "purchase" row with "is_payment" = FALSE and "is_ticket" = TRUE 
 *     and "linked_purchase_id" = the corresponding "payment"-row "purchase_id" value.
 *
 * Details are here: https://drive.google.com/drive/folders/1q_go7cpagdXCb7EGPPOz9bdj-7ruwrj5
 */

const ARGV = require('optimist').argv;
const _ = require('lodash');



const {
    /**
      * Database (Postgresql) Coonnection String
      * @type {String}
      */
    DB_CONN, 
    /**
      * ID of a purchase to convert
      * @type {Number}
      */
    BASIC_PURCHASE_ID,
    /**
     * S3 connection object
     * See: https://github.com/Automattic/knox#examples
     * @type {Object}
     */
    S3_CONFIG,
} = (function readAndValidateArguments (_, argv) {
    let {
        db,
        purchase,
        s3Key,
        s3Secret,
        s3Bucket,
    } = argv;

    if (!(_.isString(db) && db)) {
        // TODO: add a regexp validation
      console.error('Expecting "db" to be a string!');
      process.exit(1);
    }

    purchase = Number(purchase);

    if (!(_.isNumber(purchase) && purchase > 0)) {
      console.error('Expecting "purchase" to be a positive integer!');
      process.exit(1);
    }

    if (!(_.isString(s3Key) && s3Key)) {
        console.error('Expecting "s3Key" to be a string!');
        process.exit(1);
    }

    if (!(_.isString(s3Secret) && s3Secret)) {
        console.error('Expecting "s3Secret" to be a string!');
        process.exit(1);
    }

    if (!(_.isString(s3Bucket) && s3Bucket)) {
        console.error('Expecting "s3Bucket" to be a string!');
        process.exit(1);
    }

    const S3_CONFIG = {
        key: s3Key,
        secret: s3Secret,
        bucket: s3Bucket,
    }

    return {
        DB_CONN: db,
        BASIC_PURCHASE_ID: purchase,
        S3_CONFIG,
    }
})(_, ARGV);



const { Client: PGClient } = require('pg');
const Db = (function initDbClient (client, connection) {
    const Db = new client(connection);

    const _query = Db.query.bind(Db);

    Db.query = function (q, p) {
        return _query(...arguments)
        .catch(err => {

            err.query = q;
            err.params = p;

            throw err;
        })
    };

    return Db;
})(PGClient, DB_CONN);

const EOL = require('os').EOL;
const QR = require('../api/lib/QRTicketsGenerator.js');


/* =================== */
/* === Entry Point === */
/* =================== */

(async function main () {
    await Db.connect();

    let purchase = await getPurchase(Db, BASIC_PURCHASE_ID);

    validatePurchase(_, purchase);

    let event = await getEvent(Db, purchase.event_id);

    validateEvent(event);

    let ticketsQty = getTicketsQuantity(purchase.purchase_ticket_rows);

    console.log('Payment has', ticketsQty, 'ticket(s)');

    if (ticketsQty === 1) {
        let ticketPrice = purchase.purchase_ticket_rows[0].ticket_price;
        await copyAndConvertToTicket(Db, BASIC_PURCHASE_ID, ticketPrice);
    } else {    
        let paymentRowID = null;
        let ticketIndex = 0;

        // TODO: use a generator
        for (let i = 0; i < purchase.purchase_ticket_rows.length; ++i) {    

            let purchaseTicket = purchase.purchase_ticket_rows[i];  

            let {
                purchase_ticket_id: purchaseTicketID,
                ticket_price: ticketPrice,
                quantity,
                event_ticket_id: eventTicketID,
                ticket_sort_order: order
            } = purchaseTicket;


            let eventTicketRow = {
                order,
                quantity,
            }

            for (let j = 0; j < quantity; ++j) {

                ++ticketIndex;

                if (i === j && j === 0) {
                    paymentRowID = await copyAndConvertToTicket(
                                                   Db, BASIC_PURCHASE_ID, ticketPrice, ticketIndex);

                    await createPurchaseTicketRow(Db, purchaseTicketID, BASIC_PURCHASE_ID);

                    console.log(EOL, 'Created a new payment row', '#' + paymentRowID);
                } else {                    
                    let ticketRowID = await createTicketRow(Db, BASIC_PURCHASE_ID, {
                        linkedPurchaseID: paymentRowID,
                        amount: purchaseTicket.ticket_price,
                    }, ticketIndex);

                    console.log(EOL, 'Created a new ticket row', '#' + ticketRowID);

                     let { barcode, hash } = await generateAndUploadQRCode(Db, QR, {
                        purchase_id: ticketRowID,
                        ticketTypes: event.tickets,
                        eventID: purchase.event_id,
                        ts: purchase.ts, 
                        eventTicketRow,
                        user_id: -1,
                        s3Config: S3_CONFIG,
                    });

                    console.log('\tbarcode = ', barcode);
                    console.log('\thash = ', hash);

                    await setBarcode(Db, ticketRowID, barcode, hash);

                    await createPurchaseTicketRow(Db, purchaseTicketID, ticketRowID);
                }
            }
        }

        await removeOldPurchaseTicketRows(Db, purchase.purchase_ticket_rows, BASIC_PURCHASE_ID);
    }

    await Db.end().catch(() => {});

    process.exit(0);
})()
.catch(async err => {

    await Db.end().catch(() => {});

    console.error(err);
    process.exit(1);
});

/* =================== */



function getPurchase (Db, id) {
    let query = 
        `SELECT
            p."event_id", p."type", p."status", p."is_ticket", p."is_payment", 
            (EXTRACT(EPOCH FROM p."created"))::INT "ts", (
                SELECT ARRAY_AGG(ROW_TO_JSON("pt_rows"))
                FROM (
                    SELECT
                        pt."purchase_ticket_id", pt."event_ticket_id", pt."canceled", pt."quantity",
                        pt."ticket_price", et."sort_order" "ticket_sort_order"
                    FROM "purchase_ticket" pt 
                    JOIN "event_ticket" et 
                        ON et."event_ticket_id" = pt."event_ticket_id"
                    WHERE pt."purchase_id" = p."purchase_id"
                ) "pt_rows"
            ) "purchase_ticket_rows"
         FROM "purchase" p 
         WHERE p."purchase_id" = $1
        `

    return Db.query(query, [id]).then(res => res.rows[0] || null);
}

function validatePurchase (_, purchase) {
    if (_.isEmpty(purchase)) {
        throw new Error('Purchase not found');
    }

    if (purchase.status === 'canceled') {
        throw new Error('Purchase is canceled')
    }

    if (purchase.is_ticket !== purchase.is_payment || purchase.is_ticket === false) {
        throw new Error('Not a basic purchase')
    }
}

function getEvent (Db, id) {
    let query = 
        `SELECT 
            COALESCE(
                (tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, FALSE) 
                                                                             "is_assigned_mode_on",
            (
                SELECT ARRAY_AGG(ROW_TO_JSON(t))
                FROM (
                    SELECT
                        et.event_ticket_id,  
                        et.current_price::NUMERIC price,  
                        et.application_fee, 
                        et.label, 
                        et.sort_order, 
                        et.has_barcode,
                        COALESCE(et.kiosk_surcharge, 0)::NUMERIC "kiosk_surcharge",
                        (SELECT
                             COALESCE(
                                 JSONB_OBJECT_AGG(
                                     TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD'),
                                     TRUE
                                 ),
                                 '{}'::JSONB
                             )
                        FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) AS valid_dates
                    FROM "event_ticket" et
                    WHERE et.event_id = e.event_id 
                    ORDER BY et.event_ticket_id ASC 
                ) "t"
            ) "tickets"
         FROM "event" e
         WHERE e."event_id" = $1`;

    return Db.query(query, [id]).then(res => res.rows[0] || null);
}

function validateEvent (event) {
    if (_.isEmpty(event)) {
        throw new Error('Event not found');
    }

    if (!event.is_assigned_mode_on) {
        throw new Error('Assigned mode is off on the event');
    }

    if (_.isEmpty(event.tickets)) {
        throw new Error('No ticket types found!');
    }
}

function getTicketsQuantity (purchaseTicketRows) {
    return purchaseTicketRows.reduce((res, row) => res + Number(row.quantity), 0);
}

async function copyAndConvertToTicket (Db, id, ticketPrice, ticketIndex) {
    /*
     * Make a new payment-row
     */
    let clearBarcode = true;
    let purchaseCopyID = await copyPurchaseRow(Db, id, clearBarcode);
    /**
     * Convert the initial payment-row to a ticket-row
     */
    await convertPaymentRowToTicket(Db, id, purchaseCopyID, ticketPrice, ticketIndex);

    return purchaseCopyID;
}

async function copyPurchaseRow (Db, id, clearTicketColumns = false) {
    try {
        await Db.query('BEGIN;');

        await Db.query(
            `CREATE TEMP TABLE "temp_swt_convert_basic_to_assigned" ON COMMIT DROP AS
             SELECT * FROM "purchase" WHERE "purchase_id" = $1`,
             [id]
        );

        await Db.query(
            `UPDATE "temp_swt_convert_basic_to_assigned" 
             SET "purchase_id" = NEXTVAL('purchase_purchase_id_seq'::REGCLASS)
              ${
                  clearTicketColumns 
                    ? `, "is_ticket" = FALSE 
                       , "ticket_barcode" = NULL
                       , "tickets_additional" = NULL`
                    : ''
             }
            `
        );

        let { rows } = await Db.query(
            `INSERT INTO "purchase" 
             TABLE "temp_swt_convert_basic_to_assigned" 
             RETURNING "purchase_id"`
        );

        await Db.query('COMMIT;');

        return rows[0].purchase_id;
    } catch (err) {
        await Db.query('ROLLBACK;').catch(() => {});
        throw err;
    }
}

function convertPaymentRowToTicket (Db, id, linkedPurchaseID, ticketPrice, ticketIndex) {
    const COLUMNS_TO_CLEAR = [
        'additional_fee_amount', 
        'card_last_4', 
        'card_name', 
        'collected_sw_fee', 
        'net_profit', 
        'scanned_at', 
        'scanner_id', 
        'scanner_location', 
        'stripe_account_id', 
        'stripe_card_fingerprint', 
        'stripe_card_id', 
        'stripe_charge_id', 
        'stripe_fee',
        'stripe_payment_type',
        'stripe_percent', 
        'stripe_transfer_status', 
        'tickets_scan'
    ];

    let query = 
    `UPDATE "purchase"
     SET ${
            COLUMNS_TO_CLEAR.map(col => `"${col}" = NULL,`).join('\n')
         }
         "last" = "last" || ' ' || COALESCE($3::TEXT, '') || ' (Help Desk)',
         "linked_purchase_id" = $2,
         "is_payment" = FALSE, 
         "amount" = $4
     WHERE "purchase_id" = $1`;

     return Db.query(query, [id, linkedPurchaseID, ticketIndex || null, ticketPrice]).then(() => {});
}

async function generateAndUploadQRCode (Db, QR, params) {
    let barcode = await generateTicketBarcode(Db);

    let hash = await generateQRCodeAndHash(QR, Object.assign({ barcode }, params));

    return { barcode, hash };
}

// NOTE: this is a duplicate of SWTReceiptService.generateUniqueBarcode().
// The service has a lot of global dependencies so it was impossible to use it here.
function generateTicketBarcode (Db) {
    let code = Math.floor(Math.random() * (1000000000 - 100000000 + 1)) + 100000000;
    return Db.query(
        `SELECT p.purchase_id 
         FROM purchase p 
         WHERE p.ticket_barcode = $1 
            AND p.payment_for = 'tickets'`,
        [code]
    ).then(result => {
        if(result.rows.length === 0) {
            return code;
        } else {
            return generateTicketBarcode();
        }
    });
}

async function generateQRCodeAndHash (QR, params) {
    let {
        purchase_id,
        ticketTypes,
        eventID,
        ts, 
        barcode,
        eventTicketRow,
        user_id, 
        s3Config
    } = params || {};

    let ticketsContentChunk = QR.generateQRCodeItemsString(ticketTypes, [eventTicketRow]);

    let qrData = {
        tickets: ticketsContentChunk,
        event_id: eventID,
        ticket_barcode: barcode,
        purchase_timestamp: ts,
        purchase_id, user_id,
    }

    let content = QR.generateContent(qrData);

    return QR.generate(Object.assign({
        qr_content: content,
        s3Config
    }, qrData));
}

async function createTicketRow (Db, sourceTicketID, params, ticketIndex) {
    let ticketID = await copyPurchaseRow(Db, sourceTicketID);

    let { linkedPurchaseID, amount, index } = params || {};

    let query = 
    `UPDATE "purchase" 
     SET "linked_purchase_id" = $2,
        "amount" = $3,
        "last" = (
            SELECT 
                p."last" || ' ' || COALESCE($4::TEXT, '') || ' (Help Desk)'
            FROM "purchase" p
            WHERE p."purchase_id" = $2
        ), 
        "user_id" = -1
    WHERE "purchase_id" = $1`;

    await Db.query(query, [ticketID, linkedPurchaseID, amount, ticketIndex || null]);

    return ticketID;
}

function setBarcode (Db, ticketID, barcode, hash) {
    let query = 
        `UPDATE "purchase"
         SET "ticket_barcode" = $2, 
             "hash" = $3
         WHERE "purchase_id" = $1`;

    return Db.query(query, [ticketID, barcode, hash])
}

function createPurchaseTicketRow (Db, purchaseTicketID, ticketRowID) {
    let query = 
    `INSERT INTO "purchase_ticket" (
        "created", "modified", "purchase_id", "amount", "ticket_price",
        "quantity", "canceled", "available", "event_ticket_id", "discount",
        "ticket_discount_id", "discounted_quantity", "ticket_fee", "kiosk_surcharge"
    )
    SELECT
        "created", "modified", $2 "purchase_id", "ticket_price" "amount", "ticket_price",
        1::INT "quantity", "canceled", 1::INT "available", "event_ticket_id", "discount",
        "ticket_discount_id", "discounted_quantity", "ticket_fee", "kiosk_surcharge"
    FROM "purchase_ticket" WHERE "purchase_ticket_id" = $1`;

    return Db.query(query, [purchaseTicketID, ticketRowID]).then(() => {})
}

async function removeOldPurchaseTicketRows (Db, purchaseTicketRows, purchaseID) {
    let ids = purchaseTicketRows.map(r => r.purchase_ticket_id);
    // Convert to a PG-array to be able to use in the "ANY()"
    ids = `{${ids.join(',')}}`;

    let query = 
    `DELETE FROM "purchase_ticket" 
     WHERE "purchase_ticket_id" = ANY($2) AND "purchase_id" = $1`;

    return Db.query(query, [purchaseID, ids]).then(res => res.rowCount);
}
