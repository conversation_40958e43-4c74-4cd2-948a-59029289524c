'use strict';

const argv  = require('optimist').argv;
const fs    = require('fs').promises;
const xlsx  = require('xlsx');
const swUtils  = require('../api/lib/swUtils');

global.knex = require('knex')({client: 'pg'});

require('../scheduler/globals-loder').init();

const {
    import_id: IMPORT_ID,
    event: EVENT_ID,
    path: PATH_TO_FILE,
    conn: DB_CONNECTION,
    type: TICKET_TYPE,
    receiver_type: RECEIVER_TYPE,
    border_colour: BORDER_COLOUR,
} = argv;

const DOCUMENT_HEADING = {
    FIRST_NAME: 'First Name',
    LAST_NAME : 'Last Name',
    EMAIL: 'Email',
    PHONE: 'Phone',
    BORDER_COLOUR_FIELD: 'QR Code Border Color'
};

const connectionConfig = JSON.parse(Buffer.from(DB_CONNECTION, 'base64').toString('utf-8'));

const connectionParams = _.isObject(connectionConfig)
    ? _.omit(connectionConfig, 'module')
    : {connectionString: connectionConfig};
const db = require('../api/lib/db.js');
global.Db = new db(connectionParams, {}, {error: console.error});

async function processImport() {
    try {
        const file = await fs.readFile(PATH_TO_FILE);
        let parsedRows = parseFile(file);

        if(!parsedRows.length) {
            throw { validation: 'Empty list passed' };
        }

        const { createdTickets, sentReceipts, duplicatesCount } = await TicketsService.freeTicket.generateBulk(
            IMPORT_ID, EVENT_ID, parsedRows, RECEIVER_TYPE
        );

        return { createdTickets, sentReceipts, duplicatesCount };
    } catch (err) {
        throw err;
    }
}

function parseFile(file) {
    let workbook = xlsx.read(file);

    let workingSheet = workbook.Sheets[workbook.SheetNames[0]];
    let rawJSON      = xlsx.utils.sheet_to_json(workingSheet, { header: 1, raw: true });

    return formatRows(rawJSON);
}

function getBorderColour (border_colour) {
    if(!BORDER_COLOUR) {
        return;
    }

    return border_colour || BORDER_COLOUR;
}

function formatRows (parsedFile) {
    let headings = parsedFile[0];

    const columnsConfig = [
        {name: 'phone', title: DOCUMENT_HEADING.PHONE, required: true},
        {name: 'email', title: DOCUMENT_HEADING.EMAIL, required: true},
        {name: 'first', title: DOCUMENT_HEADING.FIRST_NAME, required: true},
        {name: 'last', title: DOCUMENT_HEADING.LAST_NAME, required: true},
        {name: 'border_colour', title: DOCUMENT_HEADING.BORDER_COLOUR_FIELD, required: false},
    ].reduce((config, columnConfig) => {
        const index = headings.indexOf(columnConfig.title);
        if(index === -1) {
            if(columnConfig.required) {
                throw { validation: `"${columnConfig.title}" column not found` };
            }
            else {
                return config;
            }
        }
        config.push({
            ...columnConfig,
            index,
        });
        return config;
    }, []);

    const parseRow = (row, index) => {
        return columnsConfig.reduce((result, columnConfig) => {
            let value = row[columnConfig.index];
            if(!value) {
                if(columnConfig.required) {
                    throw {validation: `"${columnConfig.title}" column value is required on row ${index+1}`};
                } else {
                    result[columnConfig.name] = null;
                    return result;
                }
            }
            result[columnConfig.name] = `${value}`.trim();

            return result;
        }, {});
    };

    return parsedFile.reduce((all, row, index) => {
        const isHeadings = index === 0;
        const rowHasValues = row.some(v => Boolean(v));
        if(!isHeadings && rowHasValues) {

            const {
                phone,
                email,
                first,
                last,
                border_colour
            } = parseRow(row, index);

            all.push({
                email: email,
                first: first,
                last: last,
                phone: phone,
                ticket_type: TICKET_TYPE,
                receiver_type: RECEIVER_TYPE || null,
                border_colour: getBorderColour(border_colour),
            });
        }

        return all;
    }, [])
}

processImport()
    .then(async result => {
        const progress = 1;
        await ImportProgressService.updateImport(IMPORT_ID, ImportProgressService.STATUSES.FINISHED, progress, result);
        process.exit(0);
    })
    .catch(async err => {
        let output = err;
        let exitCode = 0;
        if(!swUtils.isValidationError(err)) {
            exitCode = 1;
            output = { message: 'Internal server error' };
            console.error(err);
        }
        try {
            await ImportProgressService.updateImport(IMPORT_ID, ImportProgressService.STATUSES.ERROR, null, output);
        }
        catch(err) {
            console.error(err);
        }
        Db.end();

        process.exit(exitCode);
    });
