'use strict';

const
    fs = require('fs'),
    path = require('path'),
    argv = require('optimist').argv,
    co = require('co'),
    xlsx = require('xlsx'),
    pg = require('pg'),
    moment = require('moment'),
    _ = require('lodash');

const
    UPLOAD_PATH         = argv.path,
    CONNECTION          = argv.conn,
    CLUB_ID 			= Number(argv.club),
    CLUB_CODE 			= argv.code,
    CLUB_REGION 		= argv.region,
    CLUB_COUNTRY 		= argv.country && argv.country.toLowerCase(),
    CLUB_GENDERS        = JSON.parse(argv.genders),
    OWNER_ID 			= Number(argv.owner),
    SEASON 				= Number(argv.season),
    CLUB_COUNTRY_CODE 	= parseCountry(CLUB_COUNTRY),
    FIRST_HEADER_NAME   = 'Team Code';

const
	VOLLEYBALL_ID 		= 2,
	INDOOR_ID 			= 1,
	GENDERS 			= {
		male 	: 'male',
		m 		: 'male',
		f 		: 'female',
		female 	: 'female',
		w 		: 'female'
	},
	PARSE_DATE_FORMAT 	= 'MM/DD/YYYY',
	DB_DATE_FORMAT 		= 'YYYY-MM-DD',
	EMAIL_PATTERN 		= /^\w+(?:[._+-]\w+)*@\w+(?:[_.-]\w+)*\.[a-zA-Z]{2,4}$/,
	PHONE_PATTERN 		= /^([0-9]{10,15})$/,
	MIN_COLUMNS_COUNT 	= 5;

if(!UPLOAD_PATH) {
    console.error('Invalid Upload Path');
    process.exit(1);
}

if(!CONNECTION) {
    console.error('Invalid Connection String');
    process.exit(1);
}

if(!CLUB_ID) {
	console.error('Invalid Club Id');
	process.exit(1);
}

if(!CLUB_CODE) {
	console.error('Invalid Club Code');
	process.exit(1);
}

if(!SEASON) {
	console.error('Invalid Season');
	process.exit(1);
}

if(!OWNER_ID) {
	console.error('Invalid Owner Id');
	process.exit(1);
}

if(!CLUB_COUNTRY) {
	console.error('Club Country required');
	process.exit(1);
}

if(!(CLUB_GENDERS && CLUB_GENDERS.length)) {
    console.error('Club Genders required');
    process.exit(1)
}

/**
 * The method recalculates XLSX-sheet size
 * Taken from here: https://github.com/SheetJS/js-xlsx/issues/764
 */
function updateSheetRange(ws) {
  let range = {s:{r:20000000, c:20000000},e:{r:0,c:0}};

  Object.keys(ws)
  .filter(x => { return x.charAt(0) != "!"; })
  .map(xlsx.utils.decode_cell)
  .forEach(x => {
    range.s.c = Math.min(range.s.c, x.c);
    range.s.r = Math.min(range.s.r, x.r);
    range.e.c = Math.max(range.e.c, x.c);
    range.e.r = Math.max(range.e.r, x.r);
  });
  ws['!ref'] = xlsx.utils.encode_range(range);

  return ws;
}

co(function* () {
	let fileBuffer = yield (new Promise(function (resolve, reject) {
		let uploadFilePath = path.resolve(UPLOAD_PATH, `FOREIGN-ROSTER-${new Date().getTime()}-${CLUB_ID}.xlsx`);
        let outputStream = fs.createWriteStream(uploadFilePath),
            inputStream  = fs.createReadStream(null, { fd: 0 }),
            chunks       = [],
            totalLength  = 0

        inputStream.on('data', chunk => {
            chunks.push(chunk);
            totalLength += chunk.length;
        });

        inputStream.on('close', () => {
            if(totalLength === 0) {
                reject({ validation: 'Empty File passed', statusCode: 2 })
            } else {
                resolve(Buffer.concat(chunks, totalLength))
            }
        })

        inputStream.on('error', err => {
            reject(err);
        })

        inputStream.pipe(outputStream);
    }));

    console.log('Connecting to DB ...');

    let dbClient = new pg.Client(JSON.parse(Buffer.from(CONNECTION, 'base64').toString('utf-8')));
    dbClient.connect();
    let workbook = xlsx.read(fileBuffer);
    console.log('workbook', workbook.Sheets[workbook.SheetNames[0]]);
    let workingSheet = updateSheetRange(workbook.Sheets[workbook.SheetNames[0]]);
    console.log('workingSheet', workingSheet);
    let jsonSheetRows = xlsx.utils.sheet_to_json(workingSheet, {
                            header: 1
                        });

    if(jsonSheetRows.length === 0) {
        throw { validation: 'Empty File passed', statusCode: 2 }
    }

    yield (runSQL(dbClient, 'BEGIN'));

    let teamsData =  yield (runSQL(
    	dbClient,
    	`SELECT 
    		FORMAT('{ %s }', STRING_AGG(
			        FORMAT('"%s": %s', LOWER(TRIM(mt."organization_code")), mt.master_team_id), ', '
			    )
		    )::JSON "team_codes"
    	 FROM "master_team" mt 
    	 WHERE mt.master_club_id = $1 
    	 	AND mt.deleted IS NULL 
    	 	AND mt.organization_code IS NOT NULL 
    	 	AND mt.organization_code <> ''
    	 	AND mt.season = $2`,
    	[CLUB_ID, SEASON]
    ));

    const athleteRoles = yield getAthleteRoleFromDb(dbClient);

    let existingTeamCodes = (teamsData[0] && teamsData[0].team_codes) || {};

    let teamPattern = new RegExp(`^[g|b]([0-9])([0-9|a])${CLUB_CODE}([1-9][0-9]?)${CLUB_REGION}$`, 'i');

    let processedRows = yield (jsonSheetRows.reduce((prev, current, index) => {
    	return prev.then(sum => {
            const nonEmptyRows = current && current.filter(c => !!c);
    		return (
	    		(nonEmptyRows && (nonEmptyRows.length >= MIN_COLUMNS_COUNT) && current[0] !== FIRST_HEADER_NAME)
	    			?upsertRow(dbClient, existingTeamCodes, teamPattern, athleteRoles, current)
	    			:Promise.resolve(sum)
    		).then(modifiedRows => {
    			return (sum + modifiedRows);
    		}).catch(err => {
    			if(err.validation) {
    				err.validation += `; Line #${index + 1}`
    			}
    			throw err;
    		})
    	})
    }, Promise.resolve(0)));

    if(processedRows === 0) {
    	yield (runSQL(dbClient, 'ROLLBACK'));
    	throw {
    		validation: 'No changes has been made'
    	}
    }

    yield (
    	runSQL(
    		dbClient,
    		`UPDATE "master_staff_role"
			 SET "primary" = TRUE
			 WHERE "master_staff_role_id" IN (
			     SELECT STRING_AGG(msr.master_staff_role_id::TEXT, '')::INTEGER
			     FROM "master_staff" ms 
			     INNER JOIN "master_staff_role" msr 
			         ON msr.master_staff_id = ms.master_staff_id
			     WHERE ms.deleted IS NULL 
			         AND ms.season = $1
			         AND ms.master_club_id = $2
			     GROUP BY msr.master_staff_id  
			     HAVING COUNT(msr.*) = 1
			 )`,
            [SEASON, CLUB_ID]
    	)
    )

    yield (runSQL(dbClient, 'COMMIT'));
}).then(() => {
    process.exit(0);
}).catch(err => {
    console.error(!!err.validation?JSON.stringify(err):err);
    process.exit(err.statusCode || 1);
});

function upsertRow (client, existingTeams, pattern, athleteRoles, row) {
	console.log('Upsert row call', JSON.stringify(row, null, ' '));
	let masterTeamCode = row && row[0];

	if(!masterTeamCode) {
		return Promise.resolve(0);
	}

	masterTeamCode = masterTeamCode.trim().toLowerCase();

	if(!pattern.test(masterTeamCode)) {
        return Promise.reject({validation: 'Team has wrong club code or region'});
	}

	let masterTeamId 	= existingTeams[masterTeamCode],
		masterTeamName 	= row[1];

	return co(function* () {
		if(!masterTeamId) {
			if(!!masterTeamName) {
				console.log('Creating Team', `"${masterTeamName}"`)
				let teamDetails = masterTeamCode.match(pattern);
				masterTeamId = yield (createTeam(client, masterTeamCode, masterTeamName, teamDetails[1], teamDetails[2]));
				if(!masterTeamId) {
					throw {
						validation: 'Error on Team Creation'
					}
				}
				existingTeams[masterTeamCode] = masterTeamId;
			} else {
				throw {
					validation: 'Team Name Required'
				}
			}
		}

		let role 		= row[2] && row[2].toString().trim().toLowerCase(),
			first 		= row[3] && row[3].toString().trim(),
			last 		= row[4] && row[4].toString().trim(),
			gender 		= GENDERS[row[5] && row[5].toString().toLowerCase()],
			birthdate 	= moment.utc(row[6], PARSE_DATE_FORMAT),
			email 		= row[11] && row[11].toString().trim(),
			mobile 		= row[12] && row[12].toString().replace(/\D/g, ''),
			address 	= row[13],
			address2 	= row[14] || null,
			city 		= row[15],
			state 		= row[16],
			country= parseCountry(row[17]) || CLUB_COUNTRY_CODE,
			zip 		= row[18] || null,
            ethnicity   = row[19] && row[19].toString().toLowerCase() || null;

		if(!role)                           { throw { validation: 'Empty Member Role' } }
        if(!first)                          { throw { validation: 'First Name required' } }
        if(!last)                           { throw { validation: 'Last Name required' } }
        if(!gender)                         { throw { validation: 'Invalid Gender Value' } }
        if(email && !EMAIL_PATTERN.test(email))
                                            { throw { validation: 'Invalid Email' } }
        if(mobile && !PHONE_PATTERN.test(mobile))
                                            { throw { validation: 'Invalid Mobile Phone Number' } }

        // if(!address)                        { throw { validation: 'Address Required' } }
        // if(!city)                           { throw { validation: 'City Required' } }
        // if(!state)                          { throw { validation: 'State Required' } }
        // if(!country)                        { throw { validation: 'Country Required' } }

        let modified;
console.log('birthdate', birthdate, birthdate.isValid())
        const ATHLETE_ROLE_NAMES = athleteRoles.map(r => r.name);
        if(ATHLETE_ROLE_NAMES.includes(role)) {
        	if(!birthdate.isValid()) {
        		throw { validation: 'Invalid Birthdate' }
        	}

        	let uniform    		= row[7] || null,
	        	position   		= convertPosition(row[8]),
	        	height     		= heightToInches(row[9]),
	        	gradyear   		= parseInt(row[10], 10),
	        	age 	   		= getMinAge(birthdate.toDate(), SEASON),
	        	birthdateStr 	= birthdate.format(DB_DATE_FORMAT),
                athlete_role            = getAthleteRole(athleteRoles, role);

	        if(!gradyear) {
	        	throw {
	        		validation: 'Gradyear required for Player'
	        	}
			}

			if (!Number.isInteger(Number(uniform))) {
				throw { validation: 'Invalid Uniform' };
			}
            console.log(`WITH "check_gender" AS (
                        SELECT 'wrong_gender'::TEXT "action", -1 "id"
                        FROM "master_team" mt
                        WHERE mt.master_team_id = $20
                            /* Need to duplicate gender param ($3 & $21) because pg cannot cast the parameter twice */
                            AND NOT (mt.gender = $21::TEAM_GENDER OR mt.gender = 'coed')
                    ), "team" AS (
                        SELECT mt.master_team_id "id"
                        FROM "master_team" mt 
                        WHERE mt.master_team_id = $20
                            AND (
                                ($17::INTEGER <= mt.age OR mt.age = 0)
                                OR (
                                    ($17::INTEGER = 19 or $17::INTEGER = 20)
                                    /* gradyear = season */
                                    AND $16::TEXT = $19::TEXT
                                    AND $17::INTEGER = 18
                                )
                            )
                    ), "s" AS (
						SELECT s.state FROM "state" s
						WHERE (
							LOWER(s.state) = LOWER(TRIM($10)) 
							OR LOWER(s.name) = LOWER(TRIM($10))
						)
                    ), "existing_athlete" AS (
                        SELECT 'existing_athlete'::TEXT "action", -1 "id"
                        FROM "master_athlete" ma
						WHERE LOWER(ma.first) = LOWER(TRIM($1::TEXT))
                            AND LOWER(ma.last) = LOWER(TRIM($2::TEXT))
						    AND ma.master_club_id = ($18)::INTEGER
						    AND ma.season = ($19)::INTEGER
						    AND ma.birthdate = $4
						    AND ma.deleted IS NULL
						    AND (ma.usav_number IS NOT NULL OR ma.aau_membership_id IS NOT NULL)
                    ), "update_existing" AS (
						UPDATE "master_athlete" 
							SET "gender" 	= $3, 
								"birthdate" = $4, 
								"email" 	= $5, 
								"phonem" 	= $6, 
								"address" 	= $7,
								"address2" 	= $8,
								"city" 		= $9, 
								"state" 	= (SELECT "state" FROM "s"), 
								"zip" 		= $11, 
								"country" 	= $12, 
								"jersey" 	= $13,
								"sport_position_id" = $14,
								"height" 	= $15,
								"gradyear" 	= ($16)::INTEGER,
								"age" 		= $17,
								"season" 	= ($19)::INTEGER,
								"deleted" 	= NULL,
								"role"      = $22,
								"ethnicity" = $23,
								"master_team_id" 	= (SELECT "id" FROM "team")
						WHERE LOWER("first") = LOWER(TRIM($1::TEXT))
							AND LOWER("last") = LOWER(TRIM($2::TEXT))
							AND "usav_number" IS NULL
							AND "organization_code" IS NULL
                            AND "aau_membership_id" IS NULL
                            AND "master_club_id" = ($18)::INTEGER
                            AND "season" = ($19)::INTEGER
                            AND EXISTS (SELECT * FROM "team")
                            AND NOT EXISTS (SELECT * FROM "check_gender")
                            AND NOT EXISTS (SELECT * FROM "existing_athlete")
						RETURNING 'update'::TEXT "action", "master_athlete_id" "id"
					), "insert_new" AS (
						INSERT INTO "master_athlete" (
							"first", "last", "gender", "birthdate", "email", "phonem", "address",
							"address2", "city", "state", "zip", "country", "jersey", "sport_position_id",
							"height", "gradyear", "age", "master_club_id", "season", "role", "ethnicity", 
							"master_team_id"
						)
						SELECT 
							INITCAP($1), INITCAP($2), $3, $4, $5, $6, $7,
							$8, $9, (SELECT "state" FROM "s"), $11, $12, $13, $14, 
							$15, ($16)::INTEGER, $17, $18, ($19)::INTEGER, $22, $23, (SELECT "id" FROM "team")
						WHERE NOT EXISTS (SELECT * FROM "update_existing")
                            AND EXISTS (SELECT * FROM "team")
                            AND NOT EXISTS (SELECT * FROM "check_gender")
                            AND NOT EXISTS (SELECT * FROM "existing_athlete")
						RETURNING 'create'::TEXT "action", "master_athlete_id" "id"
					)
					SELECT * FROM "check_gender" UNION ALL
					SELECT * FROM "existing_athlete" UNION ALL
                    SELECT * FROM "update_existing" UNION ALL 
                    SELECT * FROM "insert_new"`, [
                first, last, gender, birthdateStr, email, mobile, address,
                address2, city, state, zip, country, uniform, position,
                height, gradyear, age, CLUB_ID, SEASON, masterTeamId, gender,
                athlete_role, ethnicity
            ]);
	        modified = yield (
	        	runSQL(
	        		client,
                    `WITH "check_gender" AS (
                        SELECT 'wrong_gender'::TEXT "action", -1 "id"
                        FROM "master_team" mt
                        WHERE mt.master_team_id = $20
                            /* Need to duplicate gender param ($3 & $21) because pg cannot cast the parameter twice */
                            AND NOT (mt.gender = $21::TEAM_GENDER OR mt.gender = 'coed')
                    ), "team" AS (
                        SELECT mt.master_team_id "id"
                        FROM "master_team" mt 
                        WHERE mt.master_team_id = $20
                            AND (
                                ($17::INTEGER <= mt.age OR mt.age = 0)
                                OR (
                                    ($17::INTEGER = 19 or $17::INTEGER = 20)
                                    /* gradyear = season */
                                    AND $16::TEXT = $19::TEXT
                                    AND $17::INTEGER = 18
                                )
                            )
                    ), "s" AS (
						SELECT s.state FROM "state" s
						WHERE (
							LOWER(s.state) = LOWER(TRIM($10)) 
							OR LOWER(s.name) = LOWER(TRIM($10))
						)
                    ), "existing_athlete" AS (
                        SELECT 'existing_athlete'::TEXT "action", -1 "id"
                        FROM "master_athlete" ma
						WHERE LOWER(ma.first) = LOWER(TRIM($1::TEXT))
                            AND LOWER(ma.last) = LOWER(TRIM($2::TEXT))
						    AND ma.master_club_id = ($18)::INTEGER
						    AND ma.season = ($19)::INTEGER
						    AND ma.birthdate = $4
						    AND ma.deleted IS NULL
						    AND (ma.usav_number IS NOT NULL OR ma.aau_membership_id IS NOT NULL)
                    ), "update_existing" AS (
						UPDATE "master_athlete" 
							SET "gender" 	= $3, 
								"birthdate" = $4, 
								"email" 	= $5, 
								"phonem" 	= $6, 
								"address" 	= $7,
								"address2" 	= $8,
								"city" 		= $9, 
								"state" 	= (SELECT "state" FROM "s"), 
								"zip" 		= $11, 
								"country" 	= $12, 
								"jersey" 	= $13,
								"sport_position_id" = $14,
								"height" 	= $15,
								"gradyear" 	= ($16)::INTEGER,
								"age" 		= $17,
								"season" 	= ($19)::INTEGER,
								"deleted" 	= NULL,
								"role"      = $22,
								"ethnicity" = $23,
								"master_team_id" 	= (SELECT "id" FROM "team")
						WHERE LOWER("first") = LOWER(TRIM($1::TEXT))
							AND LOWER("last") = LOWER(TRIM($2::TEXT))
							AND "usav_number" IS NULL
							AND "organization_code" IS NULL
                            AND "aau_membership_id" IS NULL
                            AND "master_club_id" = ($18)::INTEGER
                            AND "season" = ($19)::INTEGER
                            AND EXISTS (SELECT * FROM "team")
                            AND NOT EXISTS (SELECT * FROM "check_gender")
                            AND NOT EXISTS (SELECT * FROM "existing_athlete")
						RETURNING 'update'::TEXT "action", "master_athlete_id" "id"
					), "insert_new" AS (
						INSERT INTO "master_athlete" (
							"first", "last", "gender", "birthdate", "email", "phonem", "address",
							"address2", "city", "state", "zip", "country", "jersey", "sport_position_id",
							"height", "gradyear", "age", "master_club_id", "season", "role", "ethnicity", 
							"master_team_id"
						)
						SELECT 
							INITCAP($1), INITCAP($2), $3, $4, $5, $6, $7,
							$8, $9, (SELECT "state" FROM "s"), $11, $12, $13, $14, 
							$15, ($16)::INTEGER, $17, $18, ($19)::INTEGER, $22, $23, (SELECT "id" FROM "team")
						WHERE NOT EXISTS (SELECT * FROM "update_existing")
                            AND EXISTS (SELECT * FROM "team")
                            AND NOT EXISTS (SELECT * FROM "check_gender")
                            AND NOT EXISTS (SELECT * FROM "existing_athlete")
						RETURNING 'create'::TEXT "action", "master_athlete_id" "id"
					)
					SELECT * FROM "check_gender" UNION ALL
					SELECT * FROM "existing_athlete" UNION ALL
                    SELECT * FROM "update_existing" UNION ALL 
                    SELECT * FROM "insert_new"`, [
						first, last, gender, birthdateStr, email, mobile, address,
						address2, city, state, zip, country, uniform, position,
						height, gradyear, age, CLUB_ID, SEASON, masterTeamId, gender,
                        athlete_role, ethnicity
					]
	        	).then(rows => {
                    console.log('rows', rows);
                    if(rows.length === 0) {
                        throw {
                            validation: `Athlete's age (${age}) does not match the Team's age`
                        }
                    }
                    let action = rows[0] && rows[0].action
                    if(action === 'wrong_gender') {
                        throw {
                            validation: `Athlete's gender (${gender}) does not match the Team's gender`
                        }
                    } else if(action === 'existing_athlete') {
                        throw {
                            validation: `Athlete ${first} ${last}, ${birthdateStr} already exist`
                        }
                    }
	        		console.log('Player', action)
	        		return rows.length;
	        	})
	        );
        } else {
        	let roleId 			= getStaffRole(row[2]),
        		isImpact 		= false,
        		bgScreening 	= 'foreign',
        		birthdateStr 	= birthdate.isValid()?birthdate.format(DB_DATE_FORMAT):null,
        		cert

        	if(!roleId) {
        		throw {
        			validation: `Invalid Staffer's Role`
        		}
        	}

        	if(row[8] && (row[8].toLowerCase().indexOf('impact') >= 0)) {
        		isImpact = true;
        		cert 	= 'IMPACT';
        	} else {
        		isImpact = false;
        		cert = null;
        	}

        	let masterStaffId = yield (
	        	runSQL(
	        		client,
					`WITH "s" AS (
						SELECT s.state FROM "state" s
						WHERE (
							LOWER(s.state) = LOWER(TRIM($10)) 
							OR LOWER(s.name) = LOWER(TRIM($10))
						)
                    ), "existing_staff" AS (
                        SELECT 'existing_staff'::TEXT "action", -1 "id"
                        FROM "master_staff" ms
                        WHERE LOWER(ms.first) = LOWER(TRIM($1::TEXT))
                          AND LOWER(ms.last) = LOWER(TRIM($2::TEXT))
                          AND ms.master_club_id = ($15)::INTEGER
                          AND ms.season = ($16)::INTEGER
                          AND ms.birthdate = $4
                          AND ms.deleted IS NULL
                          AND (ms.usav_number IS NOT NULL OR ms.aau_membership_id IS NOT NULL)
                    ), "update_existing" AS (
						UPDATE "master_staff" 
							SET "gender" 	= $3, 
								"birthdate" = $4, 
								"email" 	= $5, 
								"phone" 	= $6, 
								"address" 	= $7,
								"address2" 	= $8,
								"city" 		= $9, 
								"state" 	= (SELECT "state" FROM "s"), 
								"zip" 		= $11, 
								"is_impact" = $12,
								"bg_screening" = $13,
								"cert" 		= $14,
                                "season" 	= ($16)::INTEGER,
								"deleted"	= NULL
						WHERE LOWER("first") = LOWER(TRIM($1::TEXT))
							AND LOWER("last") = LOWER(TRIM($2::TEXT)) 
							AND "usav_number" IS NULL
							AND "organization_code" IS NULL
							AND "aau_membership_id" IS NULL
							AND "master_club_id" = ($15)::INTEGER
                            AND "season" = ($16)::INTEGER
                            AND NOT EXISTS (SELECT * FROM "existing_staff")
                            RETURNING 'update'::TEXT "action", "master_staff_id"
					), "insert_new" AS (
						INSERT INTO "master_staff" (
							"first", "last", "gender", "birthdate", "email", "phone", "address",
							"address2", "city", "state", "zip", "is_impact", "bg_screening",
							"cert", "master_club_id", "season"
						)
						SELECT 
							INITCAP($1), INITCAP($2), $3, $4, $5, $6, $7,
							$8, $9, (SELECT "state" FROM "s"), $11, $12, $13, 
							$14, $15, $16
						WHERE NOT EXISTS (SELECT * FROM "update_existing")
                          AND NOT EXISTS (SELECT * FROM "existing_staff")
                        RETURNING 'create'::TEXT "action", "master_staff_id"
					)
					SELECT * FROM "update_existing" UNION ALL
                    SELECT * FROM "existing_staff" UNION ALL
                    SELECT * FROM "insert_new"`, [
						first, last, gender, birthdateStr, email, mobile, address,
						address2, city, state, zip, isImpact, bgScreening,
						cert, CLUB_ID, SEASON
					]
	        ).then(rows => {
	        	modified = rows.length;
                const staffer = rows[0];
                const action = staffer && staffer.action;
                if(action === 'existing_staff') {
                    throw {
                        validation: `Staff ${first} ${last}, ${birthdateStr} already exist`
                    }
                }
	        	console.log('Staffer', staffer);
	        	return staffer && staffer.master_staff_id;
	        }));

	       	yield (
	       		runSQL(
	       			client,
	       			`WITH "update_role" AS (
	       				UPDATE "master_staff_role" 
	       					SET "role_id" = $3
	       				WHERE "master_staff_id" = $1 
	       					AND "master_team_id" = $2
	       				RETURNING 'update'::TEXT "action"
	       			 ), "insert_role" AS (
	       			 	INSERT INTO "master_staff_role" ("master_staff_id", "master_team_id", "role_id")
	       			 	SELECT $1, $2, $3
	       			 	WHERE NOT EXISTS (
							SELECT * FROM "update_role"
	       			 	)
	       			 	RETURNING 'insert'::TEXT "action"
	       			 )
	       		    SELECT * FROM "update_role" UNION ALL SELECT * FROM "insert_role"`,
	       			[masterStaffId, masterTeamId, roleId]
	       		).then(rows => {
	       			modified += rows.length;
	       		})
	       	);
        }

        return modified;
	})
}

function createTeam (client, teamCode, teamName, ageStr, rank) {
    let teamAge = parseInt('1' + ageStr, 10);

    if(!teamAge) {
    	return Promise.reject({validation: 'Invalid Team Age'})
    }

    let teamGender = GENDERS[teamCode[0]];

    if(CLUB_GENDERS.indexOf(teamCode[0]) === -1) {
        return Promise.reject({
            validation: `"${teamGender}" gender of the team "${teamName}" is not allowed by the Club settings`
        })
    }

    return runSQL(
    	client,
    	`INSERT INTO "master_team" (
			"organization_code", "team_name", "master_club_id", "club_owner_id", "sport_id",
			"sport_variation_id", "season", "gender", "age", "rank"
	     ) SELECT UPPER($1), $2, $3, $4, $5, $6, $7, $8, $9, $10
	     RETURNING "master_team_id" "id"`, [
	     	teamCode, teamName, CLUB_ID, OWNER_ID, VOLLEYBALL_ID, INDOOR_ID, SEASON,
	     	teamGender, teamAge, rank
	    ]
    ).then(rows => {
    	return rows[0] && rows[0].id
    })
}

function parseCountry (c = '') {
    if(!_.isString(c)) {
        throw { validation: 'Invalid Country' }
    }

    switch(c.toLowerCase()) {
        case 'me':
        case 'mexico':
            return 'ME';
        case 'us':
        case 'usa':
        case 'united states':
            return 'US';
        case 'pr':
        case 'puerto rico':
            return 'PR';
        case 'ca':
        case 'canada':
            return 'CA';
        case 'vi':
        case 'virgin islands':
            return 'VI';
        case 'as':
        case 'american samoa':
            return 'AS';
        case 'gu':
        case 'guam':
            return 'GU';
        case 'bs':
        case 'bahamas':
            return 'BS';
        case 'pl':
        case 'poland':
            return 'PL';
        default:
        	return null;
    }
}

function getMinAge (birthday, season) {
    var bday 		= (birthday instanceof Date)?birthday:new Date(birthday),
        year 		= season,
        usav_age 	= year - bday.getFullYear();
    if (new Date(year, bday.getMonth(), bday.getDate()) >= new Date(year, 7 - 1, 1) ) {
        usav_age--;
    }
    if (usav_age < 10) usav_age = 10;
    return usav_age;
}

function convertPosition (pos) {
	switch(pos) {
        case 'Setter':
        case 'S':
            return 6;
        case 'Outside/Right Side':
        case 'OH':
        case 'OH/RS':
        case 'Outside':
            return 5;
        case 'Middle Blocker/Hitter':
        case 'MH':
        case 'MB':
        case 'Middle':
            return 3;
        case 'Defensive Specialist':
        case 'DS':
            return 8;
        case 'Libero':
        case 'LB':
            return 7;
        case 'RS':
        case 'Right Side':
        case 'RS/OH':
            return 4;
        default:
            return null;
    }
}

function heightToInches (height) {
	// if height is number - just return it
    if (typeof height === 'number') return height;
    // if height is not defined or passed not string - nothing to do here
    if (!height || typeof height !== 'string') return 0;
    // parsing height as a string with two numbers
    var values = height.match(/(\d){1,2}/g),
        new_height = 0;

  	if (values) {
        if (values.length === 2) {
            if( parseInt(values[0]) > 48 )
                new_height = parseInt(values[0], 10);
            else
                new_height = parseInt(values[0], 10)*12 + parseInt(values[1], 10);
        } else if (values.length === 1 && parseInt(values[0], 10) < 8) {
            new_height = parseInt(values[0], 10)*12;
        } else if (values.length === 1 && parseInt(values[0], 10) > 48) {
            new_height = parseInt(values[0], 10);
        }
    }

  	return new_height;
}

function getAthleteRole (athleteRoles, role) {
    const roleValue = role.trim().toLowerCase();

    const [{ id: roleID }] = athleteRoles.filter(r => r.name === roleValue);

    if(roleID !== 0 && !roleID)  {
        throw { validation: 'Invalid Member Role' };
    }

    return roleID;
}

async function getAthleteRoleFromDb (client) {
    let query = `SELECT athlete_role_id AS id, name FROM athlete_role`;

    const { rows: roles } = await client.query(query);

    return roles;
}

function getStaffRole (role) {
	switch(role.toLowerCase()) {
        case 'club director':
        case 'cd':
            return 2;
        case 'asst director':
        case 'ad':
            return 3;
        case 'head coach':
        case 'hc':
            return 4;
        case 'asst coach':
        case 'assistant coach':
        case 'ac':
            return 5;
        case 'team representative':
        case 'tr':
            return 6;
        case 'recruiting coordinator':
        case 'rc':
            return 11;
        case 'chaperone':
        case 'ch':
        case 'c':
            return 15;
        case 'manager':
        case 'm':
            return 7;
        default:
        	return null;
    }
}

function runSQL (client, text, params) {
    return new Promise((resolve, reject) => {
        client.query(text, params, function (err, result) {
            if(err) {
                err.sql = text;
                err.params = params;
                reject(err);
            } else {
                resolve(result.rows);
            }
        })
    })
}
