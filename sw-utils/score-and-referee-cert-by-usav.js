'use strict';

const argv          = require('optimist').argv
const co            = require('co');
const request       = require('request-promise');
const { Client }    = require('pg');
const XML           = require('nodexml')

const SW_WP_USERNAME    = 'SportWrench';
const SW_WP_PSWD        = 'xm1@usavWebp0int';

const DB_CONN_STR = argv.db_conn;
const TOTAL_MEMBERS_LIMIT = Number(argv.limit) || 100;
const LIMIT = 25;

const REFCERTNAME   = 'REFCERTNAME';
const REFENDDATE    = 'REFENDDATE';
const SCORECERTNAME = 'SCORECERTNAME';
const SCOREENDDATE  = 'SCOREENDDATE';

const CERT_PROPS = [REFCERTNAME, REFENDDATE, SCORECERTNAME, SCOREENDDATE];

function findMembers (client, offset = 0) {
    const FIND_MEMBERS_QUERY = 
        `SELECT 
             DISTINCT ON (d."usav_code")
             d."usav_code",
             d."id"
         FROM (
             SELECT 
                 DISTINCT ON (ma."organization_code")
                 ma."master_athlete_id" "id", 
                 ma."organization_code" "usav_code",
                 ma."created"
             FROM "master_athlete" ma 
             WHERE ma."season" = 2017
                 AND ma."deleted" IS NULL 

             UNION ALL  

             SELECT 
                 DISTINCT ON (ms."organization_code")
                 ms."master_staff_id" id, 
                 ms."organization_code" "usav_code",
                 ms."created"
             FROM "master_staff" ms 
             WHERE ms."season" = 2017
                 AND ms."deleted" IS NULL 
                 
             ORDER BY 3 DESC
         ) "d"
        LIMIT ${LIMIT}
        OFFSET ${offset}`;

    return new Promise((resolve, reject) => {
        client.query(FIND_MEMBERS_QUERY, null, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve(res.rows);
            }
        })
    })
}

function getMemberWPData (member) {
    const CODE_PROP         = 'Code';
    const ERROR_MSG_PROP    = 'Msg';

    const WP_SUCCESS_CODE   = 0;

    let { usav_code: usavCode } = member;

    let memberID = (usavCode.match(/[0-9]{7,7}/) || [])[0];

    if (!memberID) {
        return Promise.reject(new Error('Could not extract member ID'));
    }

    return request({
        method  : 'POST',
        uri     : 'https://webpoint.usavolleyball.org/wp15/Contacts/MbrVerify.wp',
        /* 'Content-Type'  : 'application/x-www-form-urlencoded' is set automatically */
        form    : {
            Username            : SW_WP_USERNAME,
            Passcode            : SW_WP_PSWD,
            MemberID            : memberID
        }
    })
    .then(respBody => XML.xml2obj(respBody))
    .then(respJSON => {      
  
        let { RESPONSE: wpResp } = respJSON;

        if (wpResp && Number(wpResp[CODE_PROP]) !== WP_SUCCESS_CODE) {
            return Promise.reject(new Error(wpResp[ERROR_MSG_PROP]))
        }


        let { MEMBER_DATA: { RESPONSE: memberResp, MEMBER: member} } = respJSON;

        if (memberResp && Number(memberResp[CODE_PROP]) !== WP_SUCCESS_CODE) {
            return Promise.reject(new Error(memberResp[ERROR_MSG_PROP]))
        }

        if (!member) {
            return Promise.reject(new Error('Member not found'));
        }

        return member;
    })
}

co(function* () {
    const client = new Client(DB_CONN_STR);

    client.connect();

    let result = {};

    for (let prop  of CERT_PROPS) {
        result[prop] = new Set();
    }

    let offset = 0;
    let totalMembersQty = 0;

    while (true) {
        
        let members = yield (findMembers(client, offset));

        if (members.length === 0 || (totalMembersQty >= TOTAL_MEMBERS_LIMIT)) {
            break;
        }

        totalMembersQty += members.length;

        for (let member of members) {

            console.log('Processing member #' + member.id);

            try {
                let webpointMemberData = yield (getMemberWPData(member));

                for (let prop  of CERT_PROPS) {
                    if (webpointMemberData[prop]) {
                        result[prop].add(webpointMemberData[prop]);
                        console.log('   ', `${prop}.size =`, result[prop].size);
                    }
                }
            } catch (err) {
                console.error('Error:', err.message);
            }

            console.log();
        }

        offset += LIMIT;
    }

    client.end();

    return result;
})
.then(result => {

    console.log('Finished!');
    console.log('Results:');

    for (let prop  of CERT_PROPS) {

        console.log(prop);

        /* jshint loopfunc:true */
        result[prop].forEach(value => console.log('   ', value));
    }

    process.exit(0);
})
.catch(err => {
    console.error(err);
    process.exit(1);
});
