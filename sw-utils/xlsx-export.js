
'use strict';

const 
	argv 	= require('optimist').argv,
	xlsx 	= require('xlsx'),
	pg      = require('pg'),
	co 		= require('co'),
	Logger 	= require('./log'),
    moment  = require('moment'),
    _       = require('lodash'),
    log 	= new Logger();

const
    DB_CONNECTION_STR 	= argv.connection,
    OUTPUT_FILEPATH 	= argv.path,
    SQL  	            = argv.query,
    TYPE                = argv.type;

let sheetName = '';

co(function* () {
	let connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));

	let sql = Buffer.from(SQL, 'base64').toString('utf-8');

	log.debug('Connecting to the DB');
	let dbClient = new pg.Client(connectionParams);
	dbClient.connect();

	let rows = yield (new Promise((resolve, reject) => {
		dbClient.query(sql, (err, result) => {
			if(err) {
				reject(err);
			} else {
				resolve(result.rows);
			}
		})
	}));

	log.debug('Closing db connection.');
	// no need to hold the connection any more
	dbClient.end();

	log.debug('Got data from DB.', rows.length, 'rows');

	if(!rows.length) {
        process.exit(2);
	}

    if(['officials', 'staffers'].includes(TYPE)) {
        rows = __formatData(rows);
        sheetName = _.capitalize(TYPE);

    } else if(TYPE === 'teams') {
        sheetName = 'Teams';
    } else if(TYPE === 'custom_form') {
        sheetName = 'Custom Form';
        rows = __formatCustomReportData(rows);
    }

    let headerCells = Object.keys(rows[0]),
        sheet 		= {};

    let workbook = {
        SheetNames 	: [sheetName],
        Sheets 		: {}
    };

	headerCells.forEach(function writeHeader (title, index) {
		sheet[xlsx.utils.encode_cell({ c: index, r: 0 })] = { v: title, t: 's' };
	});

	rows.forEach(function (row, index) {
		let sheetLine = (index + 1);

		headerCells.forEach(function (columnName, columnIndex) {
			sheet[xlsx.utils.encode_cell({ c: columnIndex, r: sheetLine })] = { v: row[columnName] || '', t: 's' };
		});
	});

	sheet['!ref'] = xlsx.utils.encode_range({
		s: { c: 0, r: 0 }, 
		e: { c: headerCells.length, r: (rows.length + 1) }
	});

	workbook.Sheets[sheetName] = sheet;
	// writing option used in xlsx is 'w' 
	// 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
	xlsx.writeFile(workbook, OUTPUT_FILEPATH,  { font: { name: 'Verdana' }, bookSST: true });
	log.debug('WorkBook written')
}).catch(err => {
	console.error(err);
	process.exit(1)
});

function __formatData (rows) {
    rows.forEach(row => {
        if (row['Schedule availability']) {
            let daysAvailability = Object.keys(row['Schedule availability']).sort();

            for (let i = 0; i < row.event_days_count; ++i) {
                row[`Day ${i + 1} Avail`] = ( row['Schedule availability'][daysAvailability[i]] === 1 )? 'Y' : 'N'
            }
        } else {
            for (let i = 0; i < row.event_days_count; ++i) {
                row[`Day ${i + 1} Avail`] = null;
            }
        }

        if (row['Hotel Nights Required'] && row['Need hotel room'] === 'Y') {
            _.each(row['Hotel Nights Required'].dates, function (value, day) {
                row[`Night of ${moment(day, 'MMDDYYY').format('ddd, MMM DD', 'en')}`] = value?'Y':'N';
            })
        } else if (row['Hotel dates']) {
            _.each(row['Hotel dates'].dates, function (value, day) {
                row[`Night of ${moment(day, 'MMDDYYY').format('ddd, MMM DD', 'en')}`] = '';
            })
        }

        delete row.event_days_count;
        delete row['Schedule availability'];
        delete row['Hotel Nights Required'];
        delete row['Hotel dates'];
    });

    return rows;
}

function __formatCustomReportData (rows) {
    rows = _.orderBy(rows, ['row', (item) => {
        return item.fields.length;
    }], ['desc', 'desc']);

    rows.forEach(d => {
        d.fields.forEach(f => {
            if(!_.isEmpty(f.options)) {
                f.value = f.options.reduce((result, option) => {

                    if(_.isString(f.value) && f.type === 'multiselect') {
                        f.value = JSON.parse(f.value);
                    }

                    if(f.type === 'select' && option.id === f.value) {
                        result.push(option.label);
                    }

                    if((f.value || []).includes(option.id) && f.type === 'multiselect') {
                        result.push(option.label);
                    }
                    return result;
                }, []).join(', ');
            }

            d[f.label] = f.value;
        })

        delete d.fields;
    });

    return rows;
}
