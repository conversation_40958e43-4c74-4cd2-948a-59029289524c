'use strict';

const assert 	= require('assert');
const _ 		= require('lodash');
const pg 		= require('pg');
const swUtils  	= require('../api/lib/swUtils');

const DB_CONN = process.argv[2];
const SEASON  = Number(process.argv[3]);

let minYear   = new Date().getUTCFullYear() - 1;


assert(_.isString(DB_CONN), 'Connection should be a string');
assert(_.isNumber(SEASON), 'Season should be a number');
assert(SEASON >= minYear, `Season should be >= ${minYear}`);

const db        = new pg.Client({ connectionString: DB_CONN });

function findMasterTeams (db, season, offset = 0, limit = 200) {
	return db.query(
		`SELECT mt."team_name", mt."master_team_id", mt."master_club_id"
		 FROM "master_team" mt 
		 WHERE mt."season" = $1
		 	AND mt."deleted" IS NULL
		 LIMIT ${limit} OFFSET ${offset}`,
		[season]
	).then(res => res.rows);
}

function findRosterTeamsRows (db, season, offset = 0, limit = 200) {
	return db.query(
		`SELECT rt."team_name", rt."roster_team_id", rt."event_id", rt."roster_club_id"
		 FROM "roster_team" rt 
		 INNER JOIN "master_team" mt 
		 	ON mt."master_team_id" = rt."master_team_id"
		 	AND mt."season" = $1
		 INNER JOIN "event" e 
		 	ON e."event_id" = rt."event_id"
		 WHERE rt."deleted" IS NULL
		 LIMIT ${limit} OFFSET ${offset}`,
		 [season]
	).then(res => res.rows)
}

function cutCharsFromTeams (teams) {
	return teams.reduce((res, team) => {
		let modifiedName = swUtils.escapeStr(team.team_name);

		if (modifiedName !== team.team_name) {
			console.log(
				'  	', 
				`"${team.team_name}" (${team.team_name.length})`,
				'->', 
				`"${modifiedName}" (${modifiedName.length})`
			);	

			team.team_name = modifiedName;

			res.push(team);
		}

		return res;
	}, [])
}

function getTotalUpdQty (updRows) {
	return updRows.reduce((sum, current) => {

		if (_.isNumber(current)) {
			sum += current;
		}

		return sum;
	}, 0)
}

function updateMasterTeamRows (db, rows) {
	return Promise.all(
		rows.map(team => {
			return db.query(
				`UPDATE "master_team" mt 
				 SET "team_name" = $1
				 WHERE mt."master_team_id" = $2 
				 	AND mt."master_club_id" = $3`,
				[team.team_name, team.master_team_id, team.master_club_id]
			).then(res => res.rowCount)
		})
	).then(getTotalUpdQty)
}

function updateRosterTeamRows (db, rows) {
	return Promise.all(
		rows.map(team => {
			return db.query(
				`UPDATE "roster_team" rt
				 SET "team_name" = $1 
				 WHERE rt."roster_team_id" = $2 
				 	AND rt."roster_club_id" = $3 
				 	AND rt."event_id" = $4`,
				[team.team_name, team.roster_team_id, team.roster_club_id, team.event_id]
			).then(res => res.rowCount)
		})
	).then(getTotalUpdQty)
}

function processTeamsRows (findFn, updFn, db, season, offset = 0, limit = 200) {
	return findFn(db, season, offset, limit)
	.then(rows => {
		let keepOnIteration = false;

		if (rows.length === 0) {
			return keepOnIteration;
		} else {
			keepOnIteration = true;

			let modifiedRows = cutCharsFromTeams(rows);

			console.log('Got', modifiedRows.length, 'rows for modification of', rows.length, 'overall');

			if (modifiedRows.length > 0) {
				return updFn(db, modifiedRows)
				.then(updRowsQty => {
					console.log(' 	Updated', updRowsQty, 'rows of', modifiedRows.length);
					
					return keepOnIteration;
				})
			} else {
				return  keepOnIteration;
			}
		}
	}).then(keepOn => {
		if (keepOn) {
			offset += limit;
			return processTeamsRows(findFn, updFn, db, season, offset);
		}
	})
}

/* ===  MAIN === */

db.connect()
.then(() => db.query('BEGIN')
.then(() => processTeamsRows(findMasterTeams, updateMasterTeamRows, db, SEASON).then(() => { 
	console.log('"master_team" rows processing finished');
	console.log();
}))
.then(() => processTeamsRows(findRosterTeamsRows, updateRosterTeamRows, db, SEASON).then(() => {
	console.log('"roster_team" rows processing finished');
	console.log();
}))
.then(() => db.query('COMMIT').then(() => {
	console.log('Transaction is commited');
}))
.then(() => db.end().then(() => {
	console.log('Disconnected from DB Server');
}))
.then(() => {
	console.log('Exiting');
	process.exit(0);
})
.catch(err => {
	console.log('here');
	console.error(err);
	process.exit(1);
})

/*
* создаем транзакцию
* Получаем строки по 200 штук
* вырезаем имена, сохраняем
* То же для roster team
*/


/*

select count(*) from "master_team" where "deleted" IS NULL and "season" = 2017
select count(*) from "roster_team" where "deleted" IS NULL AND "master_team_id" IN (
    select master_team_id from "master_team" where "deleted" IS NULL and "season" = 2017
)

*/
