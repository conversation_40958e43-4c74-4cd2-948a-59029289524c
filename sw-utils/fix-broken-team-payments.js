/**
 * This script fixes broken team_purchase records 
 * where team_purchase.surcharge is moved to team_purchase.amount and set to NULL after partial refund.
 * 
 * Link issue: https://itrdev.atlassian.net/browse/SW-1760
 */

const pg   = require('pg');
const knex = require('knex')({client: 'pg'});
const _    = require('lodash');
const swUtils  	= require('../api/lib/swUtils');

const DB_CONNECTION_STRING  = process.argv[2];
const EVENT_ID = process.argv[3];

let BROKEN_RECORDS = [];

let Db = null;

if (!DB_CONNECTION_STRING) {
    error('Missed DB Connection String');
}

//DB Connection
(async () => {
    try {
        Db = new pg.Client(DB_CONNECTION_STRING);

        await Db.connect();
    } catch(e) {
        error(e);
    }
})();

// Entry point
(async () => {
    try {
        await Db.query('BEGIN');

        BROKEN_RECORDS = await findTeamPurchaseRecords();

        if (!BROKEN_RECORDS.length) {
            error('Records not found');
        }

        console.log('\x1b[36m', `Found ${BROKEN_RECORDS.length} records`);

        const fixedRecords = fixRecords(BROKEN_RECORDS);

        await Promise.all(fixedRecords.map(updatePurchaseTeam));

        console.log('\x1b[32m', `\n\nPurchase Teams records successfully fixed:`);

        await Db.query('COMMIT');
    } catch (e) {
        error(e)
    } finally {
        process.exit();
    }
})();

function findTeamPurchaseRecords() {
    const query = knex('purchase_team AS pt')
        .select(
            'pt.purchase_team_id',
            knex.raw('COALESCE(d.credit_surcharge, e.credit_surcharge) AS "credit_surcharge"'),
            'pt.amount',
            'pt.surcharge'
        )
        .join('purchase AS p', 'p.purchase_id', 'pt.purchase_id')
        .join('event AS e', 'e.event_id', 'pt.event_id')
        .join('roster_team AS rt', 'rt.roster_team_id', 'pt.roster_team_id')
        .join('division AS d', 'd.event_id', 'e.event_id')
        .whereRaw('(rt.discount + pt.amount) <> COALESCE(pt.division_fee, pt.event_fee)')
        .where('pt.surcharge', null)
        .where('e.season', 2020)
        .where('p.status', 'paid')
        .whereRaw('(rt.discount + pt.amount) > 0')
        .whereRaw(
            '((rt.discount + pt.amount) - COALESCE(pt.division_fee, pt.event_fee)) = ' +
            'COALESCE(d.credit_surcharge, e.credit_surcharge, 0)'
        );

    if (EVENT_ID) {
        query.where('e.event_id', EVENT_ID);
    }

    return Db.query(`${query}`).then(({ rows }) => rows || []);
}

function updatePurchaseTeam({ amount, purchase_team_id, surcharge }) {
    const query = knex('purchase_team')
        .update({
            amount,
            surcharge
        })
        .where('purchase_team_id', purchase_team_id)
        .returning(['purchase_team_id', 'amount', 'surcharge']);

    return Db.query(`${query}`).then(logUpdateResult);
}

function logUpdateResult({ rows : [row]}) {
    const brokenRow = findRecord(row.purchase_team_id);

    console.log('\x1b[36m', `\nFix purchase_team_id: ${row.purchase_team_id}:`);
    console.log('\x1b[36m', ` * set amount from ${brokenRow.amount} to ${row.amount}, surcharge from ${brokenRow.surcharge} to ${row.surcharge}`);
}

function fixRecords(rows) {
    const _rows = JSON.parse(JSON.stringify(rows));

    return _rows.map(fixRecord);
}

function fixRecord(row) {
    row.amount =  swUtils.normalizeNumber(row.amount - row.credit_surcharge);
    row.surcharge = row.credit_surcharge;

    return _.pick(row, ['amount', 'purchase_team_id', 'surcharge']);
}

function findRecord(purchaseTeamId) {
    return BROKEN_RECORDS.find(({ purchase_team_id }) => purchase_team_id === purchaseTeamId);
}

function error(message) {
    console.error('\x1b[31m', message);

    process.exit();
}
