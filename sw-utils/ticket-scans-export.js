'use strict';

const { workerData, parentPort } = require('worker_threads');
const xlsx = require('xlsx');
const db = require('../api/lib/db.js');
const path = require('path');
global._ = require('lodash');
global.TicketsService = require('../api/services/TicketsService');
const TicketsStatisticsService = require('../api/services/TicketsStatisticsService');
const {
    DEFAULT_WRITE_OPTIONS
} = require('./export-helper');

const parseJSONBuffer = (buffer) => {
    return JSON.parse(Buffer.from(buffer, 'base64').toString('utf-8'));
};

const args = parseJSONBuffer(workerData)

const CONNECTION_CONFIG = args.connection;
const EVENT_ID = args.eventId;
const IS_DETAILED_SCANS = args.isDetailedScans;


async function exportTicketScans() {
    try {
        const connectionParams = _.isObject(CONNECTION_CONFIG)
        ? _.omit(CONNECTION_CONFIG, 'module')
        : { connectionString: CONNECTION_CONFIG };
        
        global.Db = new db(connectionParams, {}, { error: console.error });
        
        let data;
        
        if (IS_DETAILED_SCANS) {
            data = await TicketsStatisticsService.getDetailedUniqueScans(EVENT_ID);
        } else {
            data = await TicketsStatisticsService.getUniqueScans(EVENT_ID);
        }
        
        const filePath = createSheetFile(data);
        
        parentPort.postMessage(filePath);
    } finally {
        await Db.end();
    }
}

function createSheetFile(data) {
    const tables = data.filter(tableRows => !_.isEmpty(tableRows));

    const workbook = createWorkBook();
    const sheet = createWorkSheet(workbook);

    tables.reduce((lastCellOrigin, tableRows) => {
        const sheetRows = []

        // get the table headers from first json in rows
        const firstRow = tableRows[0];
        const columnNames = Object.keys(firstRow);

        const rowValues = tableRows.map(Object.values);

        sheetRows.push(columnNames, ...rowValues);

        // cell origin is calculated to position multiple tables separated by 1 cell
        const nextCellOrigin = lastCellOrigin === 0 ? lastCellOrigin : lastCellOrigin + 1;

        xlsx.utils.sheet_add_aoa(sheet, sheetRows, { origin: { r: 0, c: nextCellOrigin } });

        return nextCellOrigin + columnNames.length;
    }, 0);

    return saveWorkBook(workbook);
}

function createWorkSheet(workbook) {
    const sheetName = 'Scans statistics';

    const sheet = xlsx.utils.aoa_to_sheet([]);
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);

    return sheet;
}

function createWorkBook() {
    return xlsx.utils.book_new();
}

function saveWorkBook(workbook) {
    const filePath = getFilePath();

    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);

    return filePath;
}

function getFilePath() {
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const fileID = Date.now();
    const filePath = path.resolve(exportDirectoryPath, `${EVENT_ID}_scans_statistics_${fileID}.xlsx`);

    return filePath;
}

exportTicketScans()
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
