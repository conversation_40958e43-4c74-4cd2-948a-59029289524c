'use strict';

const { workerData, parentPort } = require('worker_threads');
const xlsx = require('xlsx');
const db = require('../api/lib/db.js');
const path = require('path');
global._ = require('lodash');
const AdminDisputeService = require('../api/services/AdminDisputeService.js');

const {
    DEFAULT_WRITE_OPTIONS
} = require('./export-helper.js');

const parseJSONBuffer = (buffer) => {
    return JSON.parse(Buffer.from(buffer, 'base64').toString('utf-8'));
};

const args = parseJSONBuffer(workerData)

const CONNECTION_CONFIG = args.connection;
const DISPUTE_FILTERS = args.filters;

function formatDispute(dispute) {
    const data = _.pick(dispute, [
        'event_name',
        'purchase_id',
        'reason',
        'dispute_created',
        'evidence_status',
        'amount',
    ])

    return {
        ...data,
        dispute_email: dispute.emails[0]?.created || ''
    }

}
async function exportDisputeList() {
    try {
        const connectionParams = _.isObject(CONNECTION_CONFIG)
            ? _.omit(CONNECTION_CONFIG, 'module')
            : { connectionString: CONNECTION_CONFIG };

        global.Db = new db(connectionParams, {}, { error: console.error });

        const data = await AdminDisputeService.getDisputeList(DISPUTE_FILTERS, true);

        const filePath = createSheetFile(data);

        parentPort.postMessage(filePath);
    } finally {
        await Db.end();
    }
}

function createSheetFile(data) {
    const tableRows = data.filter(tableRows => !_.isEmpty(tableRows)).map(formatDispute);

    const workbook = createWorkBook();
    const sheet = createWorkSheet(workbook);

    const firstRow = tableRows[0];
    const columnNames = Object.keys(firstRow);

    const rowValues = tableRows.map(Object.values);

    const sheetRows = [columnNames, ...rowValues];

    xlsx.utils.sheet_add_aoa(sheet, sheetRows);

    return saveWorkBook(workbook);
}

function createWorkSheet(workbook) {
    const sheetName = 'Dispute list';

    const sheet = xlsx.utils.aoa_to_sheet([]);
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);

    return sheet;
}

function createWorkBook() {
    return xlsx.utils.book_new();
}

function saveWorkBook(workbook) {
    const filePath = getFilePath();

    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);

    return filePath;
}

function getFilePath() {
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const fileID = Date.now();
    const filePath = path.resolve(exportDirectoryPath, `dispute_list_${fileID}.xlsx`);

    return filePath;
}

exportDisputeList()
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
