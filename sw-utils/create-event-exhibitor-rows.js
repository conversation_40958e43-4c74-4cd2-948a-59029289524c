const pg                = require('pg');
const knex              = require('knex')({client: 'pg'});

const DB_CONNECTION_STRING  = process.argv[2];
let EVENT_IDS               = process.argv[3];

let Db = null;
const DEFAULT_EVENT_EXHIBITOR_DATA  = {
    status: 'approved',
    booths: '{}',
    comment: '',
}

if (!DB_CONNECTION_STRING) {
    error('Missed DB Connection String');
}

if (!EVENT_IDS) {
    error('Missed Event ids');
}

EVENT_IDS = JSON.parse(EVENT_IDS);

if (!EVENT_IDS.length) {
    error('Event Ids is empty');
}

//DB Connection
(async () => {
    try {
        Db = new pg.Client(DB_CONNECTION_STRING);

        await Db.connect();
    } catch(e) {
        error(e);
    }
})();

// Entry point
(async () => {
    try {
        await Db.query('BEGIN');

        await setExhibitorSettingsForEvents();
        const exhibitors = await getExhibitors();

        if (!exhibitors.length) {
            throw 'Exhibitors Not Found';
        }

        const exhibitorsIds         = exhibitors.map(({ sponsor_id }) => sponsor_id);
        const exhibitorsVendorData  = await getExhibitorsProfilesVendorData(exhibitorsIds);
        const eventDates            = await getEventDates();
       
        const insertData = generateEventExhibitorInsertRow({
            exhibitors,
            exhibitorsVendorData,
            eventDates,
        });

        const createdRows = await createExhibitorsRows(insertData);

        console.log('\x1b[32m', `event_exhibitor rows successfuly created:`);
        console.log(createdRows.map(row => row.event_exhibitor_id));

        await Db.query('COMMIT');
    } catch (e) {
        error(e)
    } finally {
        process.exit();
    }
})();

function setExhibitorSettingsForEvents() {
    const query = knex('event')
        .whereIn('event_id', EVENT_IDS)
        .update({
            has_exhibitors: true,
            enable_exhibitors_reg: true,
            exhibitors_sw_fee: 0,
            stripe_exhibitors_private_key: knex.raw('stripe_teams_private_key')
        })

    return Db.query(`${query}`)
        .then(({ rows }) => rows);
}

function getExhibitors() {
    const query = knex.select(
        {
            sponsor_ids: knex.raw(`COALESCE(array_to_json(array_agg(data)), '[]'::JSON)`)
        },
    )
    .from(knex.raw(`
    (
        SELECT 
            p.sponsor_id, 
            p.event_id
        FROM purchase AS p
        WHERE p.event_id = ANY(?)
        AND p.payment_for = 'booths'
    ) AS data`, [EVENT_IDS]))

    return Db.query(`${query}`)
        .then(({ rows: [{ sponsor_ids }] } ) => sponsor_ids || [])
}

function getExhibitorsProfilesVendorData(ids) {
    const query = knex.select(
        'is_exhibitor',
        'is_sponsor',
        'is_non_profit',
        'is_other',
        'sponsor_id',
    )
    .from('sponsor')
    .whereIn('sponsor_id', ids);

    return Db.query(`${query}`)
        .then(({ rows }) => rows || []);
}

function getEventDates() {
    const query = knex.select(
        { dates: knex.raw(`json_object_agg(d.date, FALSE)`)},
        'd.event_id'
    )
    .from(knex.raw(`(
        SELECT
            generate_series(e.date_start, e.date_end, '1 day'::interval) AS date, 
            e.event_id
        FROM event AS e
        WHERE event_id = ANY(?)
    ) AS d`, [EVENT_IDS]))
    .groupBy('d.event_id');

    return Db.query(`${query}`)
        .then(({ rows }) => rows || []);
}

function generateEventExhibitorInsertRow({ exhibitorsVendorData, exhibitors, eventDates }) {
    const _data = [];

    exhibitors.forEach(exh => {
        const exhibitorVendorData = exhibitorsVendorData.find(data => data.sponsor_id === exh.sponsor_id);
        const exhibitorEventDates = JSON.stringify(eventDates.find(date => date.event_id === exh.event_id).dates);

        _data.push(Object.assign(
            {},
            DEFAULT_EVENT_EXHIBITOR_DATA,
            exhibitorVendorData,
            { event_id: exh.event_id },
            { event_dates: exhibitorEventDates },
        ))
    });

    return _data;
}

function createExhibitorsRows(data) {
    const query = knex('event_exhibitor')
        .insert(data)
        .returning('event_exhibitor_id');

    return Db.query(`${query}`)
        .then(({ rows }) => rows);
}

function error(message) {
    console.error('\x1b[31m', message);

    process.exit();
}