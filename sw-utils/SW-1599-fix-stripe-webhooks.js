const pg = require('pg');
const fetch = require('node-fetch');

const DB_CONN = process.argv[2];

if(!DB_CONN) {
    throw new Error('No Connection String passed!');
}

const db = new pg.Client(DB_CONN);

( async () => {
    await db.connect();
    const connected = await db.query(`select * from stripe_account where is_platform is false`).then(r=>r.rows);
    for(const account of connected) {
        const headers = new fetch.Headers();
        headers.set('Authorization', `Basic ${Buffer.from(`${account.secret_key}:`, 'utf-8').toString('base64')}`);
        try {
            const r = await fetch('https://api.stripe.com/v1/webhook_endpoints', { headers })
                .then(r => r.json())
                .then(
                    r => r.data.filter(
                        e => (
                            e.status === 'enabled'
                            && e.url.includes('sportwrench.com/api/stripe/webhook')
                        )
                    )
                );
            for(const endpoint of r) {
                // https://stripe.com/docs/api/webhook_endpoints/update?lang=curl
                console.log(`curl https://api.stripe.com/v1/webhook_endpoints/${endpoint.id} -u ${account.secret_key}: -d "disabled=true"`);
            }
        }
        catch(err) {}
    }
    await db.end();
})()
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
