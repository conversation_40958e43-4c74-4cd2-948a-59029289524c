'use strict';

const assert 					= require('assert');
const _ 						= require('lodash');
const pg 						= require('pg');
const SWTQRGenerator 			= require('../api/lib/QRTicketsGenerator');
const knox 						= require('knox');
const qrPNGGenerator          	= require('qr-image');
const argv 	                    = require('optimist').argv;

const DATE_REG_EXP 				= /^\d{4}-\d{2}-\d{2}$/;
const DEV_ENV 					= 'development';
const PROD_ENV 					= 'production';
const CHECKIN_CONTENT_PREFIX 	= 'SWTm';
const S3_IMAGES_PATH 			= '/images/qrcode/';
const TICKETS_BARCODE_TYPE      = 'tickets';
const CHECKIN_BARCODE_TYPE      = 'checkin';

const DB_CONN 					= argv.connection;
const DATE 						= argv.date;
const EVENT                     = argv.event;
const BARCODE_TYPE              = argv.type;
const ENV 						= argv.prod ? PROD_ENV : DEV_ENV;
const help                      = argv.help || argv.h;


if(help) {
    console.info();
    console.info(`Usage: node generate-missing-qr-codes <param>:<value>`);
    console.info();
    console.info(`Params:`);
    console.info(`   --connection   `, `DB connection [string]`, `(required)`);
    console.info(`   --date         `, `QR codes date [YYYY-MM-DD]`, `(required if event not set)`);
    console.info(`   --event        `, `Event ID [number]`, `(required if date not set)`);
    console.info(`   --type         `, `QR code type [tickets/checkin]`, `(optional)`);
    console.info(`   --prod         `, `Production environment flag`, `(optional)`);
    console.info();
    console.info(`   --help(--h)    `, `Help flag`);
    console.info();

    process.exit(0);
}


assert(_.isString(DB_CONN), 'Connection should be a string');

if(typeof EVENT === 'undefined' && typeof DATE !== 'undefined') {
    assert(DATE_REG_EXP.test(DATE), 'Date should have format YYYY-MM-DD');
} else if(typeof DATE === 'undefined' && typeof EVENT !== 'undefined') {
    assert(!isNaN(EVENT), 'Event ID should be a number');
} else {
    throw new Error('You need to pass DATE or EVENT ID filter');
}

assert([PROD_ENV, DEV_ENV].includes(ENV), `Invalid node environment "${ENV}"`);
assert(typeof BARCODE_TYPE === 'undefined' || [TICKETS_BARCODE_TYPE, CHECKIN_BARCODE_TYPE].includes(BARCODE_TYPE));

const db            = new pg.Client({ connectionString: DB_CONN });

const S3_CONFIG 	=  (ENV === DEV_ENV) 
							? require('../config/s3').s3.ticketsBucketDev
							: require('../config/s3').s3.ticketsBucket;

const S3Client 		= knox.createClient(S3_CONFIG);

let getPayments = () => {
	return db.query(
		`SELECT
			ticket_row."purchase_id", ticket_row."event_id", ticket_row."ticket_barcode",
			COALESCE(ticket_row."user_id", payment_row."user_id") "user_id",
			(EXTRACT(EPOCH FROM NOW()))::INT "purchase_timestamp", ( 
		        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
		        FROM ( 
		            SELECT   
		                et.event_ticket_id,  
		                et.sort_order
		            FROM event_ticket et 
		            WHERE et.event_id = e.event_id 
		            ORDER BY et.event_ticket_id ASC 
		        ) "t" 
		    ) "event_tickets", (
				SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
				FROM (
					SELECT 
						pt."quantity",
						et."sort_order" "order"
					FROM "purchase_ticket" pt 
					INNER JOIN "event_ticket" et 
						ON et."event_ticket_id" = pt."event_ticket_id"
						AND et."event_id" = ticket_row."event_id"
					WHERE pt."purchase_id" = ticket_row."purchase_id"
						AND pt."canceled" IS NULL
				) "t"
		    ) "purchase_tickets"
		 FROM "purchase" ticket_row
		 INNER JOIN "event" e 
		 	ON e."event_id" = ticket_row."event_id"
		 INNER JOIN "purchase" payment_row
		    ON payment_row."is_payment" IS TRUE
		    AND (
		        ticket_row."linked_purchase_id" = payment_row."purchase_id"
		        OR
		        ticket_row."purchase_id" = payment_row."purchase_id"
		    )
		    AND payment_row.type IS NOT NULL
		 WHERE
		    ticket_row."is_ticket" IS TRUE
		    ${DATE  ? 'AND ticket_row."created"::DATE = $1::DATE' : ''}
		    ${EVENT ? 'AND ticket_row."event_id" = $1::INTEGER'   : ''} 
		 	AND ticket_row."payment_for" = 'tickets'
		 	AND ticket_row."status" IN ('pending', 'paid')
		`,
        [DATE || EVENT]
	).then(res => res.rows)
}

let getCheckIns = () => {
	return db.query(
		`SELECT 
			ms."master_staff_id", 
			ms."checkin_barcode"
		 FROM "event_team_checkin" etch 
		 INNER JOIN "master_staff" ms 
		 	ON ms."master_staff_id" = etch."master_staff_id"
		 	AND ms."checkin_barcode" IS NOT NULL
		 WHERE
		    ${DATE  ? 'etch."created"::DATE = $1::DATE' : ''}
		    ${EVENT ? 'etch."event_id" = $1::INTEGER'   : ''}`,
        [DATE || EVENT]
	).then(res => res.rows)
}

let generateSWTImageName = data => {
	let applyFormatting = true;
	let imageName       = SWTQRGenerator.generateHash(data, applyFormatting);
	return `${imageName}.png`;
}

let generateCheckInImageName = data => {
	return `${CHECKIN_CONTENT_PREFIX}${data.checkin_barcode}.png`
}

let checkImageExists = imageName => {
	return new Promise(resolve => {

		let S3FilePath = `${S3_IMAGES_PATH}${imageName}`;

		S3Client.head(S3FilePath).on('response', res => {
			let fileExists = (res.statusCode === 200);

			resolve(fileExists);
		}).end();

	})
}

let generateSWTContent = data => {
	let ticketsQRList = 
			SWTQRGenerator.generateQRCodeItemsString(data.event_tickets, data.purchase_tickets);

	return SWTQRGenerator.generateContent({
		tickets 				: ticketsQRList,
		event_id 				: data.event_id, 
		ticket_barcode 			: data.ticket_barcode,
		purchase_timestamp 		: data.purchase_timestamp
	})
}

let generateCheckInContent = imageName => {
	return imageName;
}

let uploadImageToS3 = (imageName, content) => {
	return new Promise((resolve, reject) => {
		console.log('uploading image', imageName);

		let pngBuf = qrPNGGenerator.imageSync(content, {
            type    : 'png',
            size    : 6,
            margin  : 1
        });

		let req = S3Client.put(`${S3_IMAGES_PATH}${imageName}`, {
            'Content-Length'    : pngBuf.length,
            'Content-Type'      : 'image/png'
        });

        req.on('response', resp => {
        	if (resp.statusCode === 200) {
        		resolve()
        	} else {
        		reject(resp.data || resp.error);
        	}
        });

        req.on('error', err => {
        	reject(err);
        });

        req.end(pngBuf);
	})
}

let getQRCodesData = function () {
    return BARCODE_TYPE === CHECKIN_BARCODE_TYPE
        ? [getCheckIns()]
        : BARCODE_TYPE === TICKETS_BARCODE_TYPE
            ? [getPayments()]
            : [getPayments(), getCheckIns()];
};



db.connect()
.then(() => Promise.all(getQRCodesData()))
.then(res => {
	return db.end()
	.then(() => res[0].concat(res[1] || []))
})
.then(items => {

	return items.reduce((prev, item) => {
		return prev.then(createdQty => {
			let imageName = item.purchase_id 
							? generateSWTImageName(item) 
							: generateCheckInImageName(item);

			return checkImageExists(imageName)
			.then(imageExists => {
				if (!imageExists) {
					let qrCodeContent = item.purchase_id 
						? generateSWTContent(item)
						: generateCheckInContent(imageName);

					return uploadImageToS3(imageName, qrCodeContent)
					.then(() => {
						return (createdQty + 1);
					})
				} else {
					return createdQty
				}
			})
		})
		
	}, Promise.resolve(0))
	.then(createdQty => {
		console.log('Total rows:', items.length, 'Uploaded:', createdQty)
	})
})
.then(() => {
	process.exit(0)
})
.catch(err => {
	console.error(err);
	process.exit(1)
})

