#!/usr/bin/env node
const inquirer = require('inquirer');
const SWApiClient = require('./common/SWApiClient');

const BATCH_SIZE = 500;

(async () => {
    const server = await getServer();
    const apiClient = new SWApiClient(server);
    await signinApiClient(apiClient);
    const event = await selectEvent(apiClient);
    const db = await createDbClient();
    if(!await confirmRefunding(event)) {
        return;
    }
    await refundTeams(db, apiClient, event);
    await refundTickets(db, apiClient, event);
    await refundBooths(db, apiClient, event);

    console.log('All payments refunded');
})()
    .then(() => process.exit(0))
    .catch((err) => {
        if(err.toJSON) {
            err = err.toJSON();
        }
        console.error(err);
        process.exit(1);
    });

async function getServer() {
    const {baseURL, Authorization} = await inquirer.prompt([
        {
            name: 'baseURL',
            type: 'string',
            message: 'API base URL'
        },
        {
            name: 'Authorization',
            type: 'string',
            message: 'Authorization header for api, leave empty if none'
        }
    ])

    return {
        baseURL,
        headers: {
            Authorization,
        },
    }
}

async function signinApiClient(apiClient) {
    while(true) {
        const {
            email,
            password,
        } = await inquirer.prompt([
            {
                name: 'email',
                type: 'string',
                default: '<EMAIL>',
            },
            {
                name: 'password',
                type: 'password',
            },
        ]);
        if(await apiClient.signin(email, password)) {
            break;
        }
        console.log('Invalid email or password');
    }
}

async function selectEvent(apiClient) {
    let event;
    await inquirer.prompt([
        {
            name: 'eventId',
            type: 'number',
            async validate(eventId) {
                if(!Number.isInteger(eventId)) {
                    return false;
                }
                event = await apiClient.getEventInfo(eventId);
                return true;
            }
        },
    ]);
    return event;
}

async function createDbClient() {
    const { connection } = await inquirer.prompt([
        {
            name: 'connection',
            type: 'string',
            message: 'DB connection string',
        },
    ]);
    return require('knex')({
        client: 'pg',
        connection,
        searchPath: ['public'],
    });
}

async function confirmRefunding(event) {
    const { confirm } = await inquirer.prompt([
        {
            name: 'confirm',
            type: 'confirm',
            message: `Refund all payments on ${event.long_name}?`,
            default: false,
        }
    ]);
    return confirm;
}

async function refundTeams(db, apiClient, event) {
    while(true) {
        const teamPayments = await findPurchases(db, event.event_id, 'teams', BATCH_SIZE);
        for(const payment of teamPayments) {
            console.log(`Teams purchase ${payment.purchase_id}`);
            await apiClient.refundTeamsPurchase(event.event_id, payment.purchase_id);
        }
        if(teamPayments.length < BATCH_SIZE) {
            break;
        }
    }
}

async function refundTickets(db, apiClient, event) {
    while(true) {
        const ticketPayments = await findPurchases(db, event.event_id, 'tickets', BATCH_SIZE);
        for(const payment of ticketPayments) {
            console.log(`Tickets purchase ${payment.purchase_id} (${payment.ticket_barcode})`);
            await apiClient.refundTicket(event.event_id, payment.ticket_barcode);
        }
        if(ticketPayments.length < BATCH_SIZE) {
            break;
        }
    }
}

async function refundBooths(db, apiClient, event) {
    while(true) {
        const boothPayments = await findPurchases(db, event.event_id, 'booths', BATCH_SIZE);
        for(const payment of boothPayments) {
            console.log(`Booths purchase ${payment.purchase_id}`);
            await apiClient.refundBoothsPurchase(event.event_id, payment.purchase_id);
        }
        if(boothPayments.length < BATCH_SIZE) {
            break;
        }
    }
}


async function findPurchases(db, event_id, type, limit) {
    const columns = [
        'p.purchase_id',
        'p.type',
        'p.amount',
        'p.amount_refunded',
        'p.first',
        'p.last',
        'p.email',
    ].filter(v => v);
    const query = db('purchase as p')
        .select(columns)
        .where('p.event_id', event_id)
        .whereRaw('p.dispute_created IS NULL')
        .where('p.type', 'card')
        .where('p.status', 'paid')
        .where('p.payment_for', type)
        .orderBy('p.purchase_id', 'desc')
        .limit(limit);
    if(type === 'tickets') {
        query.whereRaw('p.is_ticket')
            .leftJoin('purchase as p_pay', 'p.linked_purchase_id', 'p_pay.purchase_id')
            .whereRaw('p_pay.dispute_created IS NULL')
            .select('p.ticket_barcode')
    }
    return query;
}

function mapChoices(items) {
    return Object.keys(items)
        .map( key => ({
        name: key,
        value: items[key],
    }));
}
