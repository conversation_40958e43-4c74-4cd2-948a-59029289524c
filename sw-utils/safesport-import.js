
const
    SSImport        	= require('../api/lib/SafeSportService'),
    argv 	            = require('optimist').argv,
    co                  = require('co'),
    _                   = require('lodash'),
    dbLib               = require('../api/lib/db');

const
    DB_CONNECTION_STR 	= argv.connection,
    SEASON 	            = argv.season,
    LIMIT               = argv.limit,
    EVENT               = argv.event;

let hrstart     = process.hrtime(),
    limitBlock  = (LIMIT)?`LIMIT ${LIMIT}`:'',
    dbClient,
    staffList,
    db,
    errList = [],
    SafeSportImport;

try {
    global.Db = db = new dbLib(JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8')));
    dbClient               = global.Db;
    SafeSportImport        = new SSImport(db);
} catch (err) {
    console.error(err);
    process.exit(1);
}

co(function* () {
    yield (db.clientConnect());

    if (parseInt(EVENT)) {
        staffList = yield(getMemberListForEvent());
    } else {
        staffList = yield (getMemberList());
    }

    if (staffList.length === 0) {
        return { updatedCount: 0, time: process.hrtime(hrstart)[0] + 's', staffList: 0 };
    }
    let updatedCount = yield (
        staffList.reduce((prev, staff) => {
            return prev.then(qty => {
                return SafeSportImport.processStaff(staff)
                .then(result => {
                    if(result.err) {
                        let errStaffData = {
                            staff_usav: staff.usav_number,
                            staff_name: staff.first + ' ' + staff.last,
                            club_name: staff.club_name,
                            club_director_name: staff.director_first + ' ' + staff.director_last
                        };
                        result.err.staff = errStaffData;
                        errList.push(_.pick(result.err, 'data', 'validation', 'staff'));
                    }

                    let {rowCount} = result;
                    return (qty + rowCount);
                })
            })
        }, Promise.resolve(0))
    );

    yield (disconnect(db));

    let time = process.hrtime(hrstart)[0] + 's';

    return { updatedCount, time, staffList: staffList.length, errorsList: errList};
})
.then(res => {
    process.send(res);
    process.exit(0);
})
.catch(err => {
    console.error(err);
    return disconnect(db)
    .then(() => {
        process.exit(1);
    })
});

function disconnect (db) {
    return db.clientDisconnect()
    .then(() => {})
    .catch(console.error.bind(console))
}

function getMemberList() {
    // getting rosters members for the events with event.date.start > NOW()
    return new Promise((resolve, reject) => {
        dbClient.query(
            `SELECT DISTINCT ON(w.usav_number) w.*
             FROM 
             (SELECT vrm.*, mc.club_name, mc.director_first, mc.director_last
              FROM v_roster_member vrm JOIN master_club mc ON vrm.master_club_id = mc.master_club_id
              WHERE NOW() < date_start AND usav_number IS NOT NULL AND webpoint_sync IS NULL
              ORDER BY event_id
             ) w 
             ${limitBlock}`,
            (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result.rows);
                }
            }
        )
    })
}

function getMemberListForEvent() {
    // getting rosters members for particular event
    // LIMIT applied

    return new Promise((resolve, reject) => {
        dbClient.query(
            `SELECT vrm.*, mc.club_name, mc.director_first, mc.director_last
             FROM v_roster_member vrm JOIN master_club mc ON vrm.master_club_id = mc.master_club_id
             WHERE usav_number IS NOT NULL AND webpoint_sync IS NULL
                   AND event_id = $1
             ${limitBlock}`, [EVENT],
            (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result.rows);
                }
            }
        )
    })
}

function getStaffList () {
    return new Promise((resolve, reject) => {
        dbClient.query(
            `SELECT ms.master_staff_id, mc.club_name, 
                    mc.director_first, mc.director_last,
                    mc.webpoint_username, mc.webpoint_password,
                    ms.organization_code, ms.usav_number, 
                    ms.master_club_id, ms.first, ms.last
            FROM "master_staff" ms
            INNER JOIN "master_club" mc
                ON mc.master_club_id = ms.master_club_id
            WHERE ms.deleted IS NULL
                AND ms.season = ${SEASON}
                AND ms.usav_number IS NOT NULL
                AND ms.webpoint_sync IS NULL
            ${limitBlock}`,
            (err, result) => {
                if(err) {
                    reject(err);
                } else {
                    resolve(result.rows);
                }
            }
        )
    })
}

function getStaffListForEvent () {
    return new Promise((resolve, reject) => {
        dbClient.query(
            `SELECT *
			 FROM (
			 	SELECT DISTINCT ON (ms.usav_number) 
			 	    rsr.roster_staff_role_id, ms.master_staff_id, ms.deleted,
			 	    mc.webpoint_username, mc.webpoint_password, ms.first, ms.last,
			 	    ms.organization_code, ms.usav_number, ms.master_club_id,
			 	    ms.webpoint_modified,mc.club_name, 
                    mc.director_first, mc.director_last
			 	FROM "roster_staff_role" rsr
			 	INNER JOIN "master_staff" ms
			 	    ON ms.master_staff_id = rsr.master_staff_id
			 	INNER JOIN "roster_team" rt
			 	    ON rt.roster_team_id = rsr.roster_team_id
			 	    AND rt.deleted is null
			 	INNER JOIN "master_club" mc
			 	    ON mc.master_club_id = ms.master_club_id
			 	WHERE rt.event_id = $1
			 	    AND rsr.deleted is null
			 	    AND ms.deleted is null
			 ) "list"
			 ORDER BY list.webpoint_modified DESC`,
            [EVENT],
            (err, result) => {
                if(err) {
                    reject(err);
                } else {
                    resolve(result.rows);
                }
            }
        )
    })
}
