'use strict';

const {argv} = require('optimist');
const xlsx = require('xlsx');
const db = require('../api/lib/db.js');
const path = require('path');
global._ = require('lodash');
global.TicketsService = require('../api/services/TicketsService');
const TicketsStatisticsService = require('../api/services/TicketsStatisticsService');
const {
    parseParameterObject,
    showError,
    DEFAULT_WRITE_OPTIONS,
} = require('./export-helper');


const $CONN_STR = argv.connection;
const $EVENT_ID = parseInt(argv.event, 10);
const $DATE_FROM = argv['date-from'];
const $DATE_TO = argv['date-to'];


async function run() {
    try {
        const connectionConfig = await parseParameterObject($CONN_STR);

        const connectionParams = _.isObject(connectionConfig)
        ? _.omit(connectionConfig, 'module')
        : {connectionString: connectionConfig};
        global.Db = new db(connectionParams, {}, {error: console.error});

        const data = await TicketsStatisticsService.getStatistics($EVENT_ID, $DATE_FROM, $DATE_TO);
        const sheetData = prepareSheetData(data);
        const filePath = createFile(sheetData);
        process.stdout.write(filePath);
    } finally {
        await Db.end();
    }
}

function prepareSheetData(data) {
    const prefixes = new Map([
        ['subtotals:balance', '(CRB)'],
        ['cash:amount', '(CN)'],
        ['cash:sw', '(CF)'],
        ['net:balance', '(CRB) - (CF) = (N)'],
        ['event_total_net_profit:balance', '(N) + (CN) ='],
    ]);
    const columns = getColumns(data);
    const rows = getRows();
    const sheetData = createEmptySheet(rows, columns);
    for(const [rowIndex, row] of rows.entries()) {
        for(const [columnIndex, column] of columns.entries()) {
            const cellKey = `${row.key}:${column.key}`;
            sheetData[rowIndex + 1][columnIndex + 1] = column.getValue(row.getRow(data), row);
            const prefix = prefixes.get(cellKey);
            if(prefix) {
                sheetData[rowIndex + 1][columnIndex + 1] = `${prefix} ${sheetData[rowIndex + 1][columnIndex + 1]}`;
            }
        }
    }

    return sheetData;
}

function getColumns(data) {
    const ticketsColumns = getTicketsColumns(data);
    const valueGetter = function getValue(row) {
        return row[this.key];
    };
    const moneyValueGetter = function getValue(row) {
        try {
            return `\$${row[this.key].toFixed(2)}`;
        } catch(err) {
            return undefined;
        }
    };
    const columns = [
        {
            key: 'tr',
            title: 'Transactions',
            getValue: valueGetter,
        },
        ...ticketsColumns,
        {
            key: 'amount',
            title: 'Amount',
            getValue: moneyValueGetter,
        },
        {
            key: 'stripe',
            title: 'Stripe Fee',
            getValue: moneyValueGetter,
        },
        {
            key: 'sw',
            title: 'SW',
            getValue: moneyValueGetter,
        },
        {
            key: 'balance',
            title: 'Balance',
            getValue: moneyValueGetter,
        },
    ];

    return columns;
}

function getTicketsColumns(data) {
    return data.ticket_types.map(
        (ticketType, index) => ( {
            title: ticketType.label,
            getValue(row) {
                try {
                    return `${row.types[index].total} (${row.types[index].not_scanned})`;
                } catch(err) {
                    return undefined;
                }
            },
        } )
    )
}

function getRows() {
    const rowGetter = function getRow(data) {
        return data[this.key];
    };
    const rows = [
        {
            key: 'prior',
            title: 'Online Prior',
            getRow: rowGetter,
        },
        {
            key: 'during',
            title: 'Online During',
            getRow: rowGetter,
        },
        {
            key: 'api',
            title: 'Event Credit',
            getRow: rowGetter,
        },
        {
            key: 'subtotals',
            title: 'Credit subtotal',
            getRow: rowGetter,
        },
        {
            key: 'cash',
            title: 'Event Cash',
            getRow: rowGetter,
        },
        {
            key: 'totals',
            title: 'Credit + Cash Total',
            getRow: rowGetter,
        },
        {
            key: 'net',
            title: 'Credit Card Net Profit (Amount SW sends to Stripe)',
            getRow(data) {
                return {
                    balance: data.net,
                };
            }
        },
        {
            key: 'event_total_net_profit',
            title: 'Event Total Net Profit (includes on-site cash)',
            getRow(data) {
                return {
                    balance: data.event_total_net_profit,
                };
            }
        },
    ];

    return rows;
}

function createEmptySheet(rows, columns) {
    const sheetData = Array.from(
        { length: rows.length + 1 },
        () => Array.from(
            { length: columns.length + 1 }
        )
    );
    for(const [rowIndex, row] of rows.entries()) {
        sheetData[rowIndex + 1][0] = row.title;
    }
    for(const [columnIndex, column] of columns.entries()) {
        sheetData[0][columnIndex + 1] = column.title;
    }

    return sheetData;
}

function createFile(sheetData) {
    const filePath = getFilePath();
    const sheetName = 'Purchase statistics';
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetData);
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);
    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);

    return filePath;
}

function getFilePath() {
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const fileID = Date.now();
    const filePath = path.resolve(exportDirectoryPath, `${$EVENT_ID}_purchase_statistics_${fileID}.xlsx`);

    return filePath;
}

run()
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
