'use strict';

/* jshint node:true */
/* jshint esversion:6 */
const defaultColumnModifier = ({name, type}) => ({
    name,
    type,
    expression: `"${name}"`,
});

class SQLqueryGenerator {
	constructor (remoteDBConnectionStr) {
		this._remoteDBConnectionStr = remoteDBConnectionStr;
	}

    __genQueryBlocks__ (columns, {excludedColumns, columnModifier = defaultColumnModifier} = {}) {
		const updTableAlias = '"t"';

        let selectionBlock = [];
        let remoteSelectionBlock = [];
        let remoteColumnsDescription = [];
        let setBlock = [];
        let pkComparisonBlock = [];
        let pkReturningBlock = [];
        let updReturningBlock = [];

		for (let column of columns) {

            if(excludedColumns && excludedColumns.includes(column.name)) {
                continue;
            }

            let columnName = `"${column.name}"`;
            const remoteColumn = columnModifier(column);

            selectionBlock.push(columnName);
            remoteSelectionBlock.push(remoteColumn.expression);
            remoteColumnsDescription.push(`"${remoteColumn.name}" ${remoteColumn.type}`);

			if (!column.is_primary) {
				setBlock.push(`${columnName} = "d".${columnName}`);
			} else {
				pkComparisonBlock.push(`${updTableAlias}.${columnName} = "d".${columnName}`);
				pkReturningBlock.push(columnName);
			}
		}	

		if (pkComparisonBlock.length > 1) {
			throw new Error(`Multiple primary keys not allowed (${pkComparisonBlock.join(', ')})`)
		}

		selectionBlock = selectionBlock.join(', ');
        remoteSelectionBlock = remoteSelectionBlock.join(', ');
        remoteColumnsDescription = remoteColumnsDescription.join(', ');
        setBlock = setBlock.join(', ');
        pkComparisonBlock = pkComparisonBlock.join(' AND ');
        updReturningBlock = pkReturningBlock.map(col => `${updTableAlias}.${col}`).join(', ');
        pkReturningBlock = pkReturningBlock.join(', ');

		return {
			selectionBlock, remoteSelectionBlock, remoteColumnsDescription,
			setBlock, pkComparisonBlock, 
			pkReturningBlock, updReturningBlock,
			updTableAlias
		}
	}

	__genSelectFromRemoteBlock__ (tableName, fieldsList, remoteColumnsDescription, eventID, where) {
		where = (!!where)
			?where.replace(/\$[1]/g, eventID)
			:`"event_id" = (${eventID})::INTEGER`

		return (
			`SELECT *
			 FROM DBLINK(
			 	'${this._remoteDBConnectionStr}',
			 	${this._prepareString(`SELECT ${fieldsList} FROM "${tableName}" WHERE ${where}`)}
			 ) AS "test_table" (${remoteColumnsDescription})`
		);
	}

	_prepareString(string) {
	    return `'${string.replace(/'/g,`''`)}'`;
    }

	generateTableColumnsSQLQuery (tableName) {
		/**
		* "pg_attribute": https://postgrespro.ru/docs/postgrespro/9.5/catalog-pg-attribute.html
		* "pg_index": https://postgrespro.ru/docs/postgrespro/9.5/catalog-pg-index.html
		*/
		return (
			`SELECT 
				table_column.name,
				UPPER(FORMAT_TYPE(attrl.atttypid, attrl.atttypmod)) "type",
				table_column.is_primary
			 FROM DBLINK(
				'${this._remoteDBConnectionStr}',
				'SELECT 
				    attr."attname" "name", 
				    UPPER(FORMAT_TYPE(attr.atttypid, attr.atttypmod)) "type",
				    COALESCE(ind."indisprimary", FALSE) "is_primary"
				FROM "pg_attribute" attr
				LEFT JOIN "pg_index" ind 
				    ON attr."attrelid" = ind."indrelid"
				    AND attr."attnum" = ANY(ind."indkey")
				    AND ind."indisprimary"
				WHERE attr."attrelid" = ''public.${tableName}''::REGCLASS
				    AND attr."attnum" > 0
				    AND NOT attr."attisdropped"'
			 ) AS "table_column" ("name" TEXT, "type" TEXT, "is_primary" BOOLEAN)
			 INNER JOIN "pg_attribute" attrl ON attrl."attname" = table_column."name"
			 WHERE attrl."attrelid" = 'public.${tableName}'::REGCLASS
                AND attrl."attnum" > 0
                AND NOT attrl."attisdropped"`
		);
	}

	// covered 😄👍
	generateUpsertSQLQuery (columns, tableData, eventID) {
		let tableName = tableData.name;
        const {excludedColumns, columnModifier} = tableData;

		let {
			selectionBlock, remoteSelectionBlock, remoteColumnsDescription, setBlock, pkComparisonBlock,
			pkReturningBlock, updReturningBlock, updTableAlias
		} = this.__genQueryBlocks__(columns, {excludedColumns, columnModifier});

		let queryBody = 
			`WITH "data" AS (
				${this.__genSelectFromRemoteBlock__(tableName, remoteSelectionBlock, remoteColumnsDescription, eventID, tableData.where)}
			), "upd" AS (
				UPDATE "${tableName}" ${updTableAlias}
				SET 
					${setBlock}
				FROM (
					SELECT * FROM "data"
				) "d"
				WHERE ${pkComparisonBlock}
				RETURNING ${updReturningBlock}
			), "ins" AS (
				INSERT INTO "${tableName}" (${selectionBlock})
				SELECT ${selectionBlock} FROM "data"
				WHERE ${pkReturningBlock} NOT IN (
					SELECT ${pkReturningBlock} FROM "upd"
				)	
				RETURNING ${pkReturningBlock}
			)
			SELECT COUNT(*) FROM "data" 
				UNION ALL 
			SELECT COUNT(*) FROM "upd" 
				UNION ALL 
			SELECT COUNT(*) FROM "ins"`;	

		return queryBody;
	}

	generateInsertSQLQuery (columns, tableData, eventID) {
        const {columnModifier} = tableData;
        let {
          selectionBlock,
            remoteSelectionBlock,
            remoteColumnsDescription,
        } = this.__genQueryBlocks__(columns, {columnModifier});
		let tableName 						= tableData.name;
		let selectFromRemoteBlock 			= 
						this.__genSelectFromRemoteBlock__(tableName, remoteSelectionBlock, remoteColumnsDescription, eventID);

		return (
			`INSERT INTO "${tableName}" (${selectionBlock})
			 ${selectFromRemoteBlock}`
		);
	}
    
    checkEventQuery(eventId) {
        return this.__genSelectFromRemoteBlock__('event', 'event_id', 'event_id name', eventId);
    }
}

module.exports = SQLqueryGenerator;
