const axios = require('axios');

class SWApiClient {
    constructor({baseURL, headers = {}}) {
        this._client = axios.create({
            baseURL,
            headers,
        });
    }

    async signin(email, password) {
        await this.getCSRFToken();

        const body = { email, password, remember_me: false };
        const response = await this._client.post(
            '/api/signin',
            body,
            {
                validateStatus: status => [200, 400].includes(status)
            }
        );

        return response.status === 200;
    }

    async refundTeamsPurchase(eventId, purchaseId) {
        await this._client.get(`/api/event/${eventId}/payment/${purchaseId}/refund`);
    }

    async refundTicket(eventId, ticketBarcode) {
        await this._client.post(`/api/event/${eventId}/ticket/${ticketBarcode}/refund`, {});
    }

    async refundBoothsPurchase(eventId, purchaseId) {
        await this._client.post(`/api/event/${eventId}/sales/payment/${purchaseId}/refund`, {})
    }

    async getEventInfo(eventId) {
        const response = await this._client.get(`/api/event/${eventId}`);
        return response.data.data.event;
    }

    async getCSRFToken () {
        const response = await this._client.get(`/api/csrfToken`);

        this._saveCookies(response);

        this._client.defaults.headers.common['X-CSRF-TOKEN'] = response.data._csrf;
    }

    _saveCookies(response) {
        const setCookieValues = response.headers['set-cookie'];
        if(!setCookieValues) {
            return;
        }
        const cookie = setCookieValues
            .map(setCookie => setCookie.split(';', 1)[0])
            .join('; ');
        this._client.defaults.headers.common.Cookie = cookie;
    }
}

module.exports = SWApiClient;
