angular.module('SportWrench')

.controller('MainController', MainController);

function MainController (
    $scope, $window, $state, userService, $rootScope, $location, $anchorScroll, $timeout, APP_ROUTES, SET_USER_NAME, HOME_PAGE_URL
) {
    var countriesWithStates     = ['US', 'CA'];
    $scope.userfirst            = userService.getName();
    $scope.breadCrumbs          = {};
    $scope.year                 = new Date().getUTCFullYear();
    $scope.opts                 = {};   
    $scope.routes               = {
        index   : APP_ROUTES.INDEX,
        reg     : APP_ROUTES.REG,
        login   : APP_ROUTES.LOGIN,
        faq     : APP_ROUTES.SUPPORT.FAQ,
        ask     : APP_ROUTES.SUPPORT.ASK,
        events  : APP_ROUTES.EO.EVENTS,
        club    : APP_ROUTES.CD.INFO,
        receipts: APP_ROUTES.EX.RECEIPTS,
        profile:  APP_ROUTES.EX.PROFILE,
        official: APP_ROUTES.OF.INFO,
        housing : APP_ROUTES.HOUSING_EVENTS,
        tickets : APP_ROUTES.TICKETS_LIST,
        terms   : APP_ROUTES.TERMS,
        privacy : APP_ROUTES.PRIVACY,
        es      : APP_ROUTES.ES.PARENT,
        ua      : APP_ROUTES.UA.EVENTS,
        sales   : APP_ROUTES.SM.EVENTS,
    };

    $rootScope.$on(SET_USER_NAME, (e, data) => {
        $scope.userfirst = data.name;
    });

    $scope.homePageUrl = HOME_PAGE_URL;

    $scope.closeMenu = function () {
        if(!this.opts.isCollapsed) this.opts.isCollapsed = true;
    }    

    $scope.scrollTo = function(id) {
        $location.url('/#' + id);
        $anchorScroll();
    }

    $scope.isMyEventsVisible = function() {
        return $scope.hasGodRole() || $scope.isSWOwner() || $scope.isEventOwner();
    }

    $scope.isLoggedIn = function() {
        return userService.isLoggedIn();
    }

    $scope.isEventOwner = function() {
        return userService.isEventOwner();
    }

    $scope.isClubDirector = function() {
        return userService.isClubDirector();
    }

    $scope.isSponsor = function  () {
        return userService.isSponsor();
    }

    $scope.isOfficial = function  () {
        return userService.isStaff();
    }

    $scope.isSales = function () {
        return userService.isSales();
    }

    $rootScope.isSWOwner = function () {
        return userService.isSWOwner();
    }

    $scope.isHousingManager = function () {
        return userService.isHousingManager();
    }

    $rootScope.isHousingManager = function () {
        return userService.isHousingManager();
    }

    $scope.showName = function() {
        $scope.userfirst = userService.getName();
    }
    
    $scope.logOut = function() {
        userService.logOut();
    }

    $scope.hasTickets = function () {
        return userService.hasTickets();
    }

    $scope.hasGodRole = function () {
        return userService.hasGodRole();
    }

    $scope.verifySeal = function () {
        $window.open(
            'https://seal.godaddy.com/verifySeal?sealID=WnkoAijHredA1jD6lvEKDnuZKmEpTQCp18VpJWF8W3hCnnBX7IFAGF',
            'SealVerification',
            'location=yes,status=yes,resizable=yes,scrollbars=no,width=592,height=433'
        )
    }

    $scope.isUSAVAdmin = function () {
        return userService.isUSAVAdmin();
    }

    var hideLogoPages = [APP_ROUTES.INDEX];
    $scope.logoVisibility = function () {
        for(var i = 0, l = hideLogoPages.length; i < l; ++i) {
            if(_.includes($state, hideLogoPages[i]))
                return 'visible-xs-inline';
        }
        return '';        
    }

    $rootScope.getControlClass = function (form, prop, isRequred) {
        return (('form-group' + ((isRequred)?' validation-required':'')) + ((form[prop] && form[prop].$invalid && form.$submitted)?' has-error':''));
    };

    $rootScope.countryHasState = function (c) {
        return (countriesWithStates.indexOf(c) >= 0);
    };
}
