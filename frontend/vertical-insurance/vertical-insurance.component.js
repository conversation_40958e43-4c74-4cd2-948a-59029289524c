
class Controller {
    constructor(verticalInsuranceService, purchaseService, $stateParams, $scope, $sce) {
        this.verticalInsuranceService = verticalInsuranceService;
        this.purchaseService = purchaseService;
        this.eventID = $stateParams.event;
        this.$scope = $scope;
        this.$sce = $sce;
    }

    $onInit() {
        this.insuranceDecision = {
            option: false
        };

        this.quote = {};
        this.updatingQuoteData = false;
        this.quoteDataLoaded = false;
        this.inlineNoticeTitle = 'Insurance Payment';

        this.$scope.$watch('$ctrl.payment', (value, oldValue) => {
            const paymentTypeOldValue = oldValue.type;
            const paymentTypeNewValue = value.type;

            const receiptOldValue = oldValue.receipt;
            const receiptNewValue = value.receipt;

            const paymentTypeWasSet = !paymentTypeOldValue && paymentTypeNewValue;
            const receiptValueChanged = !_.isEqual(receiptOldValue, receiptNewValue);
            const notEmptyNevReceiptValue = !_.isEmpty(receiptNewValue);

            if(paymentTypeWasSet || (receiptValueChanged && notEmptyNevReceiptValue)) {
                this.paymentType = value.type;
                this.selectedTeams = value.receipt;

                if(this.quote && this.quote.quote_id) {
                    this.updateQuote();
                } else {
                    this.createQuote();
                }
            }
        }, true);
    }

    changeDecision(value) {
        this.insuranceDecision.option = value;

        this.onUserDecisionChange({
            quote: _.pick(this.quote, ['quote_id', 'total_amount']),
            isAgree: this.insuranceDecision.option
        });
    }

    getDisclaimer() {
        return this.$sce.trustAsHtml(this.quote.legal_disclaimer);
    }

    createQuote() {
        if(!this.paymentType) {
            return;
        }

        const data = this.getDataForQuote();
        this.updatingQuoteData = true;

        this.verticalInsuranceService.createQuote(this.eventID, data)
            .then(response => {
                if(!_.isEmpty(response)) {
                    this.quote = response;
                    this.quoteDataLoaded = true;
                } else {
                    this.quoteDataLoaded = false;
                }
            })
            .finally(() => this.updatingQuoteData = false);
    }

    updateQuote() {
        if(!this.paymentType) {
            return;
        }

        const data = this.getDataForQuote();
        this.updatingQuoteData = true;

        this.verticalInsuranceService.updateQuote(this.eventID, this.quote.quote_id, data)
            .then(response => {
                if(!_.isEmpty(response)) {
                    this.quote = response;
                    this.quoteDataLoaded = true;
                } else {
                    this.quoteDataLoaded = false;
                }
            })
            .finally(() => this.updatingQuoteData = false);
    }

    getDataForQuote() {
        return this.teams.reduce((acc, team) => {
            if(this.selectedTeams.includes(team.roster_team_id)) {
                acc.push({
                    team_name: team.team_name,
                    insurable_amount: this.__calculateTeamAmount(team),
                    id: team.roster_team_id,
                });
            }

            return acc;
        }, []);
    }

    __calculateTeamAmount (team) {
        const eventSurcharge = this.purchaseService.getEventSurcharge(this.event, this.paymentType);
        const divisionSurcharge = this.purchaseService.getTeamDivisionSurcharge(team, this.paymentType);
        const teamRegFee = this.purchaseService.getTeamDue(team.div_reg_fee, this.event.reg_fee, 0, team.paid);

        return (teamRegFee + (divisionSurcharge || eventSurcharge || 0)) * 100;
    }

    getInlineNoticeMessage() {
        const amount = this.payment.total;
        const insuranceAmount = this.quote.total_amount;

        return `
            Please, be advised that by pressing <b>Pay $${amount}</b> you also agree to pay the <b>$${insuranceAmount} Insurance Coverage Fee.</b>
            There will be <b>two separate payments</b> on your bank statement.`
    }
}

Controller.$inject = ['verticalInsuranceService', 'purchaseService', '$stateParams', '$scope', '$sce'];

angular.module('SportWrench').component('verticalInsurance', {
    templateUrl: 'vertical-insurance/vertical-insurance.template.html',
    bindings: {
        teams: '<',
        event: '<',
        payment: '<',
        onUserDecisionChange: '&'
    },
    controller: Controller
});
