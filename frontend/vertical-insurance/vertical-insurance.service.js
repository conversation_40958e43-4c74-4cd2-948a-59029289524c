class VerticalInsuranceService {
    constructor($http, $window) {
        this.$http = $http;
        this.$window = $window;
    }

    async createQuote(event, data) {
        try {
            const response = await this.$http.post(
                `/api/vertical-insurance/team-registration/event/${event}/quote`, { teams: data }
            );

            return response && response.data && response.data.quote;
        } catch (err) {
            return null;
        }
    }

    async updateQuote(event, quoteID, data) {
        try {
            const response = await this.$http.put(
                `/api/vertical-insurance/team-registration/event/${event}/quote/${quoteID}`, { teams: data }
            );

            return response && response.data && response.data.quote;
        } catch (err) {
            return null;
        }
    }

    redirectToVIProposalPage(event) {
        const windowReference = window.open();

        this.$http.get(`/api/vertical-insurance/team-registration/event/${event}/link`)
            .then(function(response) {
                if(response && response.data && response.data.link) {
                    windowReference.location = response.data.link;
                }
            });
    }
}

VerticalInsuranceService.$inject = ['$http', '$window'];

angular.module('SportWrench').service('verticalInsuranceService', VerticalInsuranceService);
