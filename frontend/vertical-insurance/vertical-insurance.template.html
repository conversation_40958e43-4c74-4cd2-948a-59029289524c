<div class="row" ng-if="$ctrl.updatingQuoteData">
    <div class="col-xs-12 text-center">
        <spinner active="true"></spinner>
    </div>
</div>
<div data-ng-show="$ctrl.quoteDataLoaded && !$ctrl.updatingQuoteData">
    <div class="row">
        <div class="col-sm-3 col-xs-5 insurance-badge">
            Recommended
        </div>
    </div>
    <div class="row vertical-insurance-form">
        <div class="col-xs-12">
            <h4 ng-bind="$ctrl.quote.heading"></h4>
        </div>
        <div class="col-xs-12">
            <p ng-bind="$ctrl.quote.description"></p>
            <p class="small">*Required: Select an option to continue</p>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <div ng-class="{ 'col-sm-12 insurance-decision-radio':true, 'green-border': $ctrl.insuranceDecision.option }"
                     data-ng-click="$ctrl.changeDecision(true)"
                >
                    <label>
                        <input type="radio"
                               class="insurance-agree"
                               data-ng-value="true"
                               name="insurance_decision"
                               data-ng-model="$ctrl.insuranceDecision.option"
                        >
                        Accept Coverage for ${{$ctrl.quote.total_amount}}
                    </label>
                </div>
                <div ng-class="{ 'col-sm-12 insurance-decision-radio':true, 'black-border': !$ctrl.insuranceDecision.option }"
                     data-ng-click="$ctrl.changeDecision(false)"
                >
                    <label style="font-weight: normal">
                        <input type="radio"
                               class="insurance-decline"
                               data-ng-value="false"
                               name="insurance_decision"
                               data-ng-model="$ctrl.insuranceDecision.option"
                        >
                        Decline coverage
                    </label>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="row">
                <div class="col-xs-12 insurance-descriptor-heading">
                    <p ng-bind="$ctrl.quote.descriptors_heading"></p>
                </div>
                <div class="col-xs-12 insurance-descriptor" data-ng-repeat="descriptor in $ctrl.quote.descriptors">
                    <i class="glyphicon glyphicon-ok-sign"></i><span ng-bind="descriptor"></span>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div data-ng-bind-html="$ctrl.getDisclaimer()"></div>
        </div>
    </div>

    <inline-notice
        ng-if="$ctrl.insuranceDecision.option"
        title="$ctrl.inlineNoticeTitle"
        message="$ctrl.getInlineNoticeMessage()"
    ></inline-notice>
</div>
