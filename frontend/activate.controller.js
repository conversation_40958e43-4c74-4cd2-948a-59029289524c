angular.module('SportWrench')

.controller('activate<PERSON>ontroller', activateController);

function activateController (
    $scope, $rootScope, activationService, $state, $stateParams, toastr,
    APP_ROUTES, ACTIVATION_SUCCEED, ACTIVATION_FAILED
) {
    var code = $stateParams.code;
    if (code) {
        activationService.activate(code).then(function () {
            toastr.success(ACTIVATION_SUCCEED);
            $state.go(APP_ROUTES.LOGIN);
        });
    } else {
        toastr.warning(ACTIVATION_FAILED);
        $state.go(APP_ROUTES.INDEX);
    }
}
