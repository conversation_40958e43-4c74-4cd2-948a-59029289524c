<spinner ng-if="!$ctrl.isPageLoaded"></spinner>
<div class="table-responsive data-loading" ng-class="{'done': $ctrl.isPageLoaded}">
    <table class="table sw-adaptive-grid text-center" sticky-header>
        <thead>
            <tr class="font-bold">
                <td align="left">Season</td>
                <td>Event Name</td>
                <td>Date Start</td>
                <td>Date End</td>
                <td>Event Type</td>
            </tr>
        </thead>
        <tbody>{{$ctrl.types.length}}
        <tr ng-repeat-start="(type, t) in $ctrl.types" class="bg-info font-bold pointer" ng-click="$ctrl.openEvents(type)">
            <td colspan="5" align="left">{{t.title}} Events: {{t.count}}</td>
        </tr>
        <tr ng-if="!t.loaded && t.startLoading">
            <td colspan="5">
                <spinner active="!t.loaded"></spinner>
            </td>
        </tr>
        <tr ng-repeat-end ng-repeat="e in t.events" ng-if="$ctrl.openedTab[type] && t.loaded">
            <td align="left">{{e.season}}</td>
            <td class="pointer">
                <a ng-click="$ctrl.openUSAVEvent(e.event_id)">{{e.name}}</a>
            </td>
            <td><span ng-if="e.date_start">{{e.date_start}}</span></td>
            <td><span ng-if="e.date_end">{{e.date_end}}</span></td>
            <td>{{e.type}}</td>
        </tr>
        </tbody>
    </table>
</div>
