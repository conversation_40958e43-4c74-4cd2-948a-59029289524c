angular.module('SportWrench').service('UsavAdminService', UsavAdminService);

function UsavAdminService ($http, $httpParamSerializerJQLike, $uibModal) {
    this.$http              = $http;
    this.baseURL            = '/api/usav-admin/';
    this.paramSerializer    = $httpParamSerializerJQLike;
    this.$uibModal          = $uibModal;
}

UsavAdminService.prototype.getEvents = function (params) {
    return this.$http.get(`${this.baseURL}events`, { params: params })
        .then(response => response.data && response.data.events);
};

UsavAdminService.prototype.getEventTypes = function () {
    return this.$http.get(`${this.baseURL}event-types`)
        .then(response => response.data && response.data.types);
};

UsavAdminService.prototype.getDashboardData = function (eventID, params) {
    return this.$http.get(`${this.baseURL}event/${eventID}/members`, {
        params          : params,
        paramSerializer : '$httpParamSerializerJQLike'
    }).then(response => response.data && response.data.members);
};

UsavAdminService.prototype.getClubData = function (clubID, eventID) {
    return this.$http.get(`${this.baseURL}event/${eventID}/club/${clubID}/find`)
        .then(response => response.data && response.data.club);
};

UsavAdminService.prototype.getStaffInformation = function (id) {
    return this.$http.get(`${this.baseURL}member/staff/${id}`)
        .then(({ data: { information } }) => information);
}

UsavAdminService.prototype.getAthleteInformation = function (id) {
    return this.$http.get(`${this.baseURL}member/athlete/${id}`)
        .then(({ data: { information } }) => information);
}

UsavAdminService.prototype.openClubModal = function (id, name) {
    return this.$uibModal.open({
        template 	: `
            <modal-wrapper>
                <usav-admin-club club-id="id"></usav-admin-club>
            </modal-wrapper>
        `,
        size 		: 'md',
        controller 	: ['$scope', function ($scope) {
            $scope.id           = id;
            $scope.modalTitle   = `<h4>${name}</h4>`;
        }.bind(this)]
    }).result;
};

UsavAdminService.prototype.openMemberInfoModal = function (id, role) {
    return this.$uibModal.open({
        template: '<usav-admin-member-info on-close="$close()" role="role" id="id" />',
        size: 'md',
        controller: [
            '$scope',
            $scope => {
                $scope.id = id;
                $scope.role = role;
            },
        ]
    }).result;
};

