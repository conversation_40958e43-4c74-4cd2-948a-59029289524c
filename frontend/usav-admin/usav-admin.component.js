angular.module('SportWrench').component('usavAdmin', {
    templateUrl 	: 'usav-admin/usav-admin.html',
    controller 		: UsavAdminController
});

UsavAdminController.$inject = [
    'UsavAdminService', 'QUALIFIER_EVENT_TYPE', 'OTHER_EVENT_TYPE', 'APP_ROUTES', '$state'
];

function UsavAdminController (UsavAdminService, QUALIFIER_EVENT_TYPE, OTHER_EVENT_TYPE, APP_ROUTES, $state) {
    let self            = this;
    this.events         = [];
    this.types          = {};
    this.openedTab      = [];
    this.isPageLoaded   = false;

    let eventTypes = [QUALIFIER_EVENT_TYPE, OTHER_EVENT_TYPE];

    let eventTypeAliases = {
        [QUALIFIER_EVENT_TYPE]: {
            title       : 'Qualifier / Bids',
            count_field : 'qualifier_count',
            typeFilter  : 'qualifier_only'
        },
        [OTHER_EVENT_TYPE]: {
            title       : 'Other',
            count_field : 'not_qualifier_count',
            typeFilter  : 'not_qualifier_only'
        }
    };

    this.$onInit = function () {
        loadData();
    };

    this.loadEvents = function (type) {
        self.types[type].startLoading = true;

        UsavAdminService.getEvents({ [eventTypeAliases[type].typeFilter]: true }).then(events => {
            self.types[type].events         = events;
            self.types[type].loaded         = true;
            self.types[type].startLoading   = false;

            if(!self.isPageLoaded) {
                self.isPageLoaded = true;
            }

            self.openedTab[type] = !self.openedTab[type];
        })
    };

    function loadData() {
        UsavAdminService.getEventTypes().then(types => {

            eventTypes.forEach(type => {
                self.types[type] = {
                    count       : types[eventTypeAliases[type].count_field],
                    events      : [],
                    title       : eventTypeAliases[type].title,
                    loaded      : false,
                    startLoading: false
                }
            });

            self.loadEvents(QUALIFIER_EVENT_TYPE);
        });
    }

    this.openEvents = function (type) {
        if(!self.openedTab[type] && (!self.types[type].events || !self.types[type].events.length)) {
            self.loadEvents(type);
        } else {
            self.openedTab[type] = !self.openedTab[type];
        }
    }

    this.openUSAVEvent = function (id) {
        $state.go(APP_ROUTES.UA.DASHBOARD, { event: id });
    }
}
