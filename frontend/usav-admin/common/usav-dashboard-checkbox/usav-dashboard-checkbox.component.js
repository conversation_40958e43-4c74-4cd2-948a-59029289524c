angular.module('SportWrench').component('usavDashboardCheckbox', {
    templateUrl 	: 'usav-admin/common/usav-dashboard-checkbox/usav-dashboard-checkbox.html',
    bindings        : {
        member  : '<',
        type    : '@'
    },
    controller 		: UsavDashboardCheckboxController
});

UsavDashboardCheckboxController.$inject = ['STAFF_MEMBER_TYPE'];

function UsavDashboardCheckboxController(STAFF_MEMBER_TYPE) {
    let self = this;

    this.checked        = false;
    this.tooltipText    = '';

    const SS_TYPE    = 'ss';
    const BKG_TYPE   = 'bkg';

    this.$onInit = function () {
        if(self.type === SS_TYPE) {
            self.checked        = self.member.ss_verified;
            self.tooltipText    = __ssTooltipText__(self.member);
        } else if(self.type === BKG_TYPE) {
            self.checked        = !self.member.is_bg_expired;
            self.tooltipText    = __bgTooltipText__(self.member);
        }
    };

    this.isStaff = (self.member.role === STAFF_MEMBER_TYPE);

    let __bgTooltipText__ = function (member) {
        if(member.bg_expire_date) {
            return (member.is_bg_expired)
                ? 'Expired: ' + member.bg_expire_date
                : 'Expires: ' + member.bg_expire_date;
        } else {
            return 'N/A';
        }
    };

    let __ssTooltipText__ = function (member) {
        if(member.safesport_start_date) {

            if(member.safesport_end_date) {
                return member.ss_verified
                    ? 'Expires: ' + member.safesport_end_date
                    : 'Expired: ' + member.safesport_end_date;
            } else {
                return 'Verified: ' + member.safesport_start_date;
            }

        } else {
            return 'N/A';
        }
    };
}
