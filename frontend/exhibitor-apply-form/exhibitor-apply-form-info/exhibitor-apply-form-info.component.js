class ExhibitorApplyFormInfoComponent {
    constructor(ExhibitorsService) {
        this.service = ExhibitorsService;

        this.info = {};

        this.loading = {
            inProcess: true,
            error: '',
        }
    }

    $onInit() {
        this.loadInfo();
    }

    loadInfo() {
        this.service.getExhibitorProfileInfo({ eventID: this.eventId, exhibitorID: this.exhibitorId })
            .then(this.onGetInfoSuccess.bind(this))
            .catch(this.onGetInfoError.bind(this))
            .finally(this.onGetInfoFinally.bind(this));
    }

    onGetInfoSuccess(response) {
        this.info = response;
    }

    onGetInfoError(error) {
        this.loading.error = error && error.validation
            ? error.validation
            : this.INTERNAL_ERROR_MSG;
    }

    onGetInfoFinally() {
        this.loading.inProcess = false;
    }
}

angular.module('SportWrench').component('exhibitorApplyFormInfo', {
    templateUrl: 'exhibitor-apply-form/exhibitor-apply-form-info/exhibitor-apply-form-info.html',
    bindings: {
        eventId: '<',
        exhibitorId: '<',
    },
    controller: [
        'ExhibitorsService',
        ExhibitorApplyFormInfoComponent
    ],
})
