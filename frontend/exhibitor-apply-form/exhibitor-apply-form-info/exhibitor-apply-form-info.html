<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error" class="col-sm-12 exhibitor-apply-form-info">
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Company Name:</div>
            <div class="col-sm-8">{{$ctrl.info.company_name}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Company Description:</div>
            <div class="col-sm-8">{{$ctrl.info.company_description}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">First Name:</div>
            <div class="col-sm-8">{{$ctrl.info.first}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Last Name:</div>
            <div class="col-sm-8">{{$ctrl.info.last}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Title:</div>
            <div class="col-sm-8">{{$ctrl.info.sponsor_title}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Email:</div>
            <div class="col-sm-8">
                <a href="mailto:{{$ctrl.info.email}}">{{$ctrl.info.email}}</a>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Website:</div>
            <div class="col-sm-8">
                <short-link href="$ctrl.info.website_url"></short-link>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Mobile Phone:</div>
            <div class="col-sm-8">{{$ctrl.info.mobile_phone}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Office Phone:</div>
            <div class="col-sm-8">{{$ctrl.info.office_phone}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Street:</div>
            <div class="col-sm-8">{{$ctrl.info.street}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">City:</div>
            <div class="col-sm-8">{{$ctrl.info.city}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">State:</div>
            <div class="col-sm-8">{{$ctrl.info.state}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">ZIP:</div>
            <div class="col-sm-8">{{$ctrl.info.zip}}</div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Exhibitor:</div>
            <div class="col-sm-8"><input type="checkbox" disabled ng-model="$ctrl.info.is_exhibitor"></div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Sponsor:</div>
            <div class="col-sm-8"><input type="checkbox" disabled ng-model="$ctrl.info.is_sponsor"></div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Non-Profit:</div>
            <div class="col-sm-8"><input type="checkbox" disabled ng-model="$ctrl.info.is_non_profit"></div>
        </div>
        <div class="row">
            <div class="col-sm-4 text-bold text-right">Other:</div>
            <div class="col-sm-8"><input type="checkbox" disabled ng-model="$ctrl.info.is_other"></div>
        </div>
    </div>
</div>
