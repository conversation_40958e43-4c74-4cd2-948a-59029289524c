angular.module('SportWrench')

.controller('eventInfoController', eventInfoController);

function eventInfoController (
    $scope, eventsService, eswService, $stateParams, $state, rosterTeamService, $timeout, $q, $rootScope, APP_ROUTES
) {

    $scope.event            = $stateParams.event;
    $scope.event_info       = {};
    $scope.roster_teams     = [];
    $scope.event_divisions  = undefined;
    $rootScope.attending    = true;
    $scope.states           = {
        index: APP_ROUTES.INDEX
    }

    $scope.checkDivision = function (d) {
        if(d.teams.length) {
            d.checked = !d.checked
        }
    }

    __load();

    function __load () {
        eswService.getEventByIdWithLocation($stateParams.event)
        .then(function (response) {            
            $scope.event_info = (response && response.data.event) || {};
            return eswService.getEventDivisionsList($stateParams.event)
        }).then(function (response) {
            if(response && response.data) {
                $scope.event_divisions = response.data.divisions;
            }            
        }, function () {
            if(!$scope.event_divisions) $scope.event_divisions = []
        })
    }
}
