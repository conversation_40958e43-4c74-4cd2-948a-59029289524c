angular.module('<PERSON>Wrench',
    [
        'moment', // check if we can make a custom build for this, that has only needed functions
        '_',
        'plaid',
        'stripe_v3',
        'react_email_editor',
        'bee',
        'payment-hub',
        'sentry',
        'ngSanitize',
        'ngStorage',
        'ui.router',
        'ui.bootstrap',
        'oc.lazyLoad',
        'ui.mask',
        'angular-loading-bar',
        'ngTable', // in use on 2 pages only
        'infinite-scroll', // /club/athletes/v2/club-athletes.html, /events/dashboard/teams/all-teams.tab.html
        'ui.bootstrap.datetimepicker',
        'angular-clipboard',
        'toastr',
        'ui.select',
        'colorpicker.module',
        'angularjs-dropdown-multiselect',
        'signaturePad'
    ]
);
