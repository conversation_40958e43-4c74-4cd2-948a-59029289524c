<form class="form form-inline row-space">
    <div class="form-group">
        <div class="search-box">
            <input id="search_events" type="search" class="form-control" placeholder="Search ..." data-ng-model="search_name">
            <span class="glyphicon glyphicon-search"></span>
        </div>
    </div>
    <div class="form-group">
        <input
            type="text"
            ng-model="current_event"
            uib-typeahead="e as e.long_name for e in events | filter:$viewValue"
            class="form-control"
            typeahead-on-select="chooseEvent()"
            placeholder="Choose an Event"
        >
    </div>
    <div class="form-group" ng-show="exhibitors.length > 0">
        <input
            type="text"
            ng-model="current_exhibitor"
            uib-typeahead="e as _label(e) for e in exhibitors | filter:$viewValue"
            class="form-control"
            typeahead-on-select="chooseExhibitor()"
            placeholder="Choose an Exhibitor"
        >
    </div>
    <div class="form-group">
        <select ng-model="status" class="form-control padding-sm">
            <option value="" selected>Status</option>
            <option value="paid">Paid</option>
            <option value="pending">Pending</option>
            <option value="canceled">Canceled</option>
        </select>
    </div>
    <div class="form-group">
        <select ng-model="method" class="form-control padding-sm">
            <option value="">Method</option>
            <option value="card">Card</option>
            <option value="check">Check</option>
            <option value="pending-payment">Not Paid</option>
        </select>
    </div>
    <div class="form-group">
        <!--  ng-model="date_from" class="form-control padding-sm"  -->
        <!--  ng-model="date_to" class="form-control padding-sm"-->

        <div class="input-group date-time-control" ng-click="utils.fromPicker.isOpen = !utils.fromPicker.isOpen">
            <input type="text" ng-model="date_from" class="form-control white-ro" uib-datepicker-popup="{{utils.format}}" max-date="date_to" is-open="utils.fromPicker.isOpen" placeholder="From {{::utils.placeholder}}" readonly>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>

        <div class="input-group" ng-click="utils.toPicker.isOpen = !utils.toPicker.isOpen">
            <input type="text" ng-model="date_to" class="form-control white-ro" uib-datepicker-popup="{{utils.format}}" min-date="date_from"  is-open="utils.toPicker.isOpen" placeholder="To {{::utils.placeholder}}" readonly>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>

    </div>
    <div class="form-group">
        <select  ng-model="season" class="form-control padding-sm">
            <option ng-repeat="season in seasons" ng-value="season">{{season}} Events</option>
        </select>
    </div>
    <div class="col-sm-2 pull-right">
        <button class="btn btn-primary" ng-click="openCreateInvoiceModal($event)">Create Invoice</button>
    </div>
</form>
<table class="table table-condensed master-table" sticky-header>
    <thead>
        <tr>
            <th></th>
            <th>Created</th>
            <th>Received</th>
            <th>Event name</th>
            <th>Exhibitor</th>
            <th>Paid by</th>
            <th>Amount</th>
            <th>Status</th>
            <th ng-if="showEditButtons"></th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat-start="payment in payments | filter:receipts_filter | filter:all_filter" ng-click="get_booths_payments(payment)">
            <td>
                <i class="fa fa-angle-double-down" ng-show="payment.show_booths"></i>
                <i class="fa fa-angle-double-right" ng-show="!payment.show_booths"></i>
            </td>
            <td>{{payment.created | UTCdate:'MM/DD, hh:mm a'}}</td>
            <td>{{payment.date_paid | UTCdate:'MM/DD'}} <span ng-show="payment.check_num">#{{payment.check_num}}</span></td>
            <td>{{payment.event_name}}</td>
            <td>{{payment.company_name}}</td>
            <td>{{payment.type}}</td>
            <td><b>${{payment.amount}}</b></td>
            <td>{{payment.status}}</td>
            <td ng-if="showEditButtons">
                <i class="fa fa-pencil-square-o big-icon"
                   ng-if="isPendingPayment(payment)"
                   ng-click="openExhibitorEventReceiptModal($event, payment);"
                ></i>
            </td>
        </tr>
        <tr ng-repeat-end ng-if="payment.show_booths">
            <td colspan="8">
                <div class="well well-sm col-sm-offset-2 col-sm-7">
                    <form class="form form-inline" ng-if="showPaymentsPanel()">
                        <div class="form-group" ng-if="payment.type === 'check'">
                            <label>
                                Received <input type="checkbox" ng-model="payment.enter_check" ng-change="set_received_date(payment);"
                                ng-init="payment.enter_check =(payment.check_num && payment.date_paid)?true:false" ng-disabled="payment.status == 'canceled' || payment.status == 'refunded'">
                            </label>
                        </div>
                        <div class="form-group" ng-if="payment.type === 'check'">
                            <div class="input-group date date-reg-end" data-provide="datepicker" data-date-format="mm/dd/yyyy">
                                <input data-ng-model="payment.date_paid" class="form-control input-sm padding-sm" placeholder="mm/dd/yyyy" ng-disabled="!payment.enter_check || payment.status == 'canceled' || payment.status == 'refunded'" ng-change="payment.not_saved = true" date-formatter>
                                <span class="input-group-addon padding-sm">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                        <div class="form-group" ng-if="payment.type === 'check'">
                            <div class="input-group">
                                <span class="input-group-addon padding-sm">
                                    &nbsp;#&nbsp;
                                </span>
                                <input data-ng-model="payment.check_num" class="form-control input-sm padding-sm" maxlength="50" ng-disabled="!payment.enter_check || payment.status == 'canceled' || payment.status == 'refunded'" placeholder="Check number" ng-change="payment.not_saved = true" >
                            </div>
                        </div>
                        <button class="btn btn-primary" ng-if="showSaveBtn(payment)" ng-click="receive(payment);">Save</button>
                        <button class="btn btn-danger" ng-if="showVoidBtn(payment)" ng-click="void(payment);">Void</button>
                        <button class="btn btn-warning" ng-if="showRefundBtn(payment)" ng-click="refund(payment);">Refund</button>
                    </form>
                    <p><a href="/sales/invoice/{{payment.purchase_id}}" target="_blank">Invoice #{{payment.purchase_id}}</a></p>
                    <p ng-if="payment.notes"><b>Notes:</b> {{payment.notes}}</p>
                    <table class="table row-space table-condensed">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Fee</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="booth in payment.booth_payments">
                                <td>{{booth.title}}</td>
                                <td>{{booth.description || ''}}</td>
                                <td>{{booth.quantity}}</td>
                                <td>${{booth.fee}}</td>
                                <td>${{booth.amount}}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div ng-if="showPayButtonsForPendingPayment(payment)">
                        <div ng-if="isPendingPayment(payment)">
                            <button
                                class="btn btn-primary"
                                ng-click="showCardForm(payment)"
                            >Pay by Credit Card</button>

                            <button
                                class="btn btn-primary"
                                ng-click="showCheckButton()"
                            >Pay by Check</button>
                        </div>
                        <div class="row row-space" ng-if="cardFormOpened">
                            <div class="col-sm-12">
                                <stripe-elements-form payment="cardPaymentObject" pay="payByCard(token)"></stripe-elements-form>
                            </div>
                        </div>
                        <uib-alert class="mt-5" ng-repeat="err in payment_errors track by $index" type="{{'danger'}}" close="closeAlert($index)">{{err}}</uib-alert>
                        <div class="row row-space" ng-if="checkButtonVisible">
                            <div class="col-sm-12">
                                <div class="row row-space">
                                    <div class="col-sm-6 center-form-text">
                                        <button class="btn btn-success" ng-click="payByCheck(payment)">Confirm ${{ payment.amount }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row row-space" ng-show="paymentInProgress">
                            <div class="col-sm-12">
                                <uib-progressbar class="progress-striped" value="progress_bar_value" type="success">{{progress_bar_value | number:0 }}%</uib-progressbar>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    </tbody>
</table>
