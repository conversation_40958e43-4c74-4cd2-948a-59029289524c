angular.module('SportWrench').directive('salesReceipts', function() {
    var pickerDateFormat    = 'MM/dd/yyyy',
        utcDateFormat       = 'MM/DD/YYYY, h:mm a';
    return {
        restrict: 'E',
        scope: {
            payments: '=',
            events: '=',
            exhibitors: '=',
            getBoothPayments: '&',
            receivePayment: '&',
            ignoreOperations: '@',
            showEditButtons: '@',
            onReloadTable: '&',
            filters: '<',
        },
        templateUrl: 'components/sales-receipts/receipts.html',
        compile: function(element, attrs) {
            if (!attrs.exhibitors) {
                attrs.exhibitors = [];
            }
        },
        controller: [
            '$scope', '$filter', '$http', 'SharedService', 'APPLICATION_STATUS', 'PAYMENT_STATUS', 'PAYMENT_TYPE',
            'ExhibitorReceiptsService', 'StripeElementsService', 'toastr', '$interval', 'INTERNAL_ERROR_MSG', 'ExhibitorsService',
        function(
            $scope, $filter, $http, SharedService, APPLICATION_STATUS, PAYMENT_STATUS, PAYMENT_TYPE,
            ExhibitorReceiptsService, StripeElementsService, toastr, $interval, INTERNAL_ERROR_MSG, ExhibitorsService
        ) {
            var UTCDate = $filter('UTCdate');

            $scope.search_name = '';
            $scope.status = '';
            $scope.method = '';
            $scope.date_to = '';
            $scope.payment_errors = [];

            $scope.utils = {
                format      : pickerDateFormat,
                placeholder : pickerDateFormat.toLowerCase(),
                now         : new Date(),
                fromPicker: {
                    isOpen: false
                },
                toPicker: {
                    isOpen: false
                }
            };

            $scope.closeAlert = function (index) {
                $scope.payment_errors.splice(index, 1);
            };

            $scope.APPLICATION_STATUS = APPLICATION_STATUS;
            $scope.PAYMENT_STATUS = PAYMENT_STATUS;
            $scope.PAYMENT_TYPE = PAYMENT_TYPE;

            // those variables are probably not used
            $scope.paymentTypeChangeModeEnabled = false;
            $scope.showPaymentButtons = true;
            $scope.showPaymentChangeButton = false;

            generateSeasons();

            $scope.receipts_filter = function(item) {
                return (!$scope.current_event || +$scope.current_event.event_id === +item.event_id) &&
                    (!$scope.current_exhibitor || +$scope.current_exhibitor.sponsor_id === +item.sponsor_id) &&
                    (!$scope.status || $scope.status === item.status) &&
                    (!$scope.method || $scope.method === item.type) &&
                    (Number($scope.season) === item.season) &&
                    (!$scope.date_from || new Date($scope.date_from) <= new Date(item.event_date_start)) &&
                    (!$scope.date_to || new Date($scope.date_to) >= new Date(item.event_date_start));

            };

            $scope.all_filter = function({ created, received_date, event_name, company_name, type, amount, status }) {
                return !!(!$scope.search_name || (UTCDate(created, utcDateFormat).indexOf($scope.search_name) >= 0) ||
                    (received_date && UTCDate(received_date, utcDateFormat).indexOf($scope.search_name) >= 0) ||
                    (event_name && event_name.indexOf($scope.search_name) >= 0) ||
                    (company_name && company_name.indexOf($scope.search_name) >= 0) ||
                    (type && type.indexOf($scope.search_name) >= 0) ||
                    (amount && amount.indexOf($scope.search_name) >= 0) ||
                    (status && status.indexOf($scope.search_name) >= 0));

            };

            function collapseAll() {
                $scope.payments.forEach(p => p.show_booths = false);
            }

            $scope.get_booths_payments = function(payment) {
                if (!payment.show_booths) {
                    collapseAll();
                    StripeElementsService.initStripeElements(payment.public_key);
                    payment.show_booths = true;

                    $scope.getBoothPayments({purchaseID: payment.purchase_id})
                        .then(booths => {
                            payment.booth_payments = booths;

                            if(payment.public_key) {
                                try {
                                    StripeElementsService.initStripeElements(payment.public_key);
                                } catch (err) {
                                    console.error(err);

                                    // if stripe key is incorrect - disable card payments
                                    $scope.eventInfo.card = false;
                                }
                            }

                        });
                } else {
                    payment.show_booths = false;
                    payment.booth_payments = false;

                    $scope.cardFormOpened = false;
                    $scope.checkButtonVisible = false;
                    $scope.payment_errors.length = 0;
                }
            };

            $scope._label = function(exhibitor) {
                if (!exhibitor)
                    return '';
                return exhibitor.company_name
                    + ' ( ' + exhibitor.first + ' ' + exhibitor.last + ' )';
            }

            $scope.chooseExhibitor = function() {};

            $scope.chooseEvent = function() {};

            $scope.set_received_date = function (payment) {
                if(payment && !payment.date_paid) {
                    payment.date_paid = new Date();
                }
            };

            $scope.receive = function (payment) {
                if($scope.ignoreOperations || payment.type !== PAYMENT_TYPE.CHECK || payment.status !== PAYMENT_STATUS.PENDING){
                    return;
                }

                let data = _.pick(payment, ['purchase_id', 'check_num', 'date_paid', 'event_id']);

                $scope.receivePayment(data).then(() => {
                    // set status paid
                    payment.status      = PAYMENT_STATUS.PAID;
                    payment.not_saved   = false;
                })
            };

            $scope.void = function (payment) {
                if($scope.ignoreOperations) {
                    return;
                }

                ExhibitorsService.makeVoid(payment.purchase_id, {
                    date_canceled: new Date()
                }, payment.event_id)
                    .then(() => payment.status = PAYMENT_STATUS.CANCELED)
            };

            $scope.refund = function (payment) {
                if($scope.ignoreOperations) return;

                ExhibitorsService.makeRefund(payment.purchase_id, {
                    date_refunded: new Date()
                }, payment.event_id)
                    .then(() => payment.status = 'refunded')
            };

            $scope.showSaveBtn = function (p) {
                return p && ((p.status === PAYMENT_STATUS.PENDING && p.type === PAYMENT_TYPE.CHECK) || p.not_saved)
            };

            $scope.showVoidBtn = function (p) {
                return p && (p.status === PAYMENT_STATUS.PAID && p.type === PAYMENT_TYPE.CHECK)
            };

            $scope.showRefundBtn = function (p) {
                return p && (p.status === PAYMENT_STATUS.PAID && p.type === PAYMENT_TYPE.CARD)
            };

            $scope.showPaymentsPanel = function () {
                return !this.ignoreOperations;
            };

            $scope.showPayButtonsForPendingPayment = function (p) {
                return p.status === PAYMENT_STATUS.PENDING && p.application_status === APPLICATION_STATUS.APPROVED;
            };

            $scope.showCardForm = function(payment) {
                $scope.cardPaymentObject = {
                    purchase_id : payment.purchase_id,
                    total       : payment.amount,
                };

                $scope.cardFormOpened = true;
                $scope.checkButtonVisible = false;
            };

            $scope.isPendingPayment = function(payment) {
                return payment.status === PAYMENT_STATUS.PENDING && payment.type === PAYMENT_TYPE.PENDING_PAYMENT;
            };

            $scope.showCheckButton = function() {
                $scope.cardFormOpened = false;
                $scope.checkButtonVisible = true;
            };

            $scope.payByCard = function (token) {
                let payment = _.filter($scope.payments, p => p.purchase_id === $scope.cardPaymentObject.purchase_id)[0];

                payment = preparePayment(payment, PAYMENT_TYPE.CARD);

                return makePayment(payment, PAYMENT_TYPE.CARD, token);
            };

            $scope.payByCheck = function (payment) {
                payment = preparePayment(payment, PAYMENT_TYPE.CHECK);

                return makePayment(payment, PAYMENT_TYPE.CHECK);
            };

            $scope.openExhibitorEventReceiptModal = function($event, payment) {
                $event.stopPropagation();

                ExhibitorReceiptsService.openExhibitorEventReceiptModal({
                    eventId: payment.event_id,
                    purchaseId: payment.purchase_id,
                    companyName: payment.company_name,
                }).then(isSaved => {
                    if (isSaved) {
                        $scope.onReloadTable();
                    }
                });
            };

            $scope.openCreateInvoiceModal = function($event) {
                const isCreateMode= true;

                ExhibitorReceiptsService.openExhibitorEventReceiptModal({
                    events: $scope.events
                }, isCreateMode).then(withReload => {
                    if (withReload) {
                        $scope.onReloadTable();
                    }
                });
            };

            $scope.$watch('events', function(data) {
                if (data.length && !_.isEmpty($scope.filters)) {
                    applyFilters(data);
                }
            });

            function preparePayment(payment, type) {
                return {
                    purchase_id : payment.purchase_id,
                    event_id    : payment.event_id,
                    total       : Number(payment.amount),
                    booth       : payment.booth_payments.map(prepareBooth),
                    type        : type,
                    sponsor_id  : payment.sponsor_id,
                    amount      : Number(payment.amount)
                }
            }

            function prepareBooth(booth) {
                return _.pick(booth, ['event_booth_id', 'fee', 'quantity', 'title'])
            }

            function generateSeasons() {
                SharedService.generateSeasons()
                    .then(({ season, seasons }) => {
                        $scope.season = $scope.filters && Number($scope.filters.eventYear) || season;
                        $scope.seasons = seasons;
                    })
            }

            function applyFilters(events) {
                const { eventName } = $scope.filters;

                $scope.current_event = events.find(event => event.long_name === eventName);
            }

            function makePayment(payment, type, token) {
                let progressBarInterval = null;

                if ($scope.paymentInProgress) {
                    return;
                }

                if (token) {
                    payment.token = token;
                }

                $scope.payment_errors.length = 0;
                $scope.progress_bar_value = 0;
                $scope.paymentInProgress = true;
                $scope.progress_bar_value += 20;

                progressBarInterval = $interval(() => {
                    if ($scope.progress_bar_value >= 100) {
                        $interval.cancel(progressBarInterval);
                    } else if ($scope.progress_bar_value < 48) {
                        $scope.progress_bar_value += 4;
                    } else if ($scope.progress_bar_value >= 48) {
                        $scope.progress_bar_value += 1;
                    }
                }, 250);

                return ExhibitorReceiptsService.payReceipt(type, payment, payment.event_id)
                    .then(() => {
                        toastr.success('Successful Payment');

                        $scope.progress_bar_value = 100;

                        $scope.paymentInProgress    = false;

                        $scope.checkButtonVisible   = false;
                        $scope.cardFormOpened       = false;

                        $scope.payments.forEach(p => {
                            if(p.purchase_id === payment.purchase_id) {
                                p.type      = type;
                                p.status    = type === PAYMENT_TYPE.CARD ? PAYMENT_STATUS.PAID : p.status
                            }
                        });

                        return Promise.resolve();
                    })
                    .catch(({ data: error }) => {
                        if (progressBarInterval) {
                            $interval.cancel(progressBarInterval);
                        }

                        $scope.checkButtonVisible = false;
                        $scope.cardFormOpened = false;
                        $scope.paymentInProgress = false;

                        $scope.payment_errors.push(
                            error && error.validation
                                ? error.validation
                                : INTERNAL_ERROR_MSG
                        );
                    })
            }
        }]
    }
});
