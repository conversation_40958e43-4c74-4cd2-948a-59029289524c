<div 
    uib-dropdown
    class="change-paid-team"
    is-open="$ctrl.teamsListOpened" 
    on-toggle="$ctrl.loadTeamsList()">

	<div uib-dropdown-toggle class="pointer">
		<span ng-bind="$ctrl.teamName"></span>
		<i class="fa fa-exchange"></i> 
	</div>
    
    <div 
        class="dropdown-menu large-drop-down-menu dropdown-team-filter pull-right" 
        uib-dropdown-menu role="menu"
        ng-click="$event.stopPropagation()">

        <div class="row-space" ng-if="$ctrl.showErrorMsg()">
            <uib-alert type="danger text-center"><i class="fa fa-exclamation-triangle"></i> No Teams found!</uib-alert>
        </div>
       
        <teams-picker-list
            teams="$ctrl.teams" 
            amount="$ctrl.paidAmount"
            discount="$ctrl.discount"
            ng-if="$ctrl.showTeamsList()"
            payment-type="$ctrl.paymentType"
            replace-team="$ctrl.replaceTeam(team_id, name, discount_diff, reg_fee)">
        </teams-picker-list> 

        <spinner active="$ctrl.loading"></spinner>  
    </div>
</div>