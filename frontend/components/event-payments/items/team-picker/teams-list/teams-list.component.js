angular.module('SportWrench').component('teamsPickerList', {
	templateUrl : 'components/event-payments/items/team-picker/teams-list/teams-list.html',
	bindings 	: {
		teams 	 		: '<',
		amount 	 		: '<',
		discount 		: '<',
		paymentType 	: '<',
		replaceTeam 	: '&'
	},
	controller: [
		'_', 'eventDashboardService', 'purchaseService', 'ConfirmationService', 'UtilsService',
		TeamsPickerListController]
});

function TeamsPickerListController (
				_, eventDashboardService, purchaseService, ConfirmationService, UtilsService) {

	var event 			= eventDashboardService.getEvent();
	var eventRegFee 	= event.reg_fee;
	var eventSurcharge 	= purchaseService.getEventSurcharge(event, this.paymentType);
	var discountMsg 	= 
		' will be applied to this team to keep payment amount at the same value. ' +
		'If you don\'t want to set such discount for the new team - ' + 
		'you can cancel the old team and ask club director to pay ' + 
		'separately for the new team.'

	var setTeamsAvailability = function (amount, discount, regFeeOfEvent, typeSurcharge, teams) {
		teams.forEach(function (team) {
			/* 
			* Ignore team's discount, because we set new discount 
			* as difference between due and paid amounts 
			*/
			var due = purchaseService.getTeamDue(
						team.div_reg_fee, regFeeOfEvent, 0, team.paid, typeSurcharge);

			/**
			* We allow to switch to team with greater due amount. 
			* The difference between new team's due amount and paid amount will be recorded as
			* a discount
			**/
			if (due >= amount) {
				team.discount_diff = UtilsService.approxNumber(due - amount);
			} else {
				team.not_available = true;
			}

			team.due = due;
		});
	}

	var getConfirmMsg = function (team) {
		var msg = 'Are you sure you want to change payment to "' + team.team_name + '"?';
		var discNotif;

		if (!!team.discount_diff) {
			discNotif = 
				'Discount of $' + team.discount_diff + discountMsg;
		} else {
			discNotif = '';
		}

		return (msg + ' ' + discNotif);

	}

	this.teamCls = function (team) {
		return {
			'list-group-item clearfix': true,
			'pointer': !team.not_available,
			'text-grey': team.not_available
		}
	}
	
	this.changeTeam = function (team) {
		if (team.not_available) {
			return;
		}

		var confirmMsg = getConfirmMsg(team);

		ConfirmationService.ask(confirmMsg, {
			title 			: 'Confirm Payment Change:',
			disableNoBtn 	: true
		}).then(function (answer) {
			if (answer === ConfirmationService.YES_RESP) {
				this.replaceTeam({
					team_id 		: team.roster_team_id, 
					name 			: team.team_name,
					discount_diff 	: team.discount_diff,
					reg_fee  		: purchaseService.getTeamRegistrationFee(
																	team.div_reg_fee, eventRegFee)
				});
			}
		}.bind(this));
	}

	this.$onChanges = function (changes) {
		var amount 		= changes.amount && changes.amount.currentValue;
		var teams 		= changes.teams && changes.teams.currentValue;
		var discount 	= changes.discount && changes.discount.currentValue;

		if (Array.isArray(teams) || _.isNumber(amount) || _.isNumber(discount)) {

			amount 	 	= amount || this.amount;
			teams 	 	= teams || this.teams;
			discount  	= discount || this.discount;

			setTeamsAvailability(amount, discount, eventRegFee, eventSurcharge, teams);
		}
	}

}
