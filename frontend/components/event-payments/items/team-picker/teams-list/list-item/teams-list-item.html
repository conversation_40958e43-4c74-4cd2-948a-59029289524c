<div class="col-2">
	<genders 
	m="$ctrl.team.gender !== 'female'"
	f="$ctrl.team.gender !== 'male'"
	></genders>
</div>

<div ng-class="$ctrl.teamNameCls()" ng-bind="$ctrl.team.team_name"></div>

<small class="pull-right">
	<span ng-if="$ctrl.team.not_available">
		Due <span ng-bind="$ctrl.team.due | currency"></span> is below <span ng-bind="$ctrl.amount | currency"></span>
	</span>

	<span ng-if="!$ctrl.team.not_available && ($ctrl.team.discount_diff > 0)">
		<span ng-bind="$ctrl.amount | currency"></span> - <b ng-bind="$ctrl.team.discount_diff | currency"></b> discount
	</span>

	<span ng-if="!$ctrl.team.not_available && ($ctrl.team.discount_diff === 0)" ng-bind="$ctrl.team.due | currency"></span>
</small>