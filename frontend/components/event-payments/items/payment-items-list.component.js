angular.module('SportWrench').component('paymentItemsList', {
	templateUrl : 'components/event-payments/items/payment-items-list.html',
	bindings 	: {
		items 		 	: '<',
		type 		 	: '<paymentType',
		status  		: '<paymentStatus',
		isCanceled 		: '<',
		onItemCancel 	: '&onCancel',
		onTeamChange 	: '&?',
	},
	controller  : ['toastr', PaymentItemsListController]
})

function PaymentItemsListController (toastr) {
	var self = this;
	var changeTeamInPurchase = angular.isFunction(this.onTeamChange) && this.onTeamChange();

	this.teamChangeAvailable = angular.isFunction(changeTeamInPurchase);
	
	this.rowCls = function (item) {
		return {
			'text-grayout': !!item.canceled
		}
	}

	this.showVoidBtn = function (item) {
		return (this.type === 'check') && !item.canceled;
	}

	this.voidBtnText = function () {
		return (this.status === 'paid') ? 'Refund' : 'Cancel'
	}

	this.voidConfirmText = function (item) {
		var msg = ['Are you sure you want to cancel team ' + item.team_name + '?'];

		if (this.status === 'paid') {
			msg.push('Invoice Amount will be refunded.', 'Received amount will not be changed.');
		} else {
			msg.push('Invoice Amount will be decreased');
		}

		return msg.join(' ');
	}

	this.cancelItem = function (confirmResult, item) {
		if (!confirmResult) {
			return;
		}

		self.onItemCancel({ item: item })
		.then(function () {
			item.canceled = true;
		})
	}

	this.showChangeTeamPicker = function (team) {
		return this.teamChangeAvailable && !team.canceled;
	}

	this.replaceTeam = function (
			purchaseTeamID, oldRosterTeamID, rosterTeamID, rosterTeamName, discountDiff, regFee) {

		if (this.teamChangeAvailable) {
			return changeTeamInPurchase(purchaseTeamID, oldRosterTeamID, rosterTeamID)
			.then(function () {

				var oldTeamName = findAndReplaceTeamData(
					this.items, purchaseTeamID, oldRosterTeamID, 
						rosterTeamID, rosterTeamName, discountDiff, regFee);

				toastr.success(
							'Team "' + oldTeamName + '" was switched to "' + rosterTeamName + '"');

			}.bind(this))
		}

	}

	/**
	* When we replaced a team, we need to find it in the list (table of purchase's items)
	* and replace it's "roster_team_id" and "team_name"
	**/
	var findAndReplaceTeamData = function (
		teams, purchaseTeamID, oldRosterTeamID, rosterTeamID, 
		rosterTeamName, discountDiff, regFee) {

		for (var i = 0, team; i < teams.length; ++i) {
			team = teams[i];

			if (team.purchase_team_id === purchaseTeamID && 
				team.roster_team_id === oldRosterTeamID
			 ) {
			 	var oldName = team.team_name;

				team.roster_team_id = rosterTeamID;
				team.team_name 		= rosterTeamName;
				team.reg_fee 		= regFee;

				/* Difference between amounts now become a discount */
				if (discountDiff >= 0) {
					team.discount = discountDiff;
				}
				
				return oldName;
			}
		}

		return null;
	}

}