angular.module('SportWrench').directive('paymentsDetails', [
    'eventPaymentsService',
    'purchaseService',
    '$stateParams',
    'toastr',
    'UtilsService',
    'DONE_MSG',
    '$http',
    '$q',
    'PAYMENT_TYPE',
    'PAYMENT_STATUS',
    paymentsDetailsDirective
])

function paymentsDetailsDirective (eventPaymentsService, purchaseService, $stateParams, toastr, UtilsService, DONE_MSG, $http, $q, PAYMENT_TYPE, PAYMENT_STATUS) {
    return {
        restrict: 'E',
        scope: {
            payments            : '<',
            cancelPayment       : '&',
            changedData         : '&?',
            loadUnpaidTeams     : '&?',
            enableTeamChange    : '&',
            paymentIdToOpen     : '<'
        },
        templateUrl: 'components/event-payments/payments-details.html',
        link: function PaymentsDetailsLink (scope) {
            let eventID = $stateParams.event;
            let isTeamChangeAvailable = angular.isFunction(scope.enableTeamChange)
                                                            && (scope.enableTeamChange() === true);

            let unwatch = scope.$watchCollection('payments', function(newVal, oldVal){
                if (newVal && _.isArray(newVal)) {
                    if(scope.paymentIdToOpen) {
                        scope.payments = scope.payments.map(p => {
                            if(p.purchase_id === scope.paymentIdToOpen) {
                                p.is_open = true;

                                scope.get_payment_details(p);
                            }

                            return p;
                        })
                    }
                    unwatch();
                }
            });

            let refreshPayment = function (payment, eventID, purchaseID) {
                return eventPaymentsService.getPaymentState(eventID, purchaseID)
                .then(function (data) {
                  Object.keys(data).forEach( function (key) {
                        payment[key] = data[key];
                    });
                  scope.isRefundPending = false;
                })
            };

            //changedData can be not defined
            if(!_.isFunction(scope.changedData)) {
                scope.changedData = function () {};
            }

            scope.confirmParams = { disableNoBtn: true };

            scope.save_note = function (note, paymentID) {
                return eventPaymentsService.save_note($stateParams.event, paymentID, note)
                .then(function () {
                    toastr.success('Saved!');
                })
            };

            scope.refund = function (isConfirmed, payment) {
                scope.isRefundPending = true;
                var purchaseID = payment.purchase_id;
                purchaseService.refund(eventID, purchaseID, function () {
                    let changedTeams = payment.teams.map(t => t.roster_team_id);

                    scope.changedData({teams: changedTeams});

                    return refreshPayment(payment, eventID, purchaseID)
                })
            }       

            scope.get_payment_details = function (payment) {
                if (payment.is_open) {
                    getPaymentTeams(payment.purchase_id).then(function (teams) {
                        payment.teams = teams;
                    });
                } else {
                    payment.teams = null;
                }                
            }      

            var markPaymentAsPaid = function (purchaseID) {
                for (var i = 0; i < scope.payments.length; ++i) {
                    if (scope.payments[i].purchase_id === purchaseID) {

                        scope.payments[i].teams.forEach(function (team) {
                            if (!team.canceled) {
                                team.status_paid = 22;
                            }
                        });

                        return scope.payments[i];
                    }
                }
            } 

            scope.receiveCheck = function (data, id) {
                data.purchase_id = id;      

                return eventPaymentsService.receive(
                    $stateParams.event, data.purchase_id, 
                    data.received_at, data.check_num,
                    data.received_amount
                ).then(function () {
                    toastr.success(DONE_MSG);       

                    let payment      = markPaymentAsPaid(id);
                    let changedTeams = payment.teams.map(t => t.roster_team_id);

                    scope.changedData({teams: changedTeams});


                    return refreshPayment(payment, eventID, id)
                })
            }       

            scope.setCurrentDate = function (payment) {
                if(payment.received && !payment.date_paid) {
                    payment.date_paid = new Date();
                }
            }       

            scope.cancel = function (res, payment) {
                if (!res) return;       

                return scope.cancelPayment({
                    id: payment.purchase_id
                }).then(function () {
                    let changedTeams = payment.teams.map(t => t.roster_team_id);

                    scope.changedData({teams: changedTeams});

                    return refreshPayment(payment, eventID, payment.purchase_id)
                })
            };      

            scope.cancelTeam = function (team, payment) {
                return scope.cancelPayment({
                    id      : payment.purchase_id,
                    items   : [team.purchase_team_id]
                }).then(function () {
                    scope.changedData({teams: [team.roster_team_id]});

                    return refreshPayment(payment, eventID, payment.purchase_id)
                })
            }        

            scope.changeTeam = function (payment) {

                var purchaseID = payment.purchase_id;

                if (isTeamChangeAvailable) {
                    return function (purchaseTeamID, oldRosterTeamID, rosterTeamID) {
                        return purchaseService.changeTeamInPayment($stateParams.event, purchaseID, purchaseTeamID, {
                            old_team_id     : oldRosterTeamID,
                            team_id         : rosterTeamID
                        })
                        .then(function () {
                            scope.changedData({teams: [oldRosterTeamID, rosterTeamID]});

                            return refreshPayment(payment, eventID, purchaseID);
                        });
                    }
                } else {
                    return false;
                }
            }   

            scope.partialRefundMenu = function (payment) {
                payment.show_partial_refund = true;
            }       

            scope.hide_partial_refund_menu = function (payment) {
                payment.show_partial_refund = false;
                payment.refund_teams = undefined;
            }       

            scope.save_partial_refund = function (payment, teams) {
                return purchaseService.makePartialRefund($stateParams.event, payment.purchase_id, payment, teams)
                .then(function () {

                    var listPayment = findPayment(payment.purchase_id);     

                    getPaymentTeams(listPayment.purchase_id).then(function (teams) {
                        listPayment.teams = teams;
                        scope.hide_partial_refund_menu(listPayment);
                    });

                    let changedTeams = teams.map(t => t.roster_team_id);

                    scope.changedData({teams: changedTeams});

                    refreshPayment(listPayment, eventID, payment.purchase_id);
                })
            }       

            scope.openReceipt = function (id) {
                return purchaseService.openReceipt(id);
            }

            scope.showRefundButton = function (payment) {
                const isCardPayment = payment.type === PAYMENT_TYPE.CARD;
                const isACHPayment = payment.type === PAYMENT_TYPE.ACH;
                const isPendingACHPayment = payment.status === PAYMENT_STATUS.PENDING && isACHPayment;
                const notCanceledPayment = payment.status !== PAYMENT_STATUS.CANCELED;

                return (isCardPayment || isACHPayment) && !isPendingACHPayment && notCanceledPayment;
            }

            scope.showReceiveCheckForm = function({ status, type }) {
                return type === PAYMENT_TYPE.CHECK && status !== PAYMENT_STATUS.CANCELED;
            }

            function findPayment (purchaseID) {
                for (var i = 0; i < scope.payments.length; ++i) {
                    if (purchaseID === scope.payments[i].purchase_id) {
                        return scope.payments[i]
                    }
                }       

                return null;
            }       

            function getPaymentTeams (purchaseID) {
                var defer = $q.defer();     

                eventPaymentsService.paymentTeams($stateParams.event, purchaseID, function (resp) {
                    defer.resolve(resp.data.payment_teams);
                        
                });     

                return defer.promise;
            }
        },
        controller: [
            '$scope', '$http', 'purchaseService', 'eventPaymentsService', '$stateParams', 'DONE_MSG', 'toastr',
            'UtilsService',  '$q',
            PaymentDetailsController
        ]
    }
}

function PaymentDetailsController ($scope) {
   this.loadUnpaidTeams = $scope.loadUnpaidTeams;
}
