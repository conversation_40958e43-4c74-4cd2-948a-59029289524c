<form 
    class="form-inline row-space receive-check-form"
    name="$ctrl.receiveForm"
    ng-submit="$ctrl.onOKClick()" 
    novalidate>

    <div class="form-group">
        <label class="checkbox pointer">
            Received
            <input 
                type="checkbox" 
                name="is_received" 
                ng-model="$ctrl.enableCheckForm" 
                ng-change="$ctrl.receivedPickerToggle()">
        </label>
    </div>

    <div ng-class="{ 'form-group w140': true, 'has-error': $ctrl.showErrorCls('received_at') }">
        <div class="input-group date-time-control">
            <input 
                uib-datepicker-popup="MM/dd/yyyy" 
                is-open="$ctrl.datePicker.isOpen" 
                ng-model="$ctrl.payment.received_at" 
                name="received_at" 
                ng-class="{ 'form-control': true, 'white-ro': $ctrl.enableCheckForm }" 
                required 
                ng-click="$ctrl.toggleDatePicker()" 
                readonly 
                placeholder="mm/dd/yyyy" 
                ng-change="$ctrl.onDataChanged()"
                ng-disabled="!$ctrl.enableCheckForm"
                required>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>

    <div ng-class="{ 'form-group w140': true, 'has-error': $ctrl.showErrorCls('check_num') }">
        <div class="input-group">
            <span class="input-group-addon">&nbsp;#&nbsp;</span>
            <input 
                ng-model="$ctrl.payment.check_num" 
                name="check_num"
                class="form-control" 
                maxlength="50" 
                placeholder="Check number" 
                ng-change="$ctrl.onDataChanged()"
                ng-disabled="!$ctrl.enableCheckForm"
                required>
        </div>
    </div>

    <div ng-class="{ 'form-group w115': true, 'has-error': $ctrl.showErrorCls('amount') }">
        <div class="input-group">
            <span class="input-group-addon">&nbsp;$&nbsp;</span>
            <input
                type="number"
                ng-model="$ctrl.receivedAmount"
                name="amount"
                class="form-control" 
                ng-change="$ctrl.onDataChanged()"
                ng-disabled="!$ctrl.enableCheckForm"
                greater-than-zero
                required>
        </div>
    </div>

    <button 
        type="submit"
        ng-if="$ctrl.showOKBtn"
        class="btn btn-primary" 
        ng-disabled="!$ctrl.enableCheckForm"
    >Save</button>
</form>

