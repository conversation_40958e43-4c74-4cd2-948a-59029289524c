angular.module('SportWrench').component('paymentNote', {
	templateUrl: 'components/event-payments/payment-note/note.html',
	bindings: {
		saveNote 	: '&',
		paymentNote : '='
	},
	controller: [PaymentNoteController]
});

function PaymentNoteController () {
	var initialNote;

	this.isNoteChanged = false;

	this.onNoteChange = function () {
		this.isNoteChanged = (initialNote !== this.paymentNote);
	}

	this.save = function () {
		this.saveNote({ note: this.paymentNote })
		.then(function () {
			initialNote = this.paymentNote;
			this.isNoteChanged = false;
		}.bind(this))
	}

	this.$onInit = function () {
		initialNote = this.paymentNote || '';
	}
}