<span ng-class="$ctrl.headingClass()" sgclick="$ctrl.getDetails({ payment: $ctrl.p })">
    <b>
    	<span>#{{$ctrl.p.purchase_id}}</span>
    	<span>-</span>
    	<span>{{$ctrl.p.type | uppercase}} ({{$ctrl.getPaymentStatus()}})</span>
    	<span>&nbsp;</span>
    	<span>{{$ctrl.p.created | UTCdate: 'MM/DD h:mma'}}</span>
    </b>

	
	<span class="ml7" ng-if="$ctrl.isStripe()">
		<b>{{$ctrl.p.amount | currency}}</b>
		<span class="ml7" ng-if="$ctrl.p.date_refunded"><b>Ref.:</b> {{$ctrl.p.amount_refunded | currency}} ({{$ctrl.p.date_refunded | UTCdate: 'MM/DD'}})</span>
	</span>
    

    <span class="ml7" ng-if="!$ctrl.isStripe()">
    	<span><b>Inv.:</b> {{$ctrl.p.amount | currency}}</span>
    	<span class="ml7" ng-if="$ctrl.showCheckRefundBlock()"><b>Ref.:</b> {{$ctrl.p.amount_refunded | currency}} {{$ctrl.p.date_refunded | UTCdate: 'MM/DD'}}</span>
    	<span class="ml7" ng-if="$ctrl.p.date_paid"><b>Rcv.:</b> {{$ctrl.p.received_amount | currency}} #{{$ctrl.p.check_num}} {{$ctrl.p.date_paid | UTCdate: 'MM/DD'}}</span>
    </span>

    <span class="ml7" ng-if="$ctrl.p.canceled_date"><b>CX:</b> {{$ctrl.p.canceled_date | UTCdate: 'MM/DD'}}</span>
</span>
