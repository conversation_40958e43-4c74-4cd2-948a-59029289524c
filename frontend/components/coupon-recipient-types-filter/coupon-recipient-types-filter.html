<div class="form-group">
    <div class="col-sm-offset-1 col-sm-10">
        <label class="control-label">Recipients</label>
        <div ng-repeat="recipientType in $ctrl.EMAIL_RECIPIENT_TYPES">
            <label class="checkbox-inline">
                <input
                        type="checkbox"
                        ng-model="recipientType.checked"
                        class="ng-pristine ng-untouched ng-valid ng-not-empty"
                        ng-change="$ctrl.onCheckboxChange()"
                />
                {{recipientType.title}}
            </label>
        </div>
    </div>
</div>
