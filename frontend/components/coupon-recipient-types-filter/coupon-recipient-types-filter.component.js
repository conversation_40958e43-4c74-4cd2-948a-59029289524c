class CouponRecipientTypesFilterComponent {
    constructor(eventDashboardService) {
        this.eventDashboardService = eventDashboardService;
        this.inProcess = false;
    }

    async $onInit() {
        this.EMAIL_RECIPIENT_TYPES = [];
        const event = await this.eventDashboardService.getEvent();
        this.EMAIL_RECIPIENT_TYPES = [
            {
                id: 'club_director',
                title: 'Club Director',
                is_visible: !event.is_with_manual_teams_addition
            },
            {
                id: 'head_coach',
                title: 'Head Coach',
                is_visible: true
            },
        ]
            .filter(v => v.is_visible)
            .map(v => Object.assign({}, v, {checked: false}));
    }

    onCheckboxChange() {
        const value = this.EMAIL_RECIPIENT_TYPES.filter(v => v.checked).map(v => v.id);
        this.onChange({value});
    }
}

angular.module('SportWrench').component('couponRecipientTypesFilterComponent', {
    templateUrl: 'components/coupon-recipient-types-filter/coupon-recipient-types-filter.html',
    bindings: {
        onChange: '&',
    },
    controller: [
        'eventDashboardService',
        CouponRecipientTypesFilterComponent,
    ]
});

