angular.module('SportWrench').component('manualAddTeamForm', {
    templateUrl: 'components/manual-add-team/manual-add-team-form/manual-add-team-form.html',
    bindings: {
        divisions           : '<',
        close               : '&',
        saveData            : '&',
        hasManualClubNames  : '<'
    },
    controller: ManualAddTeamForm
});

ManualAddTeamForm.$inject = ['$scope', 'ENTRY_STATUS', '$stateParams', 'toastr'];

function ManualAddTeamForm($scope, ENTRY_STATUS, $stateParams, toastr) {
    let self = this;

    let team = {
        division_id : null,
        team_name   : ''
    };

    this.entryStatuses  = ENTRY_STATUS;
    this.teams          = [Object.assign({}, team)];

    this.addMore = function () {
        self.teams.push(Object.assign({}, team));
    };

    this.deleteRow = function (index) {
        self.teams.splice(index, 1);
    };

    $scope.$on('ManualAddTeamFormSubmitted', function () {
        self.teamsForm.$setSubmitted();

        if(self.teamsForm.$invalid) {
            toastr.warning('Not all data filled')
        } else {
            let data = {
                statusEntry : self.status_entry,
                teams       : self.teams
            };

            return self.saveData({data}).then(() => toastr.success('Success'))
        }
    });

    this.getTeamNameClass = function () {
        return this.hasManualClubNames
            ? { 'col-xs-4': true, 'text-center': true, 'team-name-input-header': true }
            : { 'col-xs-5': true, 'col-xs-offset-1': true, 'text-center': true }
    };

    this.getDivisionClass = function () {
        return this.hasManualClubNames
            ? { 'col-xs-3': true, 'text-center': true, 'division-input-header': true }
            : { 'col-xs-5': true, 'text-center': true }
    }
}
