<form name="$ctrl.teamsForm" novalidate class="manual-add-team-form">
    <div class="row">
        <div ng-class="{'col-xs-6 col-xs-offset-3': true,
        'has-error': $ctrl.teamsForm.$submitted && $ctrl.teamsForm.status_entry.$invalid }">
            <label class="control-label">Entry Status</label>
            <select class="form-control" name="status_entry"
                    ng-model="$ctrl.status_entry"
                    ng-options="s.id as s.title for s in $ctrl.entryStatuses"
                    required
            >
                <option value="" selected>Select Status...</option>
            </select>
        </div>
    </div>

    <hr/>

    <div class="row">
        <div ng-class="$ctrl.getTeamNameClass()"><h4>Team Name</h4></div>
        <div ng-if="$ctrl.hasManualClubNames" class="col-xs-4 text-center"><h4>Club Name</h4></div>
        <div ng-class="$ctrl.getDivisionClass()"><h4>Division</h4></div>
    </div>

    <add-team-row
        ng-repeat="team in $ctrl.teams track by $index"
        index="$index + 1"
        team="$ctrl.teams[$index]"
        divisions="$ctrl.divisions"
        delete="$ctrl.deleteRow($index)"
        parent-form="$ctrl.teamsForm"
        hide-club-name="!$ctrl.hasManualClubNames"
    ></add-team-row>

    <div class="row-space"></div>

    <div class="row">
        <div class="col-xs-2 mt-30">
            <button type="button" class="btn btn-primary" ng-click="$ctrl.addMore()">Add More</button>
        </div>
    </div>
</form>
