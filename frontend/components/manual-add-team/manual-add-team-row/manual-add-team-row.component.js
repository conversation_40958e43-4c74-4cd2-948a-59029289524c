angular.module('SportWrench').component('addTeamRow', {
    templateUrl: 'components/manual-add-team/manual-add-team-row/manual-add-team-row.html',
    bindings: {
        divisions   : '<',
        team        : '=',
        index       : '<',
        delete      : '&',
        validate    : '<',
        parentForm  : '<',
        hideClubName: '<'
    },
    controller: function () {
        this.rowCounterClass = function () {
            let classes = {
                'add-team-row-counter'  : true,
                'col-xs-1'              : true
            };

            return !this.hideClubName
                ? Object.assign(classes, { 'width-10': true })
                : classes;
        };

        this.deleteTeamRowClass = function () {
            let classes = {
                'delete-team-row'   : true,
                'col-xs-1'          : true
            };

            return !this.hideClubName
                ? Object.assign(classes, { 'width-10': true, 'padding-l-0': true })
                : classes;
        };

        this.getTeamNameClass = function (form) {
            let classes = {
                'has-error' :  (this.parentForm.$submitted && form.team_name.$invalid)
            };

            return !this.hideClubName
                ? Object.assign(classes, { 'col-xs-4': true })
                : Object.assign(classes, { 'col-xs-5': true });
        };

        this.getDivisionIDClass = function (form) {
            let classes = {
                'has-error' :  (this.parentForm.$submitted && form.division_id.$invalid)
            };

            return !this.hideClubName
                ? Object.assign(classes, { 'col-xs-3': true , 'division-header': true })
                : Object.assign(classes, { 'col-xs-5': true });
        }

        this.updateMembersList = function (members) {
            this.team.members = angular.copy(members);
        }
    }
});
