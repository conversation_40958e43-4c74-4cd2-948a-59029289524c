<ng-form name="teams_data">
    <div class="row add-team-row">
        <div ng-class="$ctrl.rowCounterClass()">
            <label>{{$ctrl.index}}</label>
        </div>
        <div ng-class="$ctrl.getTeamNameClass(teams_data)">
            <input type="text"
                   name="team_name"
                   required ng-model="$ctrl.team.team_name"
                   class="form-control"
                   placeholder="Team Name">
        </div>
        <div ng-class="{
            'col-xs-4': true,
            'has-error': ($ctrl.parentForm.$submitted && teams_data.manual_club_name.$invalid)}"
             ng-if="!$ctrl.hideClubName"
        >
            <input type="text"
                   name="manual_club_name"
                   ng-model="$ctrl.team.manual_club_name"
                   class="form-control"
                   placeholder="Club Name">
        </div>
        <div ng-class="$ctrl.getDivisionIDClass(teams_data)"
        >
            <select class="form-control" name="division_id"
                    ng-model="$ctrl.team.division_id"
                    name="division_id"
                    ng-options="d.division_id as d.name for d in $ctrl.divisions"
                    required
            >
                <option value="" selected>Division...</option>
            </select>
        </div>
        <div ng-if="$ctrl.index > 1"
             ng-class="$ctrl.deleteTeamRowClass()"
             ng-click="$ctrl.delete({ index: $ctrl.index })"
        >
            <span class="gl-danger fa fa-times text-danger"></span>
        </div>
    </div>
</ng-form>
<manual-add-member on-members-change="$ctrl.updateMembersList(members)"></manual-add-member>
