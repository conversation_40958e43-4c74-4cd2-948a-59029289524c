
class Controller {
    constructor (STAFF_ROLES) {
        this.STAFF_ROLES = STAFF_ROLES.filter(r => r.id === 4);
    }

    $onInit () {
        this.member = {};

        this.memberTypes = [{
            id: 'staff',
            label: 'Staff'
        }];

        this.staffRoles = this.STAFF_ROLES;
    }

    addMember () {
        if(this.manualMemberForm.$invalid) {
            return;
        }

        this.onSave({ member: this.member })
            .then(() => this.member = {})
            .catch(() => {});
    }

    disableAddButton () {
        return this.manualMemberForm.$invalid;
    }

    typeChanged () {
        if(this.member && this.member.type !== 'staff') {
            if(this.member.staff_role) {
                delete this.member.staff_role;
            }
        }
    }

    showRoleSelector () {
        return this.member.type === 'staff';
    }

    hasError (fieldName) {
        return this.manualMemberForm.$submitted && this.manualMemberForm[fieldName].$invalid;
    }
}

Controller.$inject = ['STAFF_ROLES'];

angular.module('SportWrench').component('manualAddMemberForm', {
    templateUrl: 'components/manual-add-team/manual-add-member/manual-add-member-form/template.html',
    bindings: {
        onSave: '&'
    },
    controller: Controller
});
