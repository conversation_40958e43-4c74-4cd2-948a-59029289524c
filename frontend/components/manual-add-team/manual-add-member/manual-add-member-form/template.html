<div class="row manual-add-member-form">
    <div class="col-xs-10 col-xs-offset-1">
        <div class="panel panel-default">
            <form class="form form-horizontal" name="$ctrl.manualMemberForm" novalidate>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError('member_type') }">
                    <label class="col-sm-3 control-label">Type</label>
                    <div class="col-sm-7">
                        <select class="form-control"
                                ng-model="$ctrl.member.type"
                                name="member_type"
                                ng-options="t.id as t.label for t in $ctrl.memberTypes"
                                ng-change="$ctrl.typeChanged()"
                                required>
                            <option value="" selected>Select Member Type ...</option>
                        </select>
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError('first') }">
                    <label class="col-sm-3 control-label">First</label>
                    <div class="col-sm-7">
                        <input type="text" name="first" class="form-control" ng-model="$ctrl.member.first" required>
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError('last') }">
                    <label class="col-sm-3 control-label">Last</label>
                    <div class="col-sm-7">
                        <input type="text" name="last" class="form-control" ng-model="$ctrl.member.last" required>
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError('email') }">
                    <label class="col-sm-3 control-label">Email</label>
                    <div class="col-sm-7">
                        <input type="email"
                               name="email"
                               class="form-control"
                               ng-model="$ctrl.member.email"
                               email-validator
                               required>
                    </div>
                </div>
                <div ng-if="$ctrl.showRoleSelector()"
                     ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError('staff_role') }">
                    <label class="col-sm-3 control-label">Role</label>
                    <div class="col-sm-7">
                        <select class="form-control"
                                ng-model="$ctrl.member.staff_role"
                                name="staff_role"
                                ng-options="r.id as r.name for r in $ctrl.staffRoles"
                                required>
                            <option value="" selected>Select Staff Role ...</option>
                        </select>
                    </div>
                </div>
                <button class="btn btn-info"
                        type="submit"
                        ng-click="$ctrl.addMember()"
                >Add</button>
            </form>
        </div>
    </div>
</div>
