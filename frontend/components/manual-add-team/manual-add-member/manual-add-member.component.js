
class Controller {
    constructor (toastr) {
        this.toastr = toastr;
        this.members = [];

        this.isAdditionMenuOpen = false;
    }

    removeMember () {
        this.members = [];
    }

    toggleAdditionMenu () {
        this.isAdditionMenuOpen = !this.isAdditionMenuOpen;
    }

    getToggleButtonClass () {
        return this.isAdditionMenuOpen
            ? 'fa fa-caret-square-o-right'
            : 'fa fa-caret-square-o-down';
    }

    async addMember (member) {
        if(!this.members.length) {
            this.members.push(member);

            this.isAdditionMenuOpen = false;
        } else {
            this.toastr.warning('Only one member can be added for now');

            throw new Error('To many members');
        }

        this.onMembersChange({ members: this.members });
    }
}

Controller.$inject = ['toastr'];

angular.module('SportWrench').component('manualAddMember', {
    templateUrl: 'components/manual-add-team/manual-add-member/manual-add-member.html',
    bindings: {
        onMembersChange: '&'
    },
    controller: Controller
});
