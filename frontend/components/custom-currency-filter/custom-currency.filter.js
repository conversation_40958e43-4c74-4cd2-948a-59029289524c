/**
 * Format negative currency numbers with parentheses (like ($nnn))
 */
angular.module('SportWrench')
.filter('pCurrency', ['$filter', function ($filter) {
    let currencyFilter = $filter('currency');

    return function pCurrencyFilter (amount, currencySymbol, fractionSize) {
        let useParentheses = true;
        return currencyFilter(amount, currencySymbol, fractionSize, useParentheses);
    }
}])
