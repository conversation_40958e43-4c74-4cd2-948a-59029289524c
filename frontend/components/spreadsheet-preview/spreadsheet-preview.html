<div class="spreadsheet-preview">
    <uib-tabset>
        <uib-tab ng-repeat="sheet in workbook" heading="{{sheet.title}}">
            <div style="overflow: auto">
                <table>
                    <tr>
                        <th></th>
                        <th ng-repeat="col in sheet.cols">{{col}}</td>
                    </tr>
                    <tr ng-repeat="row in sheet.rows">
                        <th>{{$index + 1}}</td>
                        <td ng-repeat="cell in row track by cell.a1">{{cell.value}}</td>
                    </tr>
                </table>
            </div>
        </uib-tab>
    </uib-tabset>
</div>
