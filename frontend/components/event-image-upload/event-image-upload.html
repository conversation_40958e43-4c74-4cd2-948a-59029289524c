<div class="event-image-upload-wrapper">
    <div class="row">
        <div class="col-sm-12">
            <h3>{{imageData.title}}:</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <div class="row file-upload-wrapper">
                <div class="col-sm-8">
                    <div class="file-upload-input">
                        <input
                            type="file"
                            id="{{inputId}}"
                            ng-model="fileToUpload"
                            file-change="onChange()"
                        >
                    </div>
                    <p class="restrictions">
                        Requires PNG or JPG images ({{convertToMB(imageData.size)}}MB max).
                    </p>
                    <p
                        ng-if="errorMessage"
                        class="alert alert-danger text-center file-upload-error-message"
                    >
                        {{errorMessage}}
                    </p>
                </div>
                <div class="col-sm-4 file-upload-action-buttons">
                    <button
                        ng-if="image.path && !image.remove"
                        type="submit"
                        class="btn btn-danger upload-image-btn"
                        sw-confirm="Do you really want to remove uploaded image?"
                        sw-confirm-do="onRemove"
                        sw-confirm-hide-no
                    >
                        Remove
                    </button>
                </div>
            </div>
        </div>
        <div ng-if="image.path && !image.remove" class="col-sm-6 image-preview-wrapper">
            <img class="image-preview-item" ng-class="{'full-width': isCoverImage }" ng-src="{{apiHost + '' + image.path}}">
        </div>
    </div>
</div>
