angular.module('SportWrench').directive('eventImageUpload', function (
    HOME_PAGE_URL, UtilsService
) {
    return {
        restrict: 'E',
        scope: {
            imageData: '<',
            eventId: '<',
            image: '=',
            fileToUpload: '='
        },
        require: '^imagesUploadForm',
        templateUrl: 'components/event-image-upload/event-image-upload.html',
        link: function(scope, attrs, elem, ctrl) {
            const ALLOWED_TYPES = [
                'image/png',
                'image/jpeg',
            ];
            scope.apiHost = HOME_PAGE_URL;

            scope.inputId      = `fileToUpload-${scope.imageData.type}`;
            scope.errorMessage = '';
            scope.isCoverImage = scope.imageData.type === 'cover-image';

            scope.onChange = () => {
                scope.errorMessage = '';

                if (scope.fileToUpload) {
                    validateFile();
                }
            };

            scope.convertToMB = function (value) {
                return UtilsService.getNextBinaryPrefixValue(value);
            };
            scope.onRemove = () => {
                if(scope.image && scope.image.id)
                    scope.image.remove = true
                
                clearInput()
            };

            const validateFile = () => {
                const isAllowType   = ALLOWED_TYPES.some(type => type === scope.fileToUpload.type);
                const fileSizeInKB  = UtilsService.getNextBinaryPrefixValue(scope.fileToUpload.size);

                if (!isAllowType) {
                    setError('Allowable PNG or JPEG types');

                    return;
                }

                if (fileSizeInKB > scope.imageData.size) {
                    setError(`Maximum size for this image type is ${
                            UtilsService.getNextBinaryPrefixValue(scope.imageData.size)}MB`);
                }
            };

            const clearInput = () => {
                angular.element(`#${scope.inputId}`).val(null);
                scope.fileToUpload = null;
            };

            const setError = (errorMessage) => {
                scope.errorMessage = errorMessage;

                clearInput();
            };
        }

    }
});
