<!-- TODO: add ng-class where use variable to use or not collapse and media css -->
<!-- TODO #1 Refactor css - add classes for navs -->
<button 
    class="sw-menu-toggle-btn btn btn-default toggle-collapse_btn spacer-sm-b" 
    ng-click="collapseToggle()">
    <i class="fa fa-bars"></i>&nbsp;{{activeStateName}}
</button>
<ul 
    class="collapse-tabs-sw nav nav-tabs {{headerClass}}"
    uib-collapse="isCollapsed()">
    <li 
        ng-repeat="t in tabs"
        ng-if="t.isVisible()"
        ng-class="{ 'active': isActiveTab(t), 'disabled': isDisabledTab(t) }" 
        uib-dropdown
        >            
            <a 
                href=""
                data-toggle="tab" 
                ng-if="!dropdownTab(t) && t.name" 
                ng-click="!isDisabledTab(t) && openTab(t.state, getStateParams(t))"
                >{{ getTabName(t) }}</a>
            <a 
                href=""
                data-toggle="tab" 
                ng-if="!dropdownTab(t) && !t.name"
                ng-click="openTab(t.state, getStateParams(t))"
                ng-include="t.template"></a>
            <a 
                href=""
                data-toggle="tab" 
                class="pointer"
                ng-if="dropdownTab(t)"
                uib-dropdown-toggle
                >{{ getTabName(t) }}&nbsp;<span class="caret"></span></a>

                <ul class="dropdown-menu" role="menu" ng-if="dropdownTab(t)">
                    <li role="menuitem" ng-repeat="s in t.states" ng-class="{'active': isActiveState(s.state)}">
                        <a href=""                              
                            ng-click="openTab(s.state, getStateParams(t))" 
                            ng-bind="s.name"
                            ng-if="!s.isVisible || s.isVisible()">
                        </a>
                    </li>                  
                </ul>
            
    </li>
    <ng-transclude></ng-transclude>
</ul>
<hr class="toggle-collapse_btn" />
