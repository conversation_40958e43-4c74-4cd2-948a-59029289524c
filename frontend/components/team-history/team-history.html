<form class="col-sm-12 form-inline row-space">
    <div class="form-group">  
        <div class="search-box">
            <input
                type="search"
                class="form-control search_teams"
                placeholder="Search ..."
                data-ng-model="utils.search"
                ng-keypress="search_keypress($event)">
            <span class="glyphicon glyphicon-search"></span>
        </div>      
    </div>
    <div class="form-group">
        <select ng-model="filters.type" class="form-control">
            <option value="" selected>Type</option>
            <option value="payment" selected>Payment</option>
            <option value="team" selected>Team</option>
            <option value="club" selected>Club</option>
        </select>
    </div>
    <div class="form-group pointer" ng-click="toggleFrom()"><!--  ng-focus="toggleFrom()" -->
        <div class="input-group">
            <input 
                type="text" 
                ng-model="filters.from" 
                ng-model-options="{debounce: { 'default': 150 }}"
                class="form-control" 
                uib-datepicker-popup="MM/dd/yyyy" 
                max-date="filters.to"
                is-open="utils.from_opened" 
                placeholder="From: MM/DD/YYYY"
                readonly
               >
            <div class="input-group-addon pointer">
                <i class="fa fa-calendar"></i>
            </div>
        </div>
    </div>
    <div class="form-group" ng-click="toggleTo()">
        <div class="input-group">
            <input 
                type="text" 
                ng-model="filters.to" 
                ng-model-options="{debounce: { 'default': 150 }}"
                class="form-control" 
                uib-datepicker-popup="MM/dd/yyyy" 
                min-date="filters.from"
                is-open="utils.to_opened" 
                placeholder="To: MM/DD/YYYY"
                readonly
               >
            <div class="input-group-addon pointer">
                <i class="fa fa-calendar"></i>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label>
            <input type="checkbox" ng-model="filters.notes"> Has notes
        </label>
    </div>
</form>
<table class="table table-condensed event-history" ng-scroll="load()">
    <thead>
        <tr>           
            <th>Created</th>
            <th>Action</th>
            <th>Comments</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="h in history" ng-click="openModal(h)" ng-class="{'pointer': isEmailAction(h)}">
            <td class="created" ng-bind="::h.created"></td>
            <td class="action" ng-bind="::h.action"></td>
            <td class="comments" ng-bind="::h.comments"></td>
            <td class="history-info">{{h.club_name || h.team_name}}</td>
        </tr>
    </tbody>
</table>
