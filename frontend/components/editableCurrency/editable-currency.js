angular.module('SportWrench').directive('editableCurrency', editableCurrency)

const round = (value) => Math.round(value * 100)/100;
const notAllowedValues = ['', '-'];

function editableCurrency() {
	return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ngModel) {
            const prefix = attrs.editableCurrency;

            ngModel.$formatters.push(value => {
                if (!value) {
                    return ''
                } else if (value < 0) {
                    return `-${prefix}${round(Math.abs(value))}`
                } else {
                    return `${prefix}${round(value)}`;
                }
            });

            elem.on('focus', () => {
                const value = ngModel.$viewValue;
                const regex = new RegExp(`[${prefix}]`, 'g');

                ngModel.$viewValue = value.replace(regex, '');
                ngModel.$render();
            });

            elem.on('blur', () => {
                let value = ngModel.$viewValue;

                if (notAllowedValues.includes(value)) {
                    ngModel.$viewValue = value;
                } else if (ngModel.$viewValue < 0) {
                    ngModel.$viewValue = `-${prefix}${round(Math.abs(value))}`
                } else {
                    ngModel.$viewValue = `${prefix}${round(value)}`;
                }

                ngModel.$render();
            })
        }
	}
}