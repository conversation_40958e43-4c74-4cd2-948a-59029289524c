
const US_BANK_ACCOUNT_TYPE = 'us_bank_account';
const ACH_MAPPING = {
    [US_BANK_ACCOUNT_TYPE]: 'ach'
};

const MIN_AMOUNT_FOR_ACH = 100;

const CARD_PAYMENT_SUB_TYPES = ['apple_pay', 'google_pay'];

class Controller {
    constructor(StripePaymentElementService, PaymentHub, teamPaymentService, $scope, $timeout, PAYMENT_TYPE) {
        this.StripePaymentElementService = StripePaymentElementService;
        this.teamPaymentService = teamPaymentService;
        this.$scope = $scope;
        this.$timeout = $timeout;
        this.PaymentHub = PaymentHub;

        this.PAYMENT_TYPE = PAYMENT_TYPE;

        this.CARD_PAYMENT_SUB_TYPES = CARD_PAYMENT_SUB_TYPES;
        this.ACH_PAYMENT_SUB_TYPES = Object.keys(ACH_MAPPING);
        this.ACH_MAPPING = ACH_MAPPING;
    }


    async $onInit() {
        this.paymentHubData = null;
        this.paymentForm = null;
        await this.__createPaymentHubPaymentIntent();

        this.paymentType = null;
        this.paymentReadyForConfirmation = false;
        this.hidePaymentForm = false;
        this.minAmountForSelectedPaymentMethod = 0;
        this.selectedCard = null;
    }

    async createPaymentElement(paymentIntentId) {
        const CONTAINER_ID = 'payment-hub-form'
        this.paymentForm = await this.PaymentHub.createForm(paymentIntentId, {
            containerElementId: CONTAINER_ID,
            onChange: ({ isValid, paymentMethodType }) => {
                if (isValid) {
                    this.paymentReadyForConfirmation = isValid;
                    this.$scope.$digest();
                }
                this.__handlePaymentTypeChange(paymentMethodType);
            },
        });
    }

    __handlePaymentTypeChange(paymentType) {
        if (this.CARD_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.PAYMENT_TYPE.CARD;
        }

        if (this.ACH_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.ACH_MAPPING[paymentType];
        }

        if (paymentType !== this.paymentType) {
            this.paymentType = paymentType;

            this.onPaymentTypeChange({ type: paymentType });
        }
    }

    tooSmallAmount() {
        return false;
    }

    submitAllowed() {
        return (
            !this.paymentIsInProgress &&
            this.paymentReadyForConfirmation
        );
    }

    onCardSelect(card) {
        this.selectedCard = card;
        this.$timeout(() => {
            this.onPaymentTypeChange({ type: this.PAYMENT_TYPE.CARD });
        })
    }

    onCardReset() {
        this.selectedCard = null;
        this.$timeout(() => {
            // set payment type to original 
            this.onPaymentTypeChange({ type: this.paymentType });
        })
    }

    async submit() {
        if (!this.submitAllowed()) {
            return;
        }

        this.paymentIsInProgress = true;

        try {
            await this.updatePaymentHubPaymentIntent()

            await this.confirmPayment();

            await this.onPaymentConfirmed({ paymentDataId: this.paymentHubData && this.paymentHubData.paymentIntentId });

            this.$scope.$digest();
        } finally {
            this.paymentIsInProgress = false;
        }
    }

    async confirmPayment() {
        if(this.paymentForm) {
            this.paymentForm.confirmPayment()
        }
    }

    updatePaymentHubPaymentIntent() {
        const paymentData = {
            amount: this.payment.total,
            type: this.payment.type,
            payment_hub_payment_intent_id: this.paymentHubData.paymentIntentId,
        };

        if (this.typeChangePayment && this.typeChangePayment.id) {
            return this.teamPaymentService.changePaymentHubPaymentType(
                angular.extend(paymentData, {
                    purchase_id: this.typeChangePayment.id,
                })
            );
        }

        return this.teamPaymentService.updatePaymentHubPaymentIntent(
            angular.extend(paymentData, { receipt: this.payment.receipt })
        );
    }
    __addError(error) {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = error && error.message;
    }

    __clearError() {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = '';
    }

    async __createPaymentHubPaymentIntent() {
        const data =
            await this.teamPaymentService.createPaymentHubPaymentIntent(
                this.payment.subtotal
            );

        this.paymentHubData = data;

        await this.createPaymentElement(data.paymentIntentId)
    }
}

Controller.$inject = ['StripePaymentElementService', 'PaymentHub', 'teamPaymentService', '$scope', '$timeout', 'PAYMENT_TYPE'];

angular.module('SportWrench').component('paymentHubForm', {
    templateUrl: 'components/payment-hub-form/template.html',
    bindings: {
        payment: '<',
        onPaymentTypeChange: '&',
        returnUrl: '<',
        typeChangePayment: '<',
        cardPaymentEnabled: '<',
        onPaymentConfirmed: '&'
    },
    controller: Controller
});
