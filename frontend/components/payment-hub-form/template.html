<div class="row" ng-if="$ctrl.tooSmallAmount()">
    <uib-alert type="danger">
        <div class="row">
            <div class="col-sm-12 text-center">
                Sorry, selected payment method can be used for payments with min {{$ctrl.minAmountForSelectedPaymentMethod | currency}} amount!
            </div>
        </div>
    </uib-alert>
</div>
<div class="row stripe-card-form" ng-if="!$ctrl.hidePaymentForm">
    <div class="col-xs-12">
        <form id="payment-form">
            <div id="payment-hub-form">
                <!-- Elements will create form elements here -->
            </div>
            <button class="btn" type="submit" ng-click="$ctrl.submit()"
                ng-disabled="!$ctrl.submitAllowed()">
                Pay {{$ctrl.payment.total | currency}}
            </button>
            <div id="error-message">
                <!-- Display error message to your customers here -->
            </div>
        </form>
    </div>
</div>
<div class="row" ng-if="$ctrl.hidePaymentForm">
    <uib-alert type="danger">
        <div class="row">
            <div class="col-sm-12 text-center">Sorry, Online Payments currently unavailable!</div>
        </div>
    </uib-alert>
</div>
