<form ng-class="$ctrl.getFormClass()">
    <div class="form-group"><label>Total: {{$ctrl.totalCount}}</label></div>
    <div ng-if="$ctrl.selectedCount" class="form-group"><label>Selected: {{$ctrl.selectedCount}}</label></div>
    <div ng-if="$ctrl.selectedCount && !$ctrl.showWithdrawn" class="form-group">
        <label class="sr-only">Status: </label>
        <select ng-model="$ctrl.groupWorkStatus" class="form-control">
            <option value="" selected>Select a work status...</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="declined">Declined</option>
            <option value="waitlisted">Wait Listed</option>
        </select>
    </div>
    <button class="btn btn-primary" ng-if="$ctrl.selectedCount && !$ctrl.showWithdrawn" ng-click="$ctrl.onSave()">Save</button>
    <button class="btn btn-warning" ng-if="$ctrl.selectedCount" ng-click="$ctrl.onSendEmail()">Send email</button>
    <button class="btn btn-default" ng-if="$ctrl.selectedCount" ng-click="$ctrl.onExport()">Export</button>
</form>
