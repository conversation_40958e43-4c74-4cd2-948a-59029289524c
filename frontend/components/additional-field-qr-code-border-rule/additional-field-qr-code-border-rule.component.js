

class Controller {
    constructor ($scope) {
        this.scope = $scope;
    }

    $onInit () {
        this.scope.modalTitle = '<h3>Please select options that change QR code border color</h3>';
        this.scope.modalShowClose = false;

        this.checked = this.rule.option_keys.reduce((obj, key) => {
            obj[key] = true;

            return obj;
        }, {});
        this.borderColour = this.rule.border_colour;
    }

    closeAndSave () {
        let checkedValues = this.getSelectedOptions();

        this.form.$setSubmitted();

        if(this.borderColour && !checkedValues.length) {
            this.form.option_key.$invalid = true;
            return;
        }

        if(this.form.$invalid) {
            return;
        }

        this.close({ selected: checkedValues, colour: this.borderColour });
    }

    optionsSelected () {
        const selectedOptions = this.getSelectedOptions();
        return selectedOptions.length;
    }

    getSelectedOptions () {
        return Object.keys(this.checked).filter(key => this.checked[key]);
    }
}

Controller.$inject = ['$scope'];

angular.module('SportWrench').component('additionalFieldQrCodeBorderRule', {
    templateUrl: 'components/additional-field-qr-code-border-rule/additional-field-qr-code-border-rule.html',
    bindings: {
        options: '<',
        rule: '<',
        close: '&'
    },
    controller: Controller
});
