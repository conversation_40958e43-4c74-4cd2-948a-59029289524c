<modal-wrapper>
    <form class="form" name="$ctrl.form">
        <div class="row">
            <div class="col-xs-12">
                <div ng-class="{
                    'form-group': true,
                    'has-error': $ctrl.optionsSelected() && $ctrl.form.$submitted && $ctrl.form.border_colour.$invalid,
                    'validation-required-group': $ctrl.optionsSelected()
                }">
                    <label class="control-label">Select Color</label>
                    <select class="form-control" name="border_colour" ng-model="$ctrl.borderColour" ng-required="$ctrl.optionsSelected()">
                        <option value="" selected>Select...</option>
                        <option value="green">Green</option>
                        <option value="yellow">Yellow</option>
                        <option value="blue">Blue</option>
                        <option value="red">Red</option>
                        <option value="violet">Violet</option>
                    </select>
                </div>
            </div>
            <div class="col-xs-12">
                <div ng-class="{
                    'form-group': true,
                    'has-error': $ctrl.borderColour && $ctrl.form.$submitted && $ctrl.form.option_key.$invalid,
                    'validation-required-group': $ctrl.borderColour
                 }">
                    <label class="control-label">Select Options</label>
                    <div class="checkbox" ng-repeat="(key, value) in $ctrl.options">
                        <label>
                            <input name="option_key" type="checkbox" ng-model="$ctrl.checked[key]"> {{value}}
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <external-button
        class="btn btn-primary"
        ng-click="$ctrl.closeAndSave()"
    >Save
    </external-button>
    <external-button
        class="btn btn-default pull-right"
        ng-click="$ctrl.close()"
    >Close
    </external-button>
</modal-wrapper>
