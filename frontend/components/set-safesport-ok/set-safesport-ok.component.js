angular.module('SportWrench').component('setSafesportOk', {
    templateUrl: 'components/set-safesport-ok/set-safesport-ok.html',
    bindings: {
        adult: '<',
        cancel: '&',
        setOk: '&'
    },
    controller: SetSafesportOkController
});

SetSafesportOkController.$inject = ['$scope', 'STAFF_MEMBER_TYPE', 'SAFESPORT_VALID_STATUS'];

function SetSafesportOkController(scope, STAFF_MEMBER_TYPE, SAFESPORT_VALID_STATUS) {
    let statusId            = this.adult.safesport_statusid;
    let safeSportChanged    = this.adult.safeSportChanged;

    scope.$watch('$ctrl.adult', (adult) => {
        if(adult.safesport_statusid !== statusId) {
            statusId = adult.safesport_statusid;
        }

        if(adult.safeSportChanged !== safeSportChanged) {
            safeSportChanged = adult.safeSportChanged;
        }
    }, true);

    this.getStatusLabel = function () {
        return Number(statusId) === SAFESPORT_VALID_STATUS ? 'OK' : 'NO';
    };

    this.statusIsNotValid = function () {
        return Number(statusId) !== SAFESPORT_VALID_STATUS;
    };

    this.showCancelButton = function () {
        return !this.statusIsNotValid() && safeSportChanged;
    };

    this.cancelSafeSportChange = function () {
        return this.cancel({ adult: this.adult });
    };

    this.setSafeSportOk = function () {
        return this.setOk({ adult: this.adult });
    }
}
