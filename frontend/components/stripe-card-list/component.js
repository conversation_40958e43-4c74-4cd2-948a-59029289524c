class Controller {
    constructor($scope, STRIPE_PAYMENT_TYPE) {
        this.$scope = $scope;
        this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;

        
    }

    $onInit() {
        this.filteredCards = this.cards.filter(({ type })=>type === this.STRIPE_PAYMENT_TYPE.CARD);
    }

    cardSelected(card) {
        return (
            this.selectedCard &&
            this.getLast4(this.selectedCard) === this.getLast4(card)
        );
    }

    getLast4 (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return paymentMethod.card_last_4
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_account_last_4
        }

        return ''
    }

    resetCard () {
        if(this.disabled) return;

        this.onCardReset();
    }

    selectCard (card) {
        if(this.disabled) return;

        this.onCardSelect({ card });
    }

    getLabel (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return paymentMethod.card_brand.toUpperCase()
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_name
        }

        return ''
    }
}

Controller.$inject = ['$scope', 'STRIPE_PAYMENT_TYPE'];

angular.module('SportWrench').component('stripeCardList', {
    templateUrl: 'components/stripe-card-list/template.html',
    bindings: {
        cards: '<',
        selectedCard: '<',
        onCardSelect: '&',
        onCardReset: '&',
        disabled: '<'
    },
    controller: Controller,
});
