angular.module('SportWrench').component('sanctioningCheckUpdate', {
    templateUrl: 'components/sanctioning-check-update/sanctioning-check-update.html',
    bindings: {
        profileInfo         : '<',
        role                : '<',
        sportSanctioningId  : '<',
    },
    controller : Sanctioning<PERSON>heckUpdateController
});

SanctioningCheckUpdateController.$inject = [
    'officialService', 'toastr', '$rootScope', '$stateParams', 'UtilsService', 'SANCTIONING_CHECK_FIELDS_ALIASES',
    'SAFESPORT_FIELD', 'BG_FIELD', 'MBR_FIELD', 'SANCTIONING_BODY', 'AAU_FIELD', 'AAU_BG_FIELD', 'AAU_SAFESPORT_FIELD'
];

function SanctioningCheckUpdateController (
    officialService, toastr, $rootScope, $stateParams, UtilsService, SANCTIONING_CHECK_FIELDS_ALIASES, SAFESPORT_FIELD,
    BG_FIELD, MBR_FIELD, SANCTIONING_BODY, AAU_FIELD, AAU_BG_FIELD, AAU_SAFESPORT_FIELD
) {
    this.SAFESPORT_FIELD        = SAFESPORT_FIELD;
    this.BG_FIELD               = BG_FIELD;
    this.MBR_FIELD              = MBR_FIELD;
    this.SANCTIONING_BODY       = SANCTIONING_BODY;
    this.AAU_FIELD              = AAU_FIELD;
    this.AAU_BG_FIELD           = AAU_BG_FIELD;
    this.AAU_SAFESPORT_FIELD    = AAU_SAFESPORT_FIELD;

    this.eventHasUSAVSanctioning = function () {
        return this.sportSanctioningId === SANCTIONING_BODY.USAV;
    };

    this.eventHasAAUSanctioning = function () {
        return this.sportSanctioningId === SANCTIONING_BODY.AAU;
    };

    this.showSetOkBtn = function (type) {
        let profile     = this.profileInfo;
        let fieldType   = SANCTIONING_CHECK_FIELDS_ALIASES[type];

        if(profile[fieldType.manual_ok]) {
            return false;
        }

        return profile[fieldType.expiration] || profile[fieldType.status] !== 'YES';
    };

    this.getExpirationText = function (type) {
        let profile     = this.profileInfo;
        let fieldType   = SANCTIONING_CHECK_FIELDS_ALIASES[type];

        if(profile[fieldType.manual_ok]) {
            return profile[fieldType.manual_ok];

        } else if(profile[fieldType.exp_date]) {
            return `${UtilsService.capitalizeFirstLetter(profile[fieldType.expiration] || 'Expires')
            } on ${profile[fieldType.exp_date]}`;

        } else if(this.profileInfo.country !== 'US' && type !== this.MBR_FIELD) {
            return 'N/A';
        }
    };

    this.setSanctioningCheckFieldOk = function (field) {
        this.loading = true;

        let date = moment().format('MMM DD, YYYY');

        officialService.setSanctioningCheckFieldOk(
            this.role, field, $stateParams.event, this.profileInfo.event_official_id, date
        )
            .then(this.onSuccess.bind(this))
            .finally(() => this.loading = false)
    };

    this.onSuccess = function(result) {
        toastr.success('Updated');
        this.profileInfo = Object.assign(this.profileInfo, result);

        $rootScope.$broadcast('reload.' + this.role + '.list', this.profileInfo);
    };

    this.getStatus = function (type) {
        let profile     = this.profileInfo;
        let fieldType   = SANCTIONING_CHECK_FIELDS_ALIASES[type];

        if(profile[fieldType.manual_ok]) {
            return 'YES'
        } else {
            return profile[fieldType.status];
        }
    }
}
