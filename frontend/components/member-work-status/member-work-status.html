<ng-form name="work_status">
    <label class="radio-inline">
        <input type="radio" value="pending" name="ws" ng-model="$ctrl.member.work_status" ng-change="$ctrl.onWSChange()">
        <strong>Pending</strong>
    </label>
    <label class="radio-inline">
        <input type="radio" value="approved" name="ws" ng-model="$ctrl.member.work_status" ng-change="$ctrl.onWSChange()">
        <strong>Approved</strong>
    </label>
    <label class="radio-inline">
        <input type="radio" value="declined" name="ws" ng-model="$ctrl.member.work_status" ng-change="$ctrl.onWSChange()">
        <strong>Declined</strong>
    </label>
    <label class="radio-inline">
        <input type="radio" value="waitlisted" name="ws" ng-model="$ctrl.member.work_status" ng-change="$ctrl.onWSChange()">
        <strong>Wait Listed</strong>
    </label>
</ng-form>
<p class="col-xs-offset-3 help-block" ng-if="$ctrl.form.work_status.$modified">
    <span>Click to restore: </span>
    <span
        class="label label-warning pointer"
        ng-bind="$ctrl.wsLabel[$ctrl.workStatusCache]"
        ng-click="$ctrl.restoreDefaultWS()">Warning</span>
</p>
