angular.module('SportWrench').component('memberWorkStatus', {
    templateUrl: 'components/member-work-status/member-work-status.html',
    bindings: {
        member: '<',
        form: '<',
        workStatusCache: '<',
        onChanged: '&',
    },
    controller: ['$rootScope', Component]
});

function Component($rootScope) {
    this.wsLabel 		= {
        pending 		: 'Pending',
        approved 		: 'Approved',
        declined 		: 'Declined',
        waitlisted 		: 'Wait Listed'
    };

    this.$onChanges = (changes) => {
        if (changes && changes.form && changes.form.currentValue) {
            if (this.form && !this.form.$modified) {
                this.form.$modified = [];
            }
        }
    };

    const _wsField = 'work_status';

    this.restoreDefaultWS = function () {
        this.member.work_status = this.workStatusCache;
        this.onWSChange();
    };

    this.onWSChange = function () {
        this.form.work_status.$modified = (this.member.work_status !== this.workStatusCache);

        setFieldModified(this.form.work_status.$modified, _wsField);
    };

    const setFieldModified = (isModified, fieldName) => {
        const _index = this.form.$modified.indexOf(fieldName);

        if (isModified) {
            if (_index === -1) {
                this.form.$modified.push(fieldName);
            }
        } else {
            this.form.$modified.splice(_index, 1);
        }

        this.onChanged({ isChanged: this.form.$modified.length > 0 });

        if(this.form.$modified.length > 0) {
            $rootScope.$emit('official-data-not-saved');
        } else {
            $rootScope.$emit('official-data-saved');
        }
    }
}
