angular.module('SportWrench').service('WebpointSyncAdultService', [
    '$http', 'ATHLETE_MEMBER_TYPE', 'STAFF_MEMBER_TYPE', WebpointSyncAdultService
]);

function WebpointSyncAdultService ($http, ATHLETE_MEMBER_TYPE, STAFF_MEMBER_TYPE) {
    this._$http     = $http;
    this._baseUrl   = '/api/event/';

    this.STAFF_MEMBER_TYPE      = STAFF_MEMBER_TYPE;
    this.ATHLETE_MEMBER_TYPE    = ATHLETE_MEMBER_TYPE;
}

WebpointSyncAdultService.prototype.syncMember = function (memberID, memberType, eventID, clubID) {
    let url = `${this._baseUrl}${eventID}/club/${clubID}/${memberType}/${memberID}/sync`;

    return this._$http.get(url).then(resp => resp.data);
};


WebpointSyncAdultService.prototype.syncAthlete = function (athleteID, eventID, clubID) {
    return this.syncMember(athleteID, this.ATHLETE_MEMBER_TYPE, eventID, clubID);
};

WebpointSyncAdultService.prototype.syncStaffer = function (stafferID, eventID, clubID) {
    return this.syncMember(stafferID, this.STAFF_MEMBER_TYPE, eventID, clubID);
};
