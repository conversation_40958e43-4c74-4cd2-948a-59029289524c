angular.module('SportWrench').directive('phoneFormatter', phoneFormatter)

function phoneFormatter ($filter) {
	return {
		require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
        	var telFilter = $filter('tel');

        	function __unFormat (v) {
        		return v && v.replace(/\D/g, '')
        	}
            ctrl.$formatters.unshift(function () {
                return telFilter(ctrl.$modelValue)
            });

            ctrl.$parsers.unshift(function (data) {
            	var unformattedValue = __unFormat(data);
                elem.val(telFilter(unformattedValue))
                return unformattedValue || null
            });
        }
	}
}