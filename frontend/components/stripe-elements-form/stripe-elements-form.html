<div class="row stripe-card-form">
    <div class="col-xs-12" ng-show="!$ctrl.utils.hidePaymentRequestButton">
        <spinner active="$ctrl.utils.paymentBtnIsLoading"></spinner>
        <div ng-show="!$ctrl.utils.paymentBtnIsLoading" id="payment-request-button"></div>
    </div>
    <div class="col-xs-12">
        <form id="payment-form" ng-submit="$ctrl.submit()">
            <fieldset>
                <legend>Enter card details</legend>

                <div id="card-element">
                    <!-- A Stripe Element will be inserted here. -->
                </div>

                <!-- Used to display Element errors. -->
                <div id="card-errors" role="alert"></div>

                <button class="btn" type="submit" ng-disabled="$ctrl.disableSubmit()">
                    Pay {{$ctrl.payment.total | currency}}
                </button>
            </fieldset>
        </form>
    </div>
</div>
