angular.module('SportWrench').directive('dateFormatter', ['$filter', function($filter) {
    return {
        require: 'ngModel',
        link: function(scope, element, attrs, modelCtrl) {
            const dateMode = attrs.dateFormatter === 'date';
            modelCtrl.$formatters.push(function(data) {
                if (dateMode) {
                    return data ? $filter('date')(new Date(data), 'MM/dd/yyyy') : '';
                }
                
                return data ? $filter('UTCdate')(new Date(data), 'MM/DD/YYYY') : '';
            });
        }
    };
}]);
