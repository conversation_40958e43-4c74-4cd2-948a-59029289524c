<div ng-class="$ctrl.getClass({field: 'visibility_scope'})">
    <label class="control-label">Template for:</label>
    <div>
        <div class="radio-inline">
            <label>
                <input type="radio"
                       name="visibility_scope"
                       value="event"
                       ng-model="$ctrl.formModel.visibility_scope"
                       ng-change="$ctrl.onChange()"
                       required
                > Current Event Only
            </label>
        </div>
        <div class="radio-inline">
            <label>
                <input type="radio"
                       name="visibility_scope"
                       value="eo"
                       ng-model="$ctrl.formModel.visibility_scope"
                       ng-change="$ctrl.onChange()"
                       required
                > All My Events
            </label>
        </div>
    </div>
</div>
