angular.module('SportWrench').component('manualImportTeamsModal', {
    templateUrl: 'components/manual-import-teams/manual-import-teams-modal.html',
    bindings: {
        close               : '&',
        onSave              : '&',
    },
    controller: ManualImportTeamsModal
});

ManualImportTeamsModal.$inject = ['ENTRY_STATUS', 'toastr', 'fileUploadService'];

function ManualImportTeamsModal(ENTRY_STATUS, toastr, fileUploadService) {
    this.entryStatuses = ENTRY_STATUS;
    this.submitting = false;
    this.error = null;

    this.file = null;
    this.statusEntry = null;

    this.isSubmitDisabled = () => {
        return this.submitting;
    };

    const getFileErrorMessage = () => {
        return fileUploadService.textFileUploadValidation(this.file);
    };

    this.save = () => {
        this.onChanges();
        if(this.form.$invalid || this.error !== null) {
            toastr.warning('Not all data filled');
            return;
        }
        this.submitting = true;
        const data = {
            statusEntry: this.statusEntry,
            file: this.file,
        };
        this.onSave({ data }).then((r) => {
            toastr.success(`${r.parsed} team(s) created`);
        }).finally(() => {
            this.submitting = false;
        });
    };

    this.onChanges = () => {
        let fileErrorMessage;
        if(this.form.status_entry.$invalid) {
            this.error = 'Invalid Entry Status';
        }else if(this.form.file.$invalid) {
            this.error = 'File is required';
        } else if(fileErrorMessage = getFileErrorMessage()) {
            this.error = fileErrorMessage;
        } else {
            this.error = null;
        }
    };

    this.isErrorVisible = () => {
        return this.error !== null && this.form.$submitted;
    };
}
