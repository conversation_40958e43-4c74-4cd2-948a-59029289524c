<div class="modal-header">
    <h3 class="text-info">Add file with Teams</h3>
</div>
<div class="modal-body form-group">
    <form novalidate name="$ctrl.form" ng-submit="$ctrl.save()" id="manual-import-teams-form">
    <div class="row">
        <div
                class="col-xs-6 col-xs-offset-3"
                ng-class="{'has-error': $ctrl.form.$submitted && $ctrl.form.status_entry.$invalid }"
        >
            <label class="control-label">Entry Status</label>
            <select class="form-control" name="status_entry"
                    ng-model="$ctrl.statusEntry"
                    ng-options="s.id as s.title for s in $ctrl.entryStatuses"
                    ng-change="$ctrl.onChanges()"
                    required
            >
                <option value="" selected>Select Status...</option>
            </select>
        </div>
    </div>

    <hr/>

    <div
            class="form-group"
            ng-class="{'has-error': $ctrl.form.$submitted && $ctrl.form.file.$invalid }"
    >
        <input type="file"
               id="file"
               name="file"
               ng-model="$ctrl.file"
               file-change="$ctrl.onChanges()"
               required
        > Upload file (XLSX, CSV)
        <br/>
        <a href="/data/import/teams_import_demo.xlsx">Download a sample</a>
    </div>

    <div class="row-space"></div>
    <uib-alert type="danger" ng-if="$ctrl.isErrorVisible()">{{ $ctrl.error }}</uib-alert>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-success" type="submit" form="manual-import-teams-form" ng-disabled="$ctrl.isSubmitDisabled()">Add</button>
    <button class="btn btn-default" ng-click="$ctrl.close()">Close</button>
</div>
