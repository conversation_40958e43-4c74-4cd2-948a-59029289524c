
const US_BANK_ACCOUNT_TYPE = 'us_bank_account';
const ACH_MAPPING = {
    [US_BANK_ACCOUNT_TYPE]: 'ach'
};

const MIN_AMOUNT_FOR_ACH = 100;

const CARD_PAYMENT_SUB_TYPES = ['apple_pay', 'google_pay'];

class Controller {
    constructor(
        StripePaymentElementService, teamPaymentService, $scope, $timeout, PAYMENT_TYPE, $window,
        PAYMENT_INTENT_STATUS, PAYMENT_PROVIDER
    ) {
        this.StripePaymentElementService = StripePaymentElementService;
        this.teamPaymentService = teamPaymentService;
        this.$scope = $scope;
        this.$timeout = $timeout;
        this.$window = $window;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.PAYMENT_INTENT_STATUS = PAYMENT_INTENT_STATUS;
        this.PAYMENT_PROVIDER = PAYMENT_PROVIDER;

        this.CARD_PAYMENT_SUB_TYPES = CARD_PAYMENT_SUB_TYPES;
        this.ACH_PAYMENT_SUB_TYPES = Object.keys(ACH_MAPPING);
        this.ACH_MAPPING = ACH_MAPPING;
    }

    async $onInit () {
        this.paymentIntent = null;

        await this.__createPaymentIntent();
        this.__initStripe();
        this.__initPaymentElement();

        this.paymentType = null;
        this.paymentReadyForConfirmation = false;
        this.hidePaymentForm = false;
        this.minAmountForSelectedPaymentMethod = 0;
        this.selectedCard = null;

        // detect page reload and closing tab
        this.$window.onbeforeunload = () => {
            if(!_.isEmpty(this.paymentIntent)) {
                const allowedToCancelStatuses = [
                    this.PAYMENT_INTENT_STATUS.SUCCEEDED,
                    this.PAYMENT_INTENT_STATUS.PROCESSING
                ];

                if(!allowedToCancelStatuses.includes(this.paymentIntent.status)) {
                    this.teamPaymentService.removePaymentSession({
                        payment_intent_id: this.paymentIntent.id,
                        payment_provider: this.PAYMENT_PROVIDER.STRIPE,
                    });
                }
            }
        };

        this.$scope.$on('$destroy', () => {
            this.$window.onbeforeunload = null;
        });
    }

    tooSmallAmount () {
        if(this.payment.type === this.PAYMENT_TYPE.ACH) {
            this.minAmountForSelectedPaymentMethod = MIN_AMOUNT_FOR_ACH;

            return this.payment.total < MIN_AMOUNT_FOR_ACH;
        }

        return false;
    }

    submitAllowed() {
        const isTooSmallAmountForAch = this.tooSmallAmount()
        const totalAllowed =
            Number(this.payment.total) > 0 && !isTooSmallAmountForAch;

        return (
            totalAllowed &&
            !this.paymentIsInProgress &&
            (this.isCardSelected() || this.paymentReadyForConfirmation)
        );
    }

    isCardSelected() {
        return this.selectedCard !== null
    }

    async submit () {
        if(!this.submitAllowed()) {
            return;
        }

        this.paymentIsInProgress = true;
        this.onPaymentConfirmStarted();

        try {
            await this.updatePaymentIntent()

            const { error, paymentIntent } = await this.confirmPayment();

            if (error) {
                this.__addError(error);
            }

            await this.onPaymentConfirmed({paymentDataId: paymentIntent && paymentIntent.id});

            this.$scope.$digest();
        } finally {
            this.$timeout(() => {
                this.paymentIsInProgress = false;
                this.onPaymentProcessed();
            });

        }
    }

    onCardSelect(card) {
        this.selectedCard = card;
        this.$timeout(()=>{
            this.onPaymentTypeChange({ type: this.PAYMENT_TYPE.CARD });
            this.__clearError();
        })
    }

    onCardReset() {
        this.selectedCard = null;
        this.$timeout(()=>{
            // set payment type to original 
            this.onPaymentTypeChange({ type: this.paymentType });
            this.__clearError();
        })
    }

    async confirmPayment() {
        // confirm payment with existing card
        if(this.selectedCard !== null) {
            return this.StripePaymentElementService.confirmCardPayment(
                this.paymentIntent.secret,
                this.selectedCard.stripe_payment_method_id,
                this.returnUrl
            );
        }
        return this.StripePaymentElementService.confirmPayment(
            this.elements,
            this.returnUrl
        );
    }

    updatePaymentIntent() {
        const paymentData = {
            amount: this.payment.total,
            type: this.payment.type,
            payment_intent_id: this.paymentIntent.id,
            verticalInsuranceQuote: this.payment.verticalInsuranceQuote
        };

        if(this.typeChangePayment && this.typeChangePayment.id){
            return this.teamPaymentService.changePaymentType(
                angular.extend(paymentData, {
                    purchase_id: this.typeChangePayment.id,
                })
            );
        }

        return this.teamPaymentService.updatePaymentIntent(
            angular.extend(paymentData, { receipt: this.payment.receipt })
        );
    }
    __addError (error) {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = error && error.message;
    }

    __clearError () {
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = '';
    }

    async __createPaymentIntent () {
        const { paymentIntent, savedCards } =
            await this.teamPaymentService.createPaymentIntent(
                this.payment.subtotal
            );

        this.paymentIntent = paymentIntent;
        this.savedCards = savedCards;
    }

    __initStripe () {
        if(this.paymentIntent.public_key) {
            try {
                this.StripePaymentElementService.init(this.paymentIntent.public_key);
            } catch (err) {

                this.hidePaymentForm = true;
            }
        }
    }

    __initPaymentElement () {
        let { elements, paymentElement }
            = this.StripePaymentElementService.getPaymentElement(this.paymentIntent.secret, {
            paymentMethodOrder: [this.PAYMENT_TYPE.CARD, US_BANK_ACCOUNT_TYPE]
        });

        paymentElement.mount('#payment-element');

        paymentElement.collapse();

        paymentElement.on('change', ({ complete, value: { type: paymentType }  }) => {
            this.__clearError();

            this.__handlePaymentTypeChange(paymentType);

            this.paymentReadyForConfirmation = complete;

            this.$scope.$digest();
        });

        this.elements = elements;
    }

    __handlePaymentTypeChange (paymentType) {
        if(this.CARD_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.PAYMENT_TYPE.CARD;
        }

        if(this.ACH_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.ACH_MAPPING[paymentType];
        }

        if(paymentType !== this.paymentType) {
            this.paymentType = paymentType;

            this.onPaymentTypeChange({ type: paymentType });
        }
    }
}

Controller.$inject = [
    'StripePaymentElementService', 'teamPaymentService', '$scope', '$timeout', 'PAYMENT_TYPE', '$window',
    'PAYMENT_INTENT_STATUS', 'PAYMENT_PROVIDER'
];

angular.module('SportWrench').component('stripePaymentElement', {
    templateUrl: 'components/stripe-payment-element/template.html',
    bindings: {
        payment: '<',
        onPaymentTypeChange: '&',
        returnUrl: '<',
        typeChangePayment: '<',
        cardPaymentEnabled: '<',
        onPaymentConfirmed: '&',
        onPaymentConfirmStarted: '&',
        onPaymentProcessed: '&'
    },
    controller: Controller
});
