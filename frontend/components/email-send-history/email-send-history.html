<a href ng-click="$ctrl.openModal()" ng-hide="!$ctrl.historyRow.event_email_id">{{$ctrl.historyRow.title}}</a>
<span ng-hide="!$ctrl.historyRow.event_email_id">to <i>{{$ctrl.historyRow.email_to}}</i></span>
<div ng-if="$ctrl.showDetailsBlock()">
    <span ng-if="!$ctrl.showDetails">{{ $ctrl.historyRow.title.substr(0,175) }}</span> 
    <span ng-if="$ctrl.showDetails">{{ $ctrl.historyRow.title }}</span>
    <a 
        href 
        ng-if="$ctrl.showShowMoreBtn()"
        ng-click="$ctrl.toggleShowDetails()"
    > 
        {{$ctrl.getShowMoreLabel()}}
    </a>
</div>
<div ng-if="$ctrl.showNotes()">
    <b>Notes:</b> <i>{{$ctrl.historyRow.comments}}</i>
</div>
<div ng-if="$ctrl.showFrom()">
    <b>From:</b>
    <i 
        uib-tooltip="{{$ctrl.historyRow.first&&$ctrl.historyRow.last?$ctrl.historyRow.first+' '+$ctrl.historyRow.last:''}}"
    >
        {{$ctrl.historyRow.role_event_owner?'Event':'Housing'}}
    </i>
</div>
<div ng-if="$ctrl.showSubject()" class="form-control-static">
    <b>Subject:</b> {{$ctrl.historyRow.email_subject}}
</div>
