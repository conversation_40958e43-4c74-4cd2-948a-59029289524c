
class Controller {
    constructor (ClubPersonalInfoService, masterClubService, ConfirmationService, toastr) {
        this.ClubPersonalInfoService = ClubPersonalInfoService;
        this.masterClubService = masterClubService;
        this.ConfirmationService = ConfirmationService;
        this.toastr = toastr;

        this.isLoading = false;
    }

    async handler () {
        let answer = await this.ConfirmationService.ask(this.confirmMsg(), {
            disableNoBtn: true,
            disableYesBtn: !this.membersCount
        }, !!this.membersCount ?
            '<div class="row"><div class="col-xs-12 text-center text-bold">Add Personal Info?</div></div>'
            : ''
        )

        if(answer === this.ConfirmationService.YES_RESP) {
            this.isLoading = true;

            await this.ClubPersonalInfoService.mergeDataFromPreviousSeason(this.type)
                .then(count => {
                    this.toastr.success(`Updated ${count} member(s)`);

                    this.onUpdate();
                })
                .finally(() => this.isLoading = false);
        }
    }

    showButton () {
        return this.club && this.club.sport_sanctionings.includes(this.masterClubService.SANCTIONING_IDS.USAV);
    }

    disableButton () {
        return this.isLoading;
    }

    confirmMsg () {
        return `SportWrench will use membership number’s to match personal information (email address, phone number and address) 
            from last season. If you entered this personal information into SportWrench last year and the membership number matches, 
            this feature will import that information into that member’s records for this season.`;
    }
}

Controller.$inject = ['ClubPersonalInfoService', 'masterClubService', 'ConfirmationService', 'toastr'];

angular.module('SportWrench').component('clubPersonalInfoMergeButton', {
    templateUrl: 'components/club-personal-info-merge-button/template.html',
    controller: Controller,
    bindings: {
        type: '<',
        club: '<',
        membersCount: '<',
        onUpdate: '&'
    }
});
