angular.module('SportWrench').directive('socialIcons', function () {
    return {
        restrict: 'E',
        scope: {
            icons: '='
        },
        templateUrl: 'components/social-items/social-items.html',
        link: function (scope) {
            scope.iconsArr = [];
            var stopWatcher = scope.$watch('icons', function (val) {
                if(!val) return;
                var items = [],keys = Object.keys(val);
                for (var i = 0, l = keys.length; i < l; ++i) {
                    if (keys[i] === 'snapchat' && val[keys[i]]) {
                        val[keys[i]] = `https://www.snapchat.com/add/${val[keys[i]]}`;
                    }

                    items.push({
                        value: val[keys[i]],
                        key: keys[i]
                    })
                }
                scope.iconsArr = items;
                stopWatcher();
            })
            scope.sort = function (icon) {
                return icon.key === 'snapchat'
            }
        }
    }
});
