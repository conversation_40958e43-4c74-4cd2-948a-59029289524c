<div ng-show="hideAddNoteField" style="padding-bottom: 5px">
    <span ng-bind="notes.length>0 ? notes.length+' notes. ' : 'No notes. '">
    </span>
    <a ng-click="hideAddNoteField=false">Add housing note.</a>
</div>
<div ng-if="!hideAddNoteField">
    <div class="row housing-notes">
        <div class="col-sm-12">
            <textarea type="text" class="form-control" ng-model="new_event_note.comments" placeholder="Tap here to add note text..." rows="4"></textarea>
        </div>
        <div class="col-sm-8 note-add-block">
            <label>Attach note to:</label>
            <label class="radio-inline">
                <input type="radio" name="optionsRadios" id="optionsRadios1" ng-model="new_event_note.action" value="club.housing.note">Club
            </label>
            <label class="radio-inline">
                <input type="radio" name="optionsRadios" id="optionsRadios2" ng-model="new_event_note.action" value="team.housing.note" ng-checked="true">Team
            </label>
        </div>
        <div class="col-sm-4"><button ng-click="addNote(new_event_note)" class="btn btn-primary pull-right">Add note</button></div>
    </div>
</div>
<table class="table" ng-if="notes.length > 0">
    <thead>
    <tr>
        <th class="col-05">Created</th>
        <th class="col-2">Note</th>
        <th></th>
    </tr>
    </thead>
    <tbody auto-height>
    <tr ng-repeat="n in notes track by $index">
        <td class="col-05">
            <span class="helper-tooltip" uib-tooltip="{{n.created | UTCdate:'MM/DD/YYYY hh:mm a'}}" ng-bind="n.created | UTCdate:'MM/DD'"></span>
        </td>
        <td class="col-2 history-content">
            <div ng-init="showDetails = false">
                    <span ng-show="!showDetails">{{ n.title.substr(0,175) }}</span>
                    <span ng-show="showDetails">{{ n.title }}</span>
                    <a href ng-show="n.title.length > 175"
                       ng-click="showDetails = !showDetails"
                       ng-bind="showDetails?'Show less':'Show more...'"
                    >
                    </a>
            </div>
            <div class="white-space-pre-line" ng-if="n.comments">
                <div>
                    <b ng-bind="getNoteTypeText(n.action)"></b>
                </div>
                {{n.comments}}
            </div>
            <span ng-if="edit[$index]" class="col-2">
                <div class="input-group input-group-sm">
                    <input type="text" ng-model="newComment" class="form-control">
                    <span class="input-group-btn">
                        <button class="btn btn-default" ng-click="saveEditedNote($index, newComment)" type="button">Append</button>
                    </span>
                </div>
            </span>
            <div ng-if="n.first && n.last">
                <b>By:</b>
                <span>
                    {{ n.first+' '+n.last }}
                </span>
                <span>
                    ({{ n.is_event_owner?'Event':'Housing' }})
                </span>
            </div>
        </td>
        <td class="col-05">
            <span ng-if="n.is_own_note && n.action === 'team.housing.note'" style="cursor:pointer" ng-click="editNote($index)">
                <i ng-if="!edit[$index]" class="fa fa-plus" aria-hidden="true"></i>
                <i ng-if="edit[$index]" class="fa fa-minus" aria-hidden="true"></i>
            </span>
        </td>
    </tr>
    </tbody>
</table>

