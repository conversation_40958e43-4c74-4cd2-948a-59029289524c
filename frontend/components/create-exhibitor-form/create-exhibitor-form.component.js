class CreateExhibitorFormComponent {
    constructor(geoService) {
        this.geoService = geoService;

        this.EMAIL_PATTERN = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/;

        this.states = [];
    }

    $onInit() {
        this.loadStates();
    }

    loadStates() {
        this.geoService.getStates('US', ({ data }) => {
            this.states = data;
        })
    }

    getControlClass(prop, isRequred) {
        return (('form-group' + ((isRequred)?' validation-required':'')) + ((this.form[prop] && this.form[prop].$invalid && this.form.$submitted)?' has-error':''));
    };
}

angular.module('SportWrench').component('createExhibitorForm', {
    templateUrl: 'components/create-exhibitor-form/create-exhibitor-form.html',
    bindings: {
        data: '=',
        form: '=',
    },
    controller: [
        'geoService',
        CreateExhibitorFormComponent
    ]
});