angular.module('SportWrench').component('eventSupervisor', {
	templateUrl 	: 'eventsupervisor/eventsupervisor.html',
	bindings 		: {},
	controller 		: [
		'SupervisorService', 'APP_ROUTES', '$state', '$uibModal', 'toastr', '$filter', 'UtilsService', 'FEE_PAYER', Ctrl
	]
});

function Ctrl (SupervisorService, APP_ROUTES, $state, $uibModal, toastr, $filter, UtilsService, FEE_PAYER) {
	var _self 	= this;

	var orderBy         = $filter('orderBy');
    let currencyFilter  = $filter('currency');

	var events  		= [];
	this.orderedEvents  = [];

	this.states = {
		teams 		: APP_ROUTES.EO.TEAMS,
		tickets 	: APP_ROUTES.EO.TICKETS_PAYMENTS,
		officials 	: APP_ROUTES.EO.OFFICIALS,
		edit 		: APP_ROUTES.EO.UPDATE_EVENT,
        staffers    : APP_ROUTES.EO.STAFFERS,
        exhibitors  : APP_ROUTES.EO.EXHIBITORS,
	};

	this.transfReq = {
		progress: null
	}

	this.order = {
		field : 'date_start',
		reverse 	: true
	};

	this.openedTab   = {};

	this.eventsBySeason = [];

	this.swFeeClass = function (event) {
		return {
			'text-danger font-bold': event.sw_fee === null,
			'fees_pay_buyer': event.sw_fee !== null && event.fees_pay_buyer
		}
	}

    this.swExhibitorsFeeClass = function (event) {
        return {
            'text-danger font-bold': event.exhibitors_sw_fee === null && event.for_exhibitors
        }
    }

	this.isLiveIconClass = UtilsService.isLiveIconClass.bind(UtilsService);

	this.openEditPage = function (eventID) {
		$state.go(this.states.edit, { event: eventID });
	}

	this.showTransfReqSpinner = function (eventID) {
		return this.transfReq.progress === eventID;
	}

	this.showStripeAccount = function (event) {

		if (!!this.transfReq.progress) {
			return;
		}

		this.transfReq.progress = event.event_id;

		SupervisorService.getStripeAccount(event.event_id)
		.then(function (resp) {

			_self.transfReq.progress = null;

			return $uibModal.open({
				template 	: [
					'<modal-wrapper>',
					'<stripe-account-info account="account"></stripe-account-info>',
					'</modal-wrapper>'
				].join(''),
				controller 	: ['$scope', function ($scope) {
					$scope.modalTitle = '<h4>' + event.name + '</h4>';

					$scope.account = resp.data.account;
				}]
			}).result
		}, function () {
			_self.transfReq.progress = null;
		});
	}

	this.showMonetaryForm = function (event) {
		return $uibModal.open({
			template 	: [
				'<modal-wrapper>',
				'<spinner active="isEventLoading"></spinner>',
				`<event-monetary-edit 
                    ng-if="!isEventLoading" 
                    allow-ticket-sales="allow_ticket_sales"
                    allow-exhibitor-sales="allow_exhibitor_sales"
                    allow-teams-sales="allow_teams_sales"  
                    basic-tickets-mode="basic_tickets_mode"
                    event="event" 
                    on-submit="submit(data)"
                >`,
				'</event-monetary-edit>',
				'</modal-wrapper>'
			].join(''),
			controller : ['$scope', function ($scope) {

				$scope.modalTitle = '<h4>' + event.name + '</h4>';

				$scope.event = {};

				$scope.isEventLoading = true;

				$scope.allow_ticket_sales       = event.for_tickets;
				$scope.allow_exhibitor_sales    = event.for_exhibitors;
                $scope.allow_teams_sales        = event.for_teams;
                $scope.basic_tickets_mode       = event.basic_tickets_mode;

				$scope.submit = function (data) {
					return SupervisorService.updateMonetaryInfo(event.event_id, data)
					.then(function () {

						if (data.stripe_tickets_fee_payer === FEE_PAYER.BUYER && data.tickets_sw_fee_payer === FEE_PAYER.BUYER) {
							event.fees_pay_buyer = true;
							event.fees_pay_seller = false;

						} else {
							event.fees_pay_seller = true;
							event.fees_pay_buyer = false;
						}

					    event.sw_fee = data.teams_sw_fee;
					    event.tickets_sw_fee = data.tickets_sw_fee;
					    event.stripe_percent = data.stripe_teams_percent;
					    event.stripe_tickets_percent = data.stripe_tickets_percent;
                        event.stripe_exhibitors_percent = data.stripe_exhibitors_percent;
                        event.exhibitors_sw_fee = data.exhibitors_sw_fee;

						toastr.success('Saved!');
						$scope.$close(true);
					});
				}

				SupervisorService.getEventMonetaryInfo(event.event_id)
				.then(function (resp) {
					$scope.event = resp.data.event;
					$scope.isEventLoading = false;
				})
				.catch(function () {
					$scope.$dismiss();
				});

			}]
		}).result;
	}

	this.changeOrder = function (field) {
		if (this.order.field === field) {
			this.order.reverse = !this.order.reverse;
		} else {
			this.order.field = field;
		}
	}

	this.balCls = function (diff) {
		return {
			'text-danger': (diff < 0)
		}
	}

	this.getEventTeamsPercent = function (maxQty, feeQty) {
		return Math.round(UtilsService.approxNumber((feeQty * 100) / (maxQty || 1)));
	}

    this.getTicketModeLabel = function(e) {
        return e.assigned_tickets_mode ? 'Assigned Ticket' : 'Basic Ticket';
    }

	loadEvents();

	function loadEvents () {
		SupervisorService.getEventsList()
		.then(function  (resp) {
			events = resp.data.events;

			var seasons         = _.groupBy(events, 'season');
			var seasonYears     = Object.keys(seasons).sort().reverse();


			_self.openedTab[seasonYears[0]] = true;

            _self.eventsBySeason = [];

			seasonYears.forEach(function (key) {
				_self.eventsBySeason.push({ year: key, events: seasons[key] });
			});
		})
	}

	this.openSeasonEvents = function (year) {
        this.openedTab[year] = !this.openedTab[year];
    };

	this.showAllPercentage = function(event) {
	    //remove nulls
	    let all = [event.stripe_percent, event.stripe_tickets_percent, event.stripe_exhibitors_percent]
            .filter(item => item);

	    //get unique items
	    let unique = [...new Set(all)];

	    return unique.length > 1;
	};

	this.feeClass = function(event) {
		return {
			'fees_pay_buyer': event.fees_pay_buyer,
		}
	};

	this.getExhibitorsSWFee = function (event) {
        return !event.for_exhibitors
            ? '-'
            : event.exhibitors_sw_fee === null
                ? 'Undefined'
                : currencyFilter(event.exhibitors_sw_fee, '$');
    };

	this.getTicketsSWFee = function (event) {
        return !event.for_tickets
            ? '-'
            : currencyFilter(event.tickets_sw_fee, '$');
    };

    this.getTeamsSWFee = function (event) {
        return event.sw_fee === null ? 'Undefined' : currencyFilter(event.sw_fee, '$');
    };

    this.showStripeSWPercent = function (season) {
        return season.events[0].stripe_sw_percent >= 0 && this.openedTab[season.year]
    }
}
