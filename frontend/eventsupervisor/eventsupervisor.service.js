angular.module('SportWrench').service('SupervisorService', SupervisorService);

function SupervisorService ($http) {
	this._$http = $http;
	this._baseURL = '/api/event-supervisor/';
}

SupervisorService.prototype.getEventsList = function () {
	return this._$http.get(this._baseURL + 'events');
}

SupervisorService.prototype.getStripeAccount = function (eventID) {
	return this._$http.get(this._baseURL + 'events/' + eventID + '/stripe-account');
}

SupervisorService.prototype.getEventMonetaryInfo = function (eventID) {
	return this._$http.get(this._baseURL + 'events/' + eventID + '/monetary');
}

SupervisorService.prototype.updateMonetaryInfo = function (eventID, data) {
	return this._$http.post(this._baseURL + 'events/' + eventID + '/monetary', data);
}