<div class="container supervisor">
	<table class="table sw-adaptive-grid events-list table-vertical-align-middle">
		<thead>
			<tr>
				<th class="col-owner">
					<a ng-click="$ctrl.changeOrder('owner')" href="">Owner</a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'owner'}}"></reverse-arrow>
				</th>
				<th class="col-event-name">Event Name</th>
				<th>
					<a ng-click="$ctrl.changeOrder('date_start')" href="">Dates</a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'date_start'}}"></reverse-arrow>
				</th>
				<th>
					<a ng-click="$ctrl.changeOrder('days_count')" href="">Days</a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'days_count'}}"></reverse-arrow>
				</th>
				<th>
					<a ng-click="$ctrl.changeOrder('sw_fee')" href="">
                        <div class="inline-block text-center">
                            <div>SW Fee</div>
                            <div class="fs-10">Teams/Tickets/Exhibitors</div>
                        </div>
                    </a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'sw_fee'}}"></reverse-arrow>
				</th>
                <th>
                    <a ng-click="$ctrl.changeOrder('stripe_percent')" href="">
                        <div class="inline-block text-center">
                            <div>Stripe %</div>
                            <div class="fs-10">Teams/Tickets/Exhibitors</div>
                        </div>
                    </a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'stripe_percent'}}"></reverse-arrow>
                </th>
				<th>Event Teams</th>
				<th>
					<a ng-click="$ctrl.changeOrder('payments')" uib-tooltip="Payments" href="">
						<i class="fa fa-cart-plus"></i>
					</a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'payments'}}"></reverse-arrow>
				</th>
				<th>
					<a ng-click="$ctrl.changeOrder('reg_open')" href="">Reg Dates</a>
                    <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.order.field == 'reg_open'}}"></reverse-arrow>
				</th>
				<th>Stripe Acc.</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-repeat-start="season in $ctrl.eventsBySeason" class="bg-info font-bold">
				<td colspan="10" ng-click="$ctrl.openSeasonEvents(season.year)" align="left">
					<span>{{season.year}} Events: {{season.events.length}}</span>
                    <span class="stripe-sw-percent" ng-if="$ctrl.showStripeSWPercent(season)">
                        (Internal Stripe % = {{season.events[0].stripe_sw_percent}})
                    </span>
				</td>
			</tr>
			<tr ng-repeat-end ng-repeat="e in season.events | orderBy:$ctrl.order.field:$ctrl.order.reverse" 
				ng-if="$ctrl.openedTab[season.year]">
				<td ng-bind="e.owner"></td>
				<td>
					<div class="font-bold">
						<i ng-class="$ctrl.isLiveIconClass(e.live)"></i>
					 	<i class="fa fa-pencil-square-o pointer" ng-click="$ctrl.openEditPage(e.event_id)"></i>
					 	<span ng-bind="e.name"></span>
					</div>
					<div>
						<a ng-if="e.for_teams" ui-state="$ctrl.states.teams" ui-state-params="{ event: e.event_id }">Teams</a>
						<a ng-if="e.for_tickets" ui-state="$ctrl.states.tickets" ui-state-params="{ event: e.event_id }">{{$ctrl.getTicketModeLabel(e)}}</a>
						<a ng-if="e.for_officials" ui-state="$ctrl.states.officials" ui-state-params="{ event: e.event_id }">Officials</a>
						<a ng-if="e.for_exhibitors" ui-state="$ctrl.states.exhibitors" ui-state-params="{ event: e.event_id }">Exhibitors</a>
					</div>
				</td>
				<td>
					<div>
						{{ e.date_start | UTCdate:'MMM, DD' }}
					</div>
					<div>
						{{e.date_end | UTCdate:'MMM, DD'}}
					</div>
				</td>
				<td ng-bind="e.days_count"></td>
				<td class="pointer" ng-click="$ctrl.showMonetaryForm(e)">
                    <div ng-class="$ctrl.swFeeClass(e)" ng-bind="$ctrl.getTeamsSWFee(e)"></div>
                    <div ng-class="$ctrl.feeClass(e)" ng-bind="$ctrl.getTicketsSWFee(e)"></div>
                    <div ng-class="$ctrl.swExhibitorsFeeClass(e)" ng-bind="$ctrl.getExhibitorsSWFee(e)"></div>
                </td>
                <td class="pointer" ng-click="$ctrl.showMonetaryForm(e)">
                    <div ng-if="$ctrl.showAllPercentage(e)">
                        <div ng-class="$ctrl.feeClass(e)">{{e.stripe_percent}}%</div>
                        <div ng-class="$ctrl.feeClass(e)">{{e.stripe_tickets_percent}}%</div>
                        <div ng-class="$ctrl.feeClass(e)">{{e.stripe_exhibitors_percent}}%</div>
                    </div>
                    <div ng-if="!$ctrl.showAllPercentage(e)">
                        <div ng-class="$ctrl.feeClass(e)">{{e.stripe_percent}}%</div>
                    </div>
                </td>
				<td>
					<div>
						<span ng-bind="e.fee_teams_qty"></span>
					 	<span> / </span>
					 	<span ng-bind="e.max_teams_qty"></span>
					</div>
					 <div>
						<span>{{$ctrl.getEventTeamsPercent(e.max_teams_qty, e.fee_teams_qty)}}% full</span>
					 </div>
				</td>
				<td ng-bind="e.payments"></td>
				<td>
					<div>
						{{ e.reg_open | UTCdate:'MMM, DD YYYY' }}
					</div>
					<div>
						{{e.reg_close | UTCdate:'MMM, DD YYYY' }}
					</div>
				</td>
				<td>
					 <span class="xs-text"><spinner active="$ctrl.showTransfReqSpinner(e.event_id)"></spinner></span>
					 <button 
						 class="btn btn-xs btn-primary" 
						 ng-click="$ctrl.showStripeAccount(e)"
						 ng-if="!$ctrl.showTransfReqSpinner(e.event_id)"
					 >Get Info</button>
				</td>
			 </tr>
		</tbody>
	</table>
</div>
