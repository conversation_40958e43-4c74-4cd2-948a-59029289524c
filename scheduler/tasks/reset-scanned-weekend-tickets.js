'use strict';

/**
 * Reset scanned weekend tickets.
 * The task updates purchase (scanned_at, scanner_id, scanner_location, tickets_scan) and purchase_ticket (available)
 * fields.
 */

class ResetScannedTickets {
    constructor () {}

    get SCAN_HISTORY_MESSAGE () {
        return `Marked as Not Scanned at %s`;
    }

    run () {
        return this.__resetTicketsScanning__()
            .then(this.__sendNotification__.bind(this));
    }

    __resetTicketsScanning__ () {
        let query =
            `WITH payments_to_update AS (
                SELECT
                  p.purchase_id,
                  e.event_id,
                  e.email,
                  e.long_name
                FROM event e
                  LEFT JOIN event_ticket et
                    ON e.event_id = et.event_id
                  LEFT JOIN purchase_ticket pt
                    ON pt.event_ticket_id = et.event_ticket_id
                  LEFT JOIN purchase p
                    ON p.purchase_id = pt.purchase_id
                WHERE (e.tickets_settings ->> 'require_recipient_name_for_each_ticket') :: BOOLEAN IS TRUE
                      AND e.deleted IS NULL
                      AND et.ticket_type = 'weekend'
                      AND pt.canceled IS NULL
                      AND p.canceled_date IS NULL
                      AND p.status <> 'canceled'
                      AND p.is_ticket IS TRUE
                      AND p.scanned_at IS NOT NULL
                      AND e.date_start < NOW() AT TIME ZONE e.timezone
                      AND e.date_end > NOW() AT TIME ZONE e.timezone
                      AND TO_CHAR(NOW()::TIMESTAMPTZ AT TIME ZONE e.timezone, 'fmHH24') :: INTEGER IN (23, 0)
                      AND p.wristband_serial IS NULL
            ), update_purchase AS (
              UPDATE purchase p
              SET
                scanned_at       = NULL,
                scanner_id       = NULL,
                scanner_location = NULL,
                tickets_scan     = CONCAT_WS(CHR(10), tickets_scan, FORMAT($1, 
                    TO_CHAR(
                      (SELECT NOW()::TIMESTAMPTZ AT TIME ZONE e.timezone 
                      FROM event e WHERE p.event_id = e.event_id), 'MM/DD/YYYY HH:mm:ss'
                    )
                ))
              WHERE p.purchase_id IN (SELECT ptu.purchase_id FROM payments_to_update ptu)
            ), update_tickets_qty AS (
              UPDATE purchase_ticket pt
              SET available = quantity
              WHERE pt.purchase_id IN (SELECT ptu.purchase_id FROM payments_to_update ptu)
            )
            SELECT DISTINCT ptu.long_name, ptu.email, count(ptu.purchase_id) OVER()::INT "scanned_tickets"
            FROM payments_to_update ptu`;

        return Db.query(query, [this.SCAN_HISTORY_MESSAGE]).then(result => result.rows);
    }

    __sendNotification__ (data) {
        if(!data || !data.length) {
            return Promise.resolve();
        }

        return Promise.all(
            data.map(this.__sendEmail__.bind(this))
        );
    }

    __sendEmail__ (event) {
        return EmailService.sendEmail({
            from        : '"SportWrench" <<EMAIL>>',
            to          : event.email,
            bcc         : '"sw debug" <<EMAIL>>, "Baranov Evhenii" <<EMAIL>>',
            subject     : `Weekend tickets scan reset for ${event.long_name}`,
            text        : `${event.scanned_tickets} "weekend" ticket's scan(s) were reset`
        });
    }
}

module.exports = new ResetScannedTickets();
