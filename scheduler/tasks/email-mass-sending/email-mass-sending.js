'use strict';

/**
 * The task proceed email sending cron job
 */

const path  = require('path');
const fork  = require('child_process').fork;
const argv  = require('optimist').argv;

class EmailMassSending {
    constructor () {
        this.sendingIsInProgress = false;
        this.process             = null;
        this.stopSending         = false;
    }

    get EMAIL_SENDING_PROCESS_FILE () {
        return {
            PATH: path.resolve(__dirname),
            NAME: 'email-sending-process.js'
        }
    }

    get MAX_ITERATIONS () {
        return 10;
    }

    isSendingInProgress () {
        return this.sendingIsInProgress;
    }

    stopEmailSending () {
        this.stopSending = true;

        return new Promise((resolve, reject) => {
            this.process.send({stop: true});

            this.process.on('message', (msg) => {
                if(msg.stopped) {
                    resolve();
                }
            });

            setTimeout(() => reject(new Error('Can\'t stop child process')), 5000);
        })
    }

    async run () {
        let jobData = await SystemJobService.findEmailSendingJob();

        if(!jobData) {
            return;
        }

        let redisTableIsNotEmpty = true;

        if(SystemJobService.doesRedisTableHasReadyStatus(jobData.tableKey)) {
            jobData.tableKey = await SystemJobService.setEmailsSendingInProgress(jobData.tableKey);
        }

        let iterations = this.MAX_ITERATIONS;

        while(redisTableIsNotEmpty && iterations > 0) {
            if(iterations !== this.MAX_ITERATIONS) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            if(this.stopSending) {
                return null;
            }

            iterations--;

            let processParams = this.__prepareProcessParams(jobData);

            await this.__createEmailSendingProcess(processParams);

            redisTableIsNotEmpty = await SystemJobService.doesTableHasReceivers(jobData.tableKey);
        }

        this.sendingIsInProgress  = false;

        if(iterations === 0) {
            await this.__sendEmailToDevs(jobData);
        }

        return SystemJobService.setEmailSendingDoneStatus(jobData.tableKey, jobData.systemJobID)
            .then(() => {
                let { recipientType } = SystemJobService.emailSending.parseEmailSendingTableKey(jobData.tableKey);

                if([AEMService.CLUB_STAFF_GROUP, AEMService.CLUB_DIRECTORS_GROUP].includes(recipientType)) {
                    return eventNotifications.setEventChangeRowsPublished(jobData.systemJobID);
                }
            });
    }

    __prepareProcessParams (jobData) {
        return {
            templateID          : jobData.templateID,
            tableKey            : jobData.tableKey
        }
    }

    __setEventChangeRowsPublished (systemJobID) {
        return Db.query(`UPDATE event_change SET published = TRUE WHERE system_job_id = $1`, [systemJobID]);
    }

    __createEmailSendingProcess (params) {
        let args = [
            `--template=${params.templateID}`,
            `--key=${params.tableKey}`,
            `--max_old_space_size=50`,
            (argv.prod ? '--prod' : '--dev')
        ];

        return new Promise((resolve, reject) => {
            this.process = fork(this.EMAIL_SENDING_PROCESS_FILE.NAME, args, {
                env     : process.env,
                cwd     : this.EMAIL_SENDING_PROCESS_FILE.PATH,
                stdio   : 'pipe',
                detached: true
            }).on('error', err => {
                reject(err);
            }).on('exit', (code) => {
                if(Number(code) !== 0) {
                    reject(new Error('Process exit code is not 0'));
                }

                resolve();
            });

            this.process.stdout.on('data', msg => {
                loggers.debug_log.verbose(msg.toString());
            });

            this.process.stderr.on('data', err => {
                loggers.errors_log.error(err.toString());
            });
        })
    }

    __sendEmailToDevs (data) {
        return EmailService.sendEmail({
            from        : '"SportWrench" <<EMAIL>>',
            to          : '"sw debug" <<EMAIL>>',
            cc          : '"Baranov Yevhenii" <<EMAIL>>',
            subject     : 'Redis table max read attempts have reached',
            text        : JSON.stringify(data, null, ' ')
        }).catch(ErrorSender.defaultError.bind(ErrorSender));
    }
}

module.exports = new EmailMassSending();
