'use strict';

const path = require('path');

process.env['SW_LOGGER_FILE_PATH'] = path.resolve(__dirname, '..', '..', '..', '..', 'logs');

/* Load sails app services to global */
require('../../globals-loder').init();

const argv  = require('optimist').argv;
const pg    = require('pg');

const VariablesTransformer 	= require('../../../api/services/aem-sender-utils/_VariablesTransformer.js');
const EmailService = require('../../../api/services/EmailService');
const AEMSenderService = require('../../../api/services/AEMSenderService');
const EMAIL_PRIORITY = require('../../../api/constants/emails').PRIORITY;

const DB_CONNECTION         = sails.config.connections[sails.config.db.connection];
const TEMPLATE_ID           = argv.template;
const TABLE_KEY             = argv.key;

const MAX_ITERATIONS = 125;

const dbClient  = new pg.Client(DB_CONNECTION);
dbClient.connect();

let stopSending = false;

process.on('message', (msg) => {
    if(msg.stop === true) {
        stopSending = true;
    }
});

function getReceiversList (tableKey) {
    return SystemJobService.getEmailReceiversList(tableKey)
        .then(result => {
            let filteredData = [];

            result.forEach((item, id) => {
                if ((id%2) !== 0) {
                    let parsedData = JSON.parse(item);
                    let duplicate = filteredData.find(recipient => recipient.email === parsedData.email);

                    if(!duplicate) {
                        filteredData.push(parsedData);
                    }
                }
            });

            return filteredData;
        })
}

function addEmailToQueue (letter, unsubscribeLinkMetadata) {
    return EmailService.sendEmail(letter, {
        priority: EMAIL_PRIORITY.MARKETING,
        unsubscribeLinkMetadata,
    });
}

function addEmailToHistory (emailAddress, emailSendingID) {
    let query = squel.insert().into('email_sending_history')
        .set('email_address'    , emailAddress)
        .set('email_sending_id' , emailSendingID);

    return dbClient.query(query.toString());
}

async function processSend (letter, receivers, tableKey, emailSendingID, unsubscribeLinkMetadata, emailSender) {
    receivers = receivers.filter(
        receiver => receiver.email
    )
    if(receivers.length === 0) {
        return;
    }

    letter.from = emailSender;
    letter.personalizations = receivers.map(
        (receiver) => {
            return {
                to: AEMSenderService.formatDestinationAddress(receiver),
                cc: receiver.cc || null,
                bcc: receiver.bcc || null,
                variables: receiver.variables || {},
            };
        }
    );

    await addEmailToQueue(letter, unsubscribeLinkMetadata).then(() => true);

    await Promise.all(
        receivers.map(
            async (receiver) => {
                await addEmailToHistory(receiver.email, emailSendingID);
                await SystemJobService.removeReceiversField(tableKey, receiver.email);
            }
        )
    )
}

async function findSystemJob(event_id, recipient_type, email_sending_id) {
    const params = [recipient_type, email_sending_id];
    let eventIDCondition = 'event_id IS NULL';
    if(event_id !== null) {
        params.push(event_id);
        eventIDCondition = 'event_id = $3';
    }
    const result = await dbClient.query(
        `SELECT settings
        FROM system_job
        WHERE ${eventIDCondition} AND recipient_type = $1 AND email_sending_id = $2
        LIMIT 1`,
        params
    );

    return result.rows?.[0];
}

async function _getEventOwnerEmail($eventId) {
    let sql = `
            SELECT COALESCE(e.email, u.email) as "email"
            FROM "event" e
                JOIN "event_owner" eo on e.event_owner_id = eo.event_owner_id
                JOIN "user" u ON u.user_id = eo.user_id
            WHERE event_id = $1`,
        params = [$eventId];

    return Db.query(sql, params).then(result => result.rows[0] && result.rows[0].email);
}


let sendEmails = async () => {
    try {
        let tableKey    = TABLE_KEY;
        let templateID  = TEMPLATE_ID;

        let { recipientType: templateGroup, eventID, emailSendingID }
            = SystemJobService.emailSending.parseEmailSendingTableKey(tableKey);

        //Table key returns string values, and there could be null, so we need to convert it
        eventID = eventID === 'null' ? null : eventID;

        const systemJob = await findSystemJob(eventID, templateGroup, emailSendingID);
        const emailCategory = systemJob?.settings?.settings?.emailCategory;

        templateGroup = AEMService.virtualGroupToReal(templateGroup);

        let [template, variablesList] = await Promise.all([
            AEMService.getTemplateByID(templateID).then((template) => {
                return {
                    html: template.email_html,
                    text: template.email_text,
                    group: template.group,
                    subject: template.subject
                };
            }),
            AEMService.getTemplateGroupVariables(templateGroup, true),
        ]);

        let unsubscribeLinkMetadata = {
            event_id: eventID,
        };
        if(!EmailService.emailCategory.hasUnsubscribeLink(emailCategory)) {
            unsubscribeLinkMetadata = undefined;
        }

        let replyTo = '';
        const hasReplyTo = EmailService.emailCategory.hasReplyTo(emailCategory);
        if(hasReplyTo) {
            replyTo = systemJob?.settings?.settings?.replyTo;
            if(_.isEmpty(replyTo)) {
                replyTo = await _getEventOwnerEmail(eventID);
            }
        }

        const emailSender = EmailService.emailCategory.getEmailSender(emailCategory);

        for(let i = 0; i < MAX_ITERATIONS; i++) {
            let receivers = await getReceiversList(tableKey);
            if (!receivers.length) {
                process.exit(0);
            }

            let _hasUnknown = VariablesTransformer.checkTemplateForUnknownVariables(template, variablesList);

            if (_hasUnknown) {
                throw new Error('Template has unknown variables!');
            }

            receivers = await VariablesTransformer.general.getEventSocialLinks(eventID, receivers);

            if(_.isEmpty(replyTo)) {
                const [receiver] = receivers;
                replyTo = receiver.replyto?.replace?.(',', '.') || null;
            }

            let letter = {
                group: template.group,
                templateId: templateID,
                replyTo,
            };
            for (const receiver of receivers) {
                receiver.variables = VariablesTransformer.getVariablesValues(templateGroup, template, variablesList, receiver);
            }

            if (stopSending) {
                process.send({ stopped: true });
                process.exit(0);
            }

            await processSend(letter, receivers, tableKey, emailSendingID, unsubscribeLinkMetadata, emailSender);
        }

        process.exit(0);
    }
    catch(err) {
        console.error(err);
        process.exit(1);
    }
};

sendEmails();
