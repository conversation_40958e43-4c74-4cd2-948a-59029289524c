'use strict';

/**
 * The task proceed email receivers list generation cron job
 */

const VariablesTransformer 	= require('../../api/services/aem-sender-utils/_VariablesTransformer.js');

class ReceiversListGenerator {
    constructor () {}

    get RECEIVERS_BATCH_SIZE () {
        return 500;
    }

    get EMAIL_SENT_ACTION () {
        return 'Email sent';
    }

    async run () {
        let jobData = await SystemJobService.findEmailListGenerationJob();

        if(!jobData) {
            return;
        }

        try {
            jobData.email_settings = JSON.parse(jobData.email_settings);
        } catch (err) {
            throw new Error('Email Settings cant be parsed');
        }

        try {
            jobData.email_filters = JSON.parse(jobData.email_filters);
        } catch (err) {
            throw new Error('Email Filters cant be parsed');
        }

        return this.__proceedLoop(jobData);
    }

    async __addSubjectToReceivers ({group, variables, subject}, eventID, allReceivers) {
        let receivers = await VariablesTransformer.general.getEventSocialLinks(eventID, allReceivers);

        for(let receiver of receivers) {
            let { subject: compiledSubject }
                = await VariablesTransformer.transformVariables(group, { subject }, variables, receiver);

            delete receiver.social_icons;
            receiver.subject = compiledSubject;
        }

        return receivers;
    }

    async __proceedLoop (jobData, redisTableKey = null) {
        let templateData;

        if(this.isRequiresLogging(jobData.recipient_type)) {
            templateData = await this.__getTemplateData(jobData.email_template_id, jobData.recipient_type);
        }

        let emptyList = false;
        let iterator  = 0;
        let isFirstBatch = true;

        let receiversQuery = this.__getReceiversSQL(jobData);

        const receiversGenerator = await Db.queryCursor(receiversQuery, this.RECEIVERS_BATCH_SIZE);

        for(let receivers of receiversGenerator) {
            receivers = await receivers;
            if(receivers.length === 0) {
                break;
            }

            if(isFirstBatch && jobData.email_settings) {
                if(jobData.email_settings.cc) {
                    receivers[0].cc = this.__prepareEmail(jobData.email_settings.cc);
                }
                if(jobData.email_settings.bcc) {
                    receivers[0].bcc = this.__prepareEmail(jobData.email_settings.bcc);
                }
            }

            isFirstBatch = false;

            if(this.isRequiresLogging(jobData.recipient_type)) {
                receivers = await this.__addHistoryRows(receivers, jobData, templateData);
            }

            if(receivers.length) {
                let formattedReceivers = this.__formatReceiversList(receivers, jobData);

                redisTableKey = await SystemJobService.addEmailReceiversToRedis(
                    jobData, formattedReceivers, redisTableKey
                );
                iterator++;
            } else {
                emptyList = true;
            }
        }

        return SystemJobService.setEmailsListGenerationNextStatus(redisTableKey, jobData.system_job_id);
    }

    __getTemplateData (templateID, receiversType) {
        let templateGroup = AEMService.virtualGroupToReal(receiversType);

        return Promise.all([
            AEMService.getTemplateByID(templateID).then(template => template && template.subject),
            AEMService.getTemplateGroupVariables(templateGroup, true)
        ]).then(result => {
            return {
                subject     : result[0],
                variables   : result[1],
                group       : templateGroup
            };
        })
    }

    isRequiresLogging (type) {
        return ![AEMService.CUSTOM_LIST_GROUP, AEMService.EVENT_OWNERS_GROUP, AEMService.CLUBS_DIRECTORS_GROUP].includes(type);
    }

    __formatReceiversList (data, jobData) {
        return data.reduce((all, recipient) => {
            if(jobData.email_settings && jobData.email_settings.replyto) {
                recipient.replyto = this.__prepareEmail(jobData.email_settings.replyto);
            }

            all.push(this.__prepareEmail(recipient.email));
            all.push(JSON.stringify(recipient));

            return all;
        }, []);
    }

    __getReceiversSQL (jobData) {
        let {
            recipient_type: recipientType,
            event_id: eventID,
            email_sending_id: emailSendingID,
            custom_recipients_list_id: customListID,
            email_template_id: templateID,
            email_settings: emailSettings,
            email_filters: receiversFilters
        } = jobData;

        emailSettings = emailSettings || {};
        receiversFilters = receiversFilters || {};
        const emailCategory = emailSettings?.emailCategory;

        let query = null;

        let filterTmplID = emailSettings && emailSettings.not_notified ? templateID : 0;

        let emailParams = {
            emailSendingID,
            templateID: filterTmplID,
            emailCategory,
        };

        if (recipientType === AEMService.TICKET_GROUP) {
            query = SQLQueryBuilder.ticketsCustomer.emailReceiversList(eventID, receiversFilters, emailParams)
        } else if (recipientType === AEMService.CAMP_GROUP) {
            query = SQLQueryBuilder.camps.emailReceiversList(eventID, receiversFilters, emailParams)
        } else if (recipientType === AEMService.OFFICIAL_GROUP) {
            query = SQLQueryBuilder.eventOfficial
                .emailReceiversList(eventID, receiversFilters, emailParams)
        } else if (recipientType === AEMService.CLUB_DIRECTORS_GROUP) {
            query = SQLQueryBuilder.clubs
                .emailReceiversList(
                    eventID,
                    {
                        receivers_type: SQLQueryBuilder.clubs.CLUB_DIRECTOR_RECEIVER_TYPE, 
                        eventRegMethod: emailSettings && emailSettings.event_registration_method,
                        emailParams,
                        ...receiversFilters
                    },
                    
                );

            query = squel.select().from(query, 'all_data');

        } else if (recipientType === AEMService.CLUBS_DIRECTORS_GROUP) {
            query = SQLQueryBuilder.clubDirectors
                .emailReceiversList(jobData.email_filters);
        }  else if (recipientType === AEMService.CLUB_STAFF_GROUP) {
            query = SQLQueryBuilder.clubs
                .emailReceiversList(
                    eventID,
                    {
                        receivers_type: SQLQueryBuilder.clubs.STAFF_RECEIVER_TYPE,
                        eventRegMethod: null,
                        emailParams,
                        ...receiversFilters
                    },
                );

            query = squel.select().from(query, 'all_data');
        } else if (recipientType === AEMService.CUSTOM_LIST_GROUP) {
            query = SQLQueryBuilder.customRecipients.emailReceiversList({eventID, emailSendingID, listID: customListID, emailCategory})
        } else if (recipientType === AEMService.STAFF_GROUP) {
            query = SQLQueryBuilder.eventStaff.emailReceiversList(eventID, receiversFilters, emailParams);
        } else if (recipientType === AEMService.EVENT_OWNERS_GROUP) {
            query = SQLQueryBuilder.eventOwners.emailReceiversList();
        }

        return query;
    }

    async __addHistoryRows (receivers, jobData, template) {
        if(!receivers.length) {
            return [];
        }

        let allReceivers = await this.__addSubjectToReceivers(template, jobData.event_id, receivers);

        let rows = await this.__getHistoryRows(allReceivers, jobData, jobData.recipient_type);

        if(!rows.length) {
            return [];
        }

        await rows.reduce((prevStep, receiver) => {
            return prevStep.then(() => {
                receiver.unique = { event_email_id: receiver.event_email_id };

                if([AEMService.CLUB_DIRECTORS_GROUP, AEMService.CLUB_STAFF_GROUP].includes(jobData.recipient_type)) {
                    receiver.unique.roster_club_id = receiver.roster_club_id;
                    receiver.unique.roster_team_id = receiver.roster_team_id;
                }

                return eventNotifications.add_notification(jobData.event_id, receiver);
            })
        }, Promise.resolve());

        return allReceivers.filter(receiver => receiver.is_unique_recipient);
    }

    async __getHistoryRows (receivers, jobData, group) {
        let formattedData = [];

        for(let receiver of receivers) {
            let eventEmailData  = this.__prepareEventEmailData(receiver, jobData, group);

            let eventEmailID = await this.__addEventEmailRow(eventEmailData);

            if(receiver.team_ids && receiver.team_ids.length) {
                receiver.team_ids.forEach(rosterTeamID => {
                    formattedData.push(
                        this.__prepareHistoryData(receiver, jobData, eventEmailID, rosterTeamID)
                    );
                });
            } else {
                formattedData.push(this.__prepareHistoryData(receiver, jobData, eventEmailID));
            }
        }

        return formattedData;
    }

    __prepareHistoryData (receiver, jobData, eventEmailID, rosterTeamID) {
        let data = {
            roster_club_id  : receiver.roster_club_id || null,
            event_id        : jobData.event_id,
            action          : this.EMAIL_SENT_ACTION,
            user_id         : jobData.user_id,
            published       : false,
            system_job_id   : jobData.system_job_id,
            event_email_id  : eventEmailID,
            purchase_id     : receiver.purchase_id || null
        };

        if(rosterTeamID) {
            data.roster_team_id = Number(rosterTeamID);
        }

        return data;
    }

    __prepareEventEmailData (receiver, jobData, type) {
        let data = {
            email_to        : this.__prepareEmail(receiver.email),
            event_id        : jobData.event_id,
            email_subject   : receiver.subject,
            roster_club_id  : [AEMService.CLUB_STAFF_GROUP, AEMService.CLUB_DIRECTORS_GROUP].includes(type)
                ? receiver.roster_club_id
                : null,
            email_cc        : receiver.cc || null,
            email_bcc       : receiver.bcc || null,
            recipient_type  : AEMSenderService.getRecipientType(type),
            email_template_id: jobData.email_template_id,
            purchase_id     : receiver.purchase_id || null
        };

        if(type === AEMService.CLUB_STAFF_GROUP) {
            data.master_staff_id = receiver.id;
        }

        return data;
    }

    __prepareEmail (email) {
        return email.trim().toLowerCase();
    }

    __addEventEmailRow (data) {
        return Db.query(
            squel.insert().into('event_email').setFields(data).returning('event_email_id')
        ).then(result => result.rows[0] && result.rows[0].event_email_id);
    }
}

module.exports = new ReceiversListGenerator();

