const {CUSTOM_PAYMENT, PAYMENT_FOR} = require("../../api/constants/payments");
const SEASON = sails.config.sw_season.current;
const EMAIL_TEMPLATE_GROUP = AEMService.UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP;
const EMAIL_TEMPLATE_TYPE = AEMService.UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE.NOTICE;
const RECIPIENT_TYPE = AEMSenderService.getRecipientType(EMAIL_TEMPLATE_GROUP);

module.exports.run = async function run() {
    const events = await getEvents();

    if(_.isEmpty(events)) {
        return null;
    }

    for(let event of events) {
        const notificationData = {
            userID: event.user_id
        };

        const [
            teamsUncollectedFee,
            ticketsUncollectedFee,
            boothsUncollectedFee
        ] = await getUncollectedFee(event.event_id);

        const teamsFeeRequiredToPay = teamsUncollectedFee * -1;
        const ticketsFeeRequiredToPay = ticketsUncollectedFee * -1;
        const boothsFeeRequiredToPay = boothsUncollectedFee * -1;

        const minFee = EventPaymentMethodService.customPayment.utils.getMinPaymentAmount(
            event.stripe_payment_method_type
        );

        if(teamsFeeRequiredToPay >= minFee || ticketsFeeRequiredToPay >= minFee || boothsFeeRequiredToPay >= minFee) {
            await sendNotification(event.event_id, notificationData);
        }
    }
}

async function getUncollectedFee(eventId) {
    return Promise.all([
        getBalance(eventId, PAYMENT_FOR.TEAMS),
        getBalance(eventId, PAYMENT_FOR.TICKETS),
        getBalance(eventId, PAYMENT_FOR.BOOTHS)
    ]);
}

function getBalance (eventID, type) {
    return EventPaymentMethodService
        .customPayment
        .balanceInfo[CUSTOM_PAYMENT.BALANCE_TYPE.UNCOLLECTED_SW_FEES][type]
        .getBalance(eventID)
        .then(result => result.currentBalance);
}

function getEvents () {
    const query = `
        select
            "e"."event_id",
            "u"."user_id",
            "spm"."type" as "stripe_payment_method_type"
        from "event" as "e"
        left join "event_payment_method" as "epm" on "epm"."event_id" = "e"."event_id"
        left join "stripe_payment_method" as "spm"
            on "spm"."stripe_payment_method_id" = "epm"."stripe_payment_method_id"
        left join "user_stripe_customer" as "usc" on "usc"."stripe_customer_id" = "spm"."stripe_customer_id"
        left join "user" as "u" on "u"."user_id" = "usc"."user_id"
        left join "event_email" as "ee" on "ee"."event_id" = "e"."event_id"
            AND "ee"."recipient_type" = '${RECIPIENT_TYPE}'
        where e.deleted IS NULL
            AND e.season = ${SEASON}
            AND epm.event_payment_method_id IS NOT NULL
            AND ee.event_email_id IS NULL
            AND (e.extra_fee_collection_mode = 'custom_payment'
                OR (e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE)
            AND (e.allow_ticket_sales IS TRUE OR e.allow_teams_registration IS TRUE OR e.has_exhibitors IS TRUE)
            AND ((NOW() AT TIME ZONE e.timezone) BETWEEN e.date_start - INTERVAL '5 days' AND e.date_start)`;

    return Db.query(query).then(result => result && result.rows);
}

function sendNotification (eventID, notificationData) {
    if(!eventID) {
        throw new Error('Event Id required');
    }

    if(_.isEmpty(notificationData)) {
        throw new Error('Template data required');
    }

    return AEMSenderService.sendTriggerNotification(EMAIL_TEMPLATE_GROUP, EMAIL_TEMPLATE_TYPE, eventID, notificationData);
}
