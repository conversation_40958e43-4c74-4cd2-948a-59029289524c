const swUtils = require('../../api/lib/swUtils');
const {CUSTOM_PAYMENT, PAYMENT_FOR} = require("../../api/constants/payments");

const ALLOWED_PAYMENT_METHOD_TYPES = [
    StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD,
    StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH,
];

module.exports.run = async function run(type, season) {
    let events = await getEvents(type, season);

    if(_.isEmpty(events)) {
        return;
    }

    loggers.debug_log.verbose(`Events to collect Lost Dispute Fee and Failed ACH Fee: ${events.length}`);

    await paySWFee(events, type);
}

function getEvents (type, season) {
    let query = knex('event AS e')
        .select({
            event_id: 'e.event_id',
            user_id: 'usc.user_id',
            payment_method_type: 'spm.type',
            stripe_fixed: 'e.stripe_teams_fixed',
            stripe_percent: knex.raw(`(
                CASE
                    WHEN (e.stripe_teams_percent > 0)
                        THEN (e.stripe_teams_percent / 100)
                    ELSE 0
                END
            )::NUMERIC`),
            ach_percent: knex.raw(`(
                CASE 
                    WHEN (e.ach_teams_percent > 0)
                        THEN (e.ach_teams_percent / 100)
                    ELSE 0
                END
            )`),
        })
        .leftJoin('event_payment_method AS epm', 'epm.event_id', 'e.event_id')
        .leftJoin('stripe_payment_method AS spm', 'spm.stripe_payment_method_id', 'epm.stripe_payment_method_id')
        .leftJoin('user_stripe_customer AS usc', 'usc.stripe_customer_id', 'spm.stripe_customer_id')
        .whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
        .where(builder => {
            builder.where('e.extra_fee_collection_mode', knex.raw('?', ['custom_payment']))
                .orWhereRaw(`(e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE`)
        })
        .where('e.season', season)
        .whereNotNull('epm.event_payment_method_id')
        .whereRaw('spm.offline_is_active IS TRUE');
    if(type === PAYMENT_FOR.TICKETS) {
        query.whereRaw('e.date_end < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.allow_ticket_sales IS TRUE');
    } else if(type === PAYMENT_FOR.TEAMS) {
        query.whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.allow_teams_registration IS TRUE');
    } else if(type === PAYMENT_FOR.BOOTHS) {
        query.whereRaw('e.date_start < (NOW() AT TIME ZONE e.timezone)')
            .whereRaw('e.has_exhibitors IS TRUE');
    } else {
        throw new Error('Payment For Type Invalid: ' + type);
    }

    return Db.query(query).then(result => result && result.rows);
}

async function getBalance (eventID, type) {
    const balanceData = await EventPaymentMethodService
        .customPayment
        .balanceInfo[CUSTOM_PAYMENT.BALANCE_TYPE.ADDITIONAL_FEES]
        .getBalance(eventID, type);

   return balanceData.totalAmount;
}

async function paySWFee (events, type) {
    for(let event of events) {
        if (!ALLOWED_PAYMENT_METHOD_TYPES.includes(event.payment_method_type)) {
            loggers.errors_log.error(`Event does not have a valid payment method type set. Event ID: ${event.event_id}. Payment Method Type: ${event.payment_method_type}. Skipping...`);
            continue;
        }

        const debtAmount = await getBalance(event.event_id, type);
        const minFee = getMinPaymentAmount(event.payment_method_type);

        if(debtAmount >= minFee) {
            event.uncollected_fee = debtAmount;

            await autoPaySWFee(event, type)
                .catch((err) =>
                    loggers.errors_log.error(
                        `Error making auto payment for lost dispute and failed ach fee ${JSON.stringify(err)}`
                    )
                );

            loggers.debug_log.verbose(`Uncollected SW Fee payment proceed for event: ${event.event_id}`);
        }
    }
}

async function autoPaySWFee (eventData, type) {
    const {
        user_id: userID,
        event_id: eventID,
        uncollected_fee: amount,
        payment_method_type,
        ach_percent,
        stripe_percent,
        stripe_fixed,
    } = eventData;

    const paymentMethodInfo = {
        payment_method_type,
        ach_percent: ach_percent ? Number(ach_percent) : StripeService.DEFAULT_ACH_PERCENT,
        stripe_percent: stripe_percent ? Number(stripe_percent) : StripeService.DEFAULT_STRIPE_PERCENT,
        stripe_fixed: stripe_fixed ? Number(stripe_fixed) : StripeService.DEFAULT_STRIPE_FIXED,
    }

    const payment = recountPaymentPrice(paymentMethodInfo, amount, type);

    eventData.payment = payment;

    await EventPaymentMethodService.customPayment.paymentCreation.process(
        eventID,
        userID,
        payment,
        CUSTOM_PAYMENT.PAYMENT_CREATION_MODE.OFFLINE
    );
}

function recountPaymentPrice (paymentMethodInfo, amount, type) {
    const customerStripeFee = EventPaymentMethodService
        .customPayment
        .utils
        .getCustomerStripeFee(paymentMethodInfo, amount);

    const recountTotal = swUtils.normalizeNumber(amount + customerStripeFee);

    return {
        amount: amount,
        merchant_fee: customerStripeFee,
        total: recountTotal,
        payment_for: CUSTOM_PAYMENT.PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE,
        payment_for_type: type,
        description: 'Auto Payment For Lost Dispute and Failed ACH SW Fee For ' + type
    }
}

function getMinPaymentAmount(payment_method_type) {
    // temp changes
    const ACH_MIN_PAYMENT_AMOUNT = 4;

    if(payment_method_type === StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH) {
        return ACH_MIN_PAYMENT_AMOUNT;
    }

    return EventPaymentMethodService.customPayment.utils.getMinPaymentAmount(payment_method_type);
}
