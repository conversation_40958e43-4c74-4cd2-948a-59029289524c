const CTRL = 'EventExhibitorController';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/exhibitors Event Exhibitors List
     * @apiDescription Returns event exhibitors list
     * @apiGroup Event Exhibitors
     *
     */
    'GET /api/event/:event/exhibitors': `${CTRL}.getExhibitors`,

    /**
     *
     * @api {get} /api/event/:event/exhibitor/:exhibitor/info Event Exhibitor Info
     * @apiDescription Returns event exhibitor info
     * @apiGroup Event Exhibitors
     *
     */
    'GET /api/event/:event/exhibitor/:exhibitor/info': `${CTRL}.getExhibitorProfileInfo`,

    /**
     *
     * @api {get} /api/event/:event/exhibitor/:exhibitor Event Exhibitor Registration Info
     * @apiDescription Returns event exhibitor registration info
     * @apiGroup Event Exhibitors
     *
     */
    'GET /api/event/:event/exhibitor/:exhibitor': `${CTRL}.getExhibitorRegistrationInfo`,

    /**
     *
     * @api {put} /api/event/:event/exhibitor/:exhibitor Event Exhibitor Registration Update
     * @apiDescription Updates event exhibitor registration
     * @apiGroup Event Exhibitors
     *
     */
    'PUT /api/event/:event/exhibitor/:exhibitor': `${CTRL}.updateExhibitorRegistrationInfo`,

    /**
     *
     * @api {post} /api/event/:event/exhibitor/:exhibitor Event Exhibitor Registration
     * @apiDescription Creates event exhibitor registration
     * @apiGroup Event Exhibitors
     *
     */
    'POST /api/event/:event/exhibitor/:exhibitor': `${CTRL}.createExhibitorRegistrationInfo`,

    /**
     *
     * @api {post} /api/event/:event/exhibitor Event Exhibitor Creation
     * @apiDescription Creates event exhibitor
     * @apiGroup Event Exhibitors
     *
     */
    'POST /api/event/:event/exhibitor': `${CTRL}.createExhibitor`,

    /**
     *
     * @api {get} /api/event/:event/exhibitors/list Event Exhibitors List for Sales
     * @apiDescription Returns event exhibitors list for specific sales manager
     * @apiGroup Event Exhibitors
     *
     */
    'GET /api/event/:event/exhibitors/list': `${CTRL}.getExhibitorsList`,

    /**
     *
     * @api {get} /api/event/:event/exhibitors/tickets Event Exhibitors Tickets
     * @apiDescription Returns event exhibitors tickets
     * @apiGroup Event Exhibitors
     *
     */
    'GET /api/event/:event/exhibitors/tickets': `${CTRL}.tickets`,
};
