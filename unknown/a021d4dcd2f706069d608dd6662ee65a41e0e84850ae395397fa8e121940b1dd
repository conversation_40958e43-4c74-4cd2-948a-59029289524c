
const CTRL = 'TicketBuyEntryCodeController';

module.exports = {
    /**
     *
     * @api {get} /api/ticket-entry-code/event/:event/code/:code/validation Ticket Buy Entry Code Validation
     * @apiDescription Validates ticket buy entry code
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'GET /api/ticket-entry-code/event/:event/code/:code/validation': `${CTRL}.validateCode`,

    /**
     *
     * @api {post} /api/point-of-sales/:pointOfSalesId/validate-entry-code Ticket Buy Entry Code Validation By PosID
     * @apiDescription Validates ticket buy entry code by PosID
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'POST /api/point-of-sales/:pointOfSalesId/validate-entry-code': {
        action: 'v2/API/other/validate-entry-code-by-pos-id',
        csrf: false
    },

    /**
     *
     * @api {post} /api/ticket-entry-code/event/:event/settings Ticket Buy Entry Code Settings Upsert
     * @apiDescription Creates or updates Ticket Buy Entry Code Settings
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'POST /api/ticket-entry-code/event/:event/settings': `${CTRL}.upsertSettings`,

    /**
     *
     * @api {get} /api/ticket-entry-code/event/:event/list Coupons On Event List
     * @apiDescription Returns All coupons on event
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'GET /api/ticket-entry-code/event/:event/list': `${CTRL}.couponsList`,

    /**
     *
     * @api {get} /api/ticket-entry-code/event/:event/code/:code/tickets Coupon Tickets List
     * @apiDescription Returns All tickets bought with specific coupon
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'GET /api/ticket-entry-code/event/:event/code/:code/tickets': `${CTRL}.couponTicketsList`,

    /**
     *
     * @api {post} /api/ticket-entry-code/event/:event/code/custom Custom Coupon Creation
     * @apiDescription Creates custom ticket buy entry code
     * @apiGroup Ticket Buy Entry Code
     *
     */
    'POST /api/ticket-entry-code/event/:event/code/custom': `${CTRL}.createCustomCode`
}
