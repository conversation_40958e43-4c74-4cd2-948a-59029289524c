'use strict';

const _ = require('lodash');

const eo 		        = require('./eo');
const tickets       	= require('./tickets');
const transfers         = require('./transfers');
const aem  		        = require('./aem');
const users             = require('./users');
const camps             = require('./camps');
const ticketsAdditional = require('./ticketsAdditional');
const esw               = require('./esw');
const files             = require('./files');
const staff             = require('./staff');
const favorite          = require('./favorite');
const customRecipients  = require('./custom-recipients');
const exhibitors        = require('./exhibitors');
const customPayment     = require('./custom-payment');
const teams             = require('./teams');
const payment           = require('./payment');
const event             = require('./event');
const importRoutes      = require('./import');
const booth             = require('./booth');
const divisions         = require('./divisions');
const clubTeams         = require('./club-teams');
const clubTeamMembers   = require('./club-team-members');
const eventSettings     = require('./eventSettings');
const official          = require('./official');
const customForm = require('./custom-form');
const customFormBuilder = require('./custom-form-builder');
const ticketsAppUserVerification = require('./tickets-app-user-verification');
const clubInvoice = require('./club-invoice');
const exhibitorInvoice = require('./exhibitor-invoice');

module.exports = _.defaults(
    {}, eo, tickets, transfers, aem, users, camps, ticketsAdditional, esw, files, staff, importRoutes, booth, divisions,
    favorite, customRecipients, exhibitors, customPayment, teams, payment, event, clubTeams, eventSettings, official,
    clubTeamMembers, customForm, customFormBuilder, ticketsAppUserVerification, clubInvoice, exhibitorInvoice
);
