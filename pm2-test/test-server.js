'use strict';

const http = require('http');
const argv = require('optimist').argv;

const PORT = argv.port || 5000;

process.send = process.send || function () {};

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  res.end('OK');
});

if (argv.delayed) {
	console.log('Delayed load')
	setTimeout(() => {
		server.listen(PORT)
		process.send('ready');
		console.log('Listening on port', PORT);
	}, 7 * 1000);
} else {
	console.log('General load');
	server.listen(PORT);
	console.log('Listening on port', PORT);
}

