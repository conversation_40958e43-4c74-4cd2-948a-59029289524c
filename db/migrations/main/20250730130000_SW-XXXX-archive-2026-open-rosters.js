/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Archive roster athletes for 2026 season events with open rosters only
        -- This will soft-delete all athletes from open rosters for 2026 events
        -- Following the pattern from getTeamMembers method in ClubMembersController.js
        UPDATE roster_athlete
        SET deleted = NOW(),
            deleted_by_user = 'system_archive_2026'
        WHERE deleted IS NULL
        AND deleted_by_user IS NULL
        AND roster_team_id IN (
            SELECT rt.roster_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        );

        -- Archive roster staff for 2026 season events with open rosters only
        -- This will soft-delete all staff from open rosters for 2026 events
        -- Following the pattern from getTeamMembers method in ClubMembersController.js
        UPDATE roster_staff_role
        SET deleted = NOW(),
            deleted_by_user = 'system_archive_2026'
        WHERE deleted IS NULL
        AND deleted_by_user IS NULL
        AND roster_team_id IN (
            SELECT rt.roster_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        );

        -- ========================================================================
        -- ARCHIVE MASTER CLUB DATA FOR 2026 SEASON (ONLY FOR OPEN ROSTERS)
        -- ========================================================================

        -- Archive master athletes for 2026 season, but only those belonging to open rosters
        -- This ensures CDs don't have athletes in their accounts for teams with open rosters
        UPDATE master_athlete
        SET deleted = NOW(),
            deleted_by_user = 'system_archive_2026'
        WHERE deleted IS NULL
        AND season = 2026
        AND master_team_id IN (
            SELECT DISTINCT rt.master_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
            AND rt.master_team_id IS NOT NULL
        );

        -- Archive master staff for 2026 season, but only those belonging to open rosters
        -- This ensures CDs don't have staff in their accounts for teams with open rosters
        UPDATE master_staff
        SET deleted = NOW(),
            deleted_by_user = 'system_archive_2026'
        WHERE deleted IS NULL
        AND season = 2026
        AND master_staff_id IN (
            SELECT DISTINCT msr.master_staff_id
            FROM master_staff_role msr
            INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
            AND rt.master_team_id IS NOT NULL
        );
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        -- Rollback: Restore archived roster members for 2026 season
        -- This will un-delete the soft-deleted records
        UPDATE roster_athlete
        SET deleted = NULL,
            deleted_by_user = NULL
        WHERE deleted_by_user = 'system_archive_2026'
        AND roster_team_id IN (
            SELECT rt.roster_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        );

        UPDATE roster_staff_role
        SET deleted = NULL,
            deleted_by_user = NULL
        WHERE deleted_by_user = 'system_archive_2026'
        AND roster_team_id IN (
            SELECT rt.roster_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        );

        -- Rollback: Restore archived master athletes for 2026 season (open rosters only)
        UPDATE master_athlete
        SET deleted = NULL,
            deleted_by_user = NULL
        WHERE deleted_by_user = 'system_archive_2026'
        AND season = 2026
        AND master_team_id IN (
            SELECT DISTINCT rt.master_team_id
            FROM roster_team rt
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
            AND rt.master_team_id IS NOT NULL
        );

        -- Rollback: Restore archived master staff for 2026 season (open rosters only)
        UPDATE master_staff
        SET deleted = NULL,
            deleted_by_user = NULL
        WHERE deleted_by_user = 'system_archive_2026'
        AND season = 2026
        AND master_staff_id IN (
            SELECT DISTINCT msr.master_staff_id
            FROM master_staff_role msr
            INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
            INNER JOIN event e ON e.event_id = rt.event_id
            WHERE e.season = 2026
            AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
            AND rt.master_team_id IS NOT NULL
        );
    `)
};
