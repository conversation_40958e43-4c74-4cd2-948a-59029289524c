class RedisService {
    constructor($http) {
        this.$http = $http;
    }

    updateTtl(key, value) {
        return this.$http.post('/api/admin/redis/cache/expires', {
            key,
            value,
        })
    }

    getKeyValue(key) {
        return this.$http.post('/api/admin/redis/key/retrieve', {
            key,
        })
    }

    deleteKeys(type) {
        return this.$http.post('api/admin/redis/key/remove', {
            type,
        })
    }

    getAllKeys(type) {
        return this.$http.get(`/api/admin/redis/keys/all`, {
            params: {
                type,
            }
        })
    }
}

angular.module('SportWrench')
    .service('RedisService', ['$http', RedisService]);
