angular.module('SportWrench').controller('DbActionsController', DbActionsController)

function DbActionsController($scope, $http, $window) {
    $scope.actions = {
        removeLineBreaks: {
            done: false,
            success: false,
        },
        setAge: {
            done: false,
            success: false
        }
    };
    
    $scope.removeLineBreaks = function () {
        $scope.actions.removeLineBreaks.done = false;
        $http.get('/api/admin/db/replace/linebreaks').success(function () {            
            $scope.actions.removeLineBreaks.success = true;
        }).error(function () {
            $scope.actions.removeLineBreaks.success = false;
        }).finally(function () {
            $scope.actions.removeLineBreaks.done = true;
        })
    }

    $scope.setAge = function () {
        $scope.actions.setAge.done = false;
        $http.get('/api/admin/db/age/set').success(function () {            
            $scope.actions.setAge.success = true;
        }).error(function () {
            $scope.actions.setAge.success = false;
        }).finally(function () {
            $scope.actions.setAge.done = true;
        })
    }

    $scope.exportEmails = function () {
        $window.open('/api/admin/db/emails/export', '_blank');
    }

    $scope.fixHeights = function () {
        $http.post('/api/admin/club/athletes/update-height').success(function() {}).error(function () {});
    }
}
