angular.module('SportWrench').service('DbCopyService', ['$http', DbCopyService]);

function DbCopyService ($http) {
	this._$http 	        = $http;
	this._baseUrl	        = '/api/admin/db';
	this._tablesInProcess   = [];
}

Object.defineProperty(DbCopyService.prototype, 'TABLES_LIST', {
    value           : ['event', 'event_location', 'event_camp', 'event_ticket', 'division', 'roster_team',
                        'roster_club', 'courts', 'division_standing', 'event_journal', 'matches', 'poolbrackets',
                        'results', 'rounds', 'purchase', 'event_official_schedule'],
    writable        : false,
    configurable    : false
});

DbCopyService.prototype.getTablesList = function () {
    return this.TABLES_LIST.join(', ');
};

DbCopyService.prototype.runCopying = function (data) {
	return this._$http.post(this._baseUrl + '/copy-prod', data);
};

DbCopyService.prototype.__runTableCopying = function (data, tableName, done) {
    return this._$http.post(this._baseUrl + '/copy-prod/' + tableName, data)
        .then(response => {
            let { progress, tablesInProgress } = this.__getProgressValues(response.data.table_name);
            done(tablesInProgress, progress);
        })
        .catch(() => {
            let { progress, tablesInProgress } = this.__getProgressValues(tableName);
            done(tablesInProgress, progress, tableName);
        })
};

DbCopyService.prototype.__getProgressValues = function (tableName) {
    this._tablesInProcess   = _.without(this._tablesInProcess, tableName);
    let progress            = this.TABLES_LIST.length - this._tablesInProcess.length;

    return { progress, tablesInProgress: this._tablesInProcess.join(', ') }
};

DbCopyService.prototype.runTablesCopying = function (data, done) {
    this._tablesInProcess = this.TABLES_LIST;

    return this.TABLES_LIST.reduce((chain, table) => {
        return chain.then(() => this.__runTableCopying(data, table, done));
    }, Promise.resolve());

};
