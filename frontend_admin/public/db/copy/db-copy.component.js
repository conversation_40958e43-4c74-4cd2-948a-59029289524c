angular.module('SportWrench').component('dbCopy', {
	templateUrl 	: 'public/db/copy/db-copy.html',
	controller 		: ['DbCopyService', 'toastr', '$timeout', DBCopyController]
})

function DBCopyController (DbCopyService, toastr, $timeout) {
    let self              = this;
	this.data             = {};
	this.error            = null;
	this.isRunning        = false;
    this.tablesInProgress = '';
    this.progressValue    = 0;
    this.allTables        = DbCopyService.TABLES_LIST;
    this.withErrorTables  = [];

	this.ctrlCls = function (name) {
		return {
			'form-group' 	: true,
			'has-error' 	: this.form.$submitted && this.form[name] && this.form[name].$invalid
		}
	};

    function onProgress (tablesInProgress, progress, tableName) {
        self.progressValue    = progress;
        self.tablesInProgress = tablesInProgress;

        if(tableName) {
            self.withErrorTables.push(tableName);
            self.tablesNotCopied = self.withErrorTables.join(', ');
        }
    }

    this.isReady = function (table) {
        return !this.tablesInProgress.split(', ').includes(table);
    };

    this.submit = function () {
        if (this.form.$invalid) {
            return;
        }

        this.tablesInProgress = DbCopyService.getTablesList();
        this.error            = null;
        this.isRunning        = true;

        DbCopyService.runTablesCopying(this.data, onProgress)
        .then(function () {
            toastr.success('OK!');
        })
        .catch(function (resp) {
            this.error = resp.data && resp.data.validation || 'Internal Error';
        }.bind(this))
        .finally(function () {
            this.isRunning          = false;
            this.progressValue      = 0;

            $timeout(function () {
                self.tablesInProgress   = '';
                self.withErrorTables    = [];
                self.tablesNotCopied    = [];
            }, 1000 * 2)
        }.bind(this))
    }
}
