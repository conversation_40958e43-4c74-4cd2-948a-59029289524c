angular.module('SportWrench').directive('safeSportImport', function (USAVService, EventsService) {
	return {
		restrict: 'E',
		scope: {},
		templateUrl: 'public/usav/safesport/import.html',
		link: function (scope) {
			scope.data = {};

			scope.events = [];

			EventsService.events()
			.then(function (resp) {
				var events = resp && resp.data && resp.data.events;
				if(events) {
					scope.events = events;
				}
			})

			scope.runUpcoming = function () {
				USAVService.saveSportImport.upcomingEvents()
			}

			scope.runChoosen = function () {
				USAVService.saveSportImport.forEvent(scope.data.event)
			}
		}
	}
})