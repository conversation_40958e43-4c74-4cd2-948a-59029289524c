<div class="row">
	<div class="col-sm-offset-2 col-sm-7">
		<form class="form-horizontal">
			<legend>SafeSport</legend>
			<div class="form-group">
		        <div class="col-sm-offset-3 col-sm-9">
		            <button 
		            	class="btn btn-default"
		            	ng-click="runUpcoming()">Reimport SafeSport for upcoming Events</button>
		        </div>
		    </div>
		    <div class="form-group">
		    	<div class="col-sm-offset-3 col-sm-9">
		    		<p>OR choose Event below to reimport it</p>
		    	</div>
		    </div>
		    <div class="form-group">
		        <label class="col-sm-3 control-label">Event:</label>
		        <div class="col-sm-9">
		            <select 
		            	class="form-control"
		            	ng-model="data.event"
		            	ng-options="e.id as e.name for e in events">
		            	<option value="">Choose Event</option>
		            </select>
		        </div>
		    </div>
		    <div class="form-group">
		        <div class="col-sm-offset-3 col-sm-9">
		            <button 
		            	class="btn btn-default"
		            	ng-click="runChoosen()"
		            	ng-disabled="!data.event">Reimport SafeSport for selected Events</button>
		        </div>
		    </div>
		</form>		
	</div>
</div>

