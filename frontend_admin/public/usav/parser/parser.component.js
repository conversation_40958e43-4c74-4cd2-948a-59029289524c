angular.module('SportWrench').component('wpParser', {
	templateUrl : 'public/usav/parser/parser.html',
	controller 	: ['USAVService', WPParserController]
})

function WPParserController (USAVService) {
	var _self = this;
	
	this.parserOuput = null;

	function onResp (resp) {
		var data = resp.data;

		_self.parserOuput = JSON.stringify(data, null, ' ');
	}

	this.runParser = function (data) {

		this.parserOuput = null;

		return USAVService.runParser(data)
		.then(onResp)
		.catch(onResp)
	}
}