angular.module('SportWrench').directive('wpRespBox', function () {
	return {
		restrict: 'E',
		scope: {
			resp: '='
		},
		templateUrl: 'public/usav/api-checker/wp-resp-box.html',
		link: function (scope) {
			scope.$watch('resp', function (val) {
				if(!val) return;
				if(typeof val === 'string') {
					scope.responseText = val;
				} else {
					scope.responseText = JSON.stringify(val, null, ' ');
				}
			})
		}
	}
})