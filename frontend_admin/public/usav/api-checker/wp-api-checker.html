<legend>Enter Member's USAV</legend>
<form class="form-inline row-space">
	<fieldset ng-disabled="utils.requestInProgress">
		<div class="form-group">
	        <label for="usav">USAV</label>
	        <input type="text" class="form-control" name="usav" ng-model="data.usav">
	    </div>
	    <button class="btn btn-primary" ng-click="makeUSAVRequest()">Get Response</button>
    </fieldset>
</form>
<wp-resp-box ng-if="utils.usav_response" resp="utils.usav_response"></wp-resp-box>
<legend>Or Find Member</legend>
<form class="form-inline row-space">
	<fieldset ng-disabled="utils.requestInProgress">
		<div class="form-group">
	        <label for="first">First</label>
	        <input type="text" class="form-control" name="first" ng-model="member.first">
	    </div>
	    <div class="form-group">
	        <label for="last">Last</label>
	        <input type="text" class="form-control" name="last" ng-model="member.last">
	    </div>
	    <div class="form-group">
	        <label for="club_name">Club Name</label>
	        <input type="text" class="form-control" name="club_name" ng-model="member.club_name">
	    </div>
	    <div class="form-group">
	        <label for="club_code">Club Code</label>
	        <input type="text" class="form-control" name="club_code" ng-model="member.club_code">
	    </div>
	    <button class="btn btn-primary" ng-click="findMembers()">Find Members</button>
	</fieldset>
</form>
<table class="table table-condensed" ng-if="utils.membersList.length">
	<thead>
		<tr>
			<th>Type</th>
			<th>G</th>
			<th>Name</th>
			<th>USAV</th>
			<th>Club</th>
			<th></th>
		</tr>
	</thead>
	<tbody>
		<tr ng-repeat-start="m in utils.membersList">
			<td ng-bind="m.type"></td>
			<td>
				<genders 
				m="m.gender === 'male'"
				f="m.gender === 'female'"
				></genders>
			</td>
			<td ng-bind="m.name"></td>
			<td ng-bind="m.usav"></td>
			<td ng-bind="m.club_name"></td>
			<td>
				<button 
					class="btn btn-primary btn-xs" 
					ng-click="makeMemberRequest(m)"
					ng-disabled="!m.usav_number">Get Webpoint Data</button>
			</td>
		</tr>
		<tr ng-repeat-end ng-if="m.wp_response">
			<td colspan="6">
				<wp-resp-box resp="m.wp_response"></wp-resp-box>
			</td>
		</tr>
	</tbody>
</table>