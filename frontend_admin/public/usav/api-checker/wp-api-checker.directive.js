angular.module('SportWrench').directive('wpApiChe<PERSON>', function (USAVService) {
	return {
		restrict: 'E',
		scope: {},
		templateUrl: 'public/usav/api-checker/wp-api-checker.html',
		link: function (scope) {
			scope.utils = {
				requestInProgress: false,
				membersList: []
			}

			scope.data = {}

			scope.member = {}

			scope.makeUSAVRequest = function () {
				if(!scope.data.usav) return;
				scope.utils.usav_response = null;
				__USAVReq(scope.data.usav)
				.then(function (member) {
					scope.utils.usav_response = member;
				})
			}

			scope.makeMemberRequest = function (member) {
				if(!member.usav_number) return;
				__USAVReq(member.usav_number)
				.then(function (wpData) {
					member.wp_response = wpData;
				})
			}

			scope.findMembers = function () {
				if(_.isEmpty(scope.member)) return;
				USAVService.findMembers(scope.member)
				.then(function (members) {
					scope.utils.membersList = members;
				})
			}

			function __USAVReq (usavNumber) {
				scope.utils.requestInProgress = true;
				return USAVService.getWPMemberData(usavNumber)
				.then(function (member) {
					scope.utils.requestInProgress = false;
					return member
				}, function (error) {
					scope.utils.requestInProgress = false;
					console.log(error)
					// process error
				})
			}
		}
	}
})