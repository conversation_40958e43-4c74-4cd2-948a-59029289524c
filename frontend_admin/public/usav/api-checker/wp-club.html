<div class="row">
	<div class="col-sm-offset-3 col-sm-5">
		<form name="$ctrl.wpClubForm" ng-submit="$ctrl.submit()" novalidate>
		    <div class="form-group">
		        <label>Login:</label>
		        <input type="text" class="form-control" ng-model="$ctrl.credentials.login" required>
		    </div>
		    <div class="form-group">
		        <label>Password:</label>
		        <input type="password" class="form-control" ng-model="$ctrl.credentials.pswd" required>
		    </div>
		     <button type="submit" class="btn btn-default">Submit</button>
		</form>
	</div>
</div>
<div class="row row-space" ng-if="$ctrl.wpData">
	<div class="col-sm-12">
		<pre>{{$ctrl.wpData | json}}</pre>
	</div>
</div>

