angular.module('SportWrench').factory('USAVService', function ($http) {
    return {
        saveSportImport: {
        	upcomingEvents: function () {
        		return $http.get('/api/admin/webpoint/staff-update')
        	},
        	forEvent: function (eventId) {
        		return $http.get('/api/admin/webpoint/event/' + eventId + '/staff-update')
        	}
        },
        getWPMemberData: function (usav) {
            return $http.get('/api/admin/webpoint/member/' + usav + '/find')
            .then(function (resp) {
                return resp && resp.data && resp.data.member;
            })
        },
        findMembers: function (params) {
            return $http.get('/api/admin/club/members/find', {
                params: params
            }).then(function (resp) {
                return resp && resp.data && resp.data.members
            })
        },
        makeClubAPIRequest: function (data) {
            return $http.post('/api/admin/webpoint/members', data)
            .then(function (resp) {
                return resp.data;
            });
        },
        runParser: function (data) {
            return $http.post('/api/admin/webpoint/run_parser', data)
            .then(function (resp) {
                return resp.data;
            });
        }
    }
})
