<div class="row">
    <div class="col-sm-12">
        <div class="row row-space">
            <div class="col-sm-12 form-inline">
                <span><b>Clear: </b></span>
                <button class="btn btn-danger" ng-click="clearStorage('cache')">Cache</button>
                <button class="btn btn-danger" ng-click="clearStorage('session')">Sessions</button>
                <button class="btn btn-danger" ng-click="clearStorage('all')">All</button>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-5 col-md-6 col-sm-8 col-xs-12">
                <ttl-form ng-repeat="ct in cache_time" key="ct.key" name="ct.name" value="ct.value"></ttl-form>
            </div>
        </div>
        <tabset>
            <tab heading="Keys" ng-click="opts.show_value_tab = false">
                <p class="lead">Total: {{keys.length}} keys</p>
                <ul class="list-group">
                    <li class="list-group-item" ng-repeat="k in keys">
                        <a href="" ng-click="load_value(k)">{{k}}</a>
                    </li>
                </ul>
            </tab>
            <tab heading="{{selection.key}}" active="opts.show_value_tab" ng-if="opts.show_value_tab">                
                <pre style="margin: 20px 0;">{{selection.value}}</pre>
            </tab>
        </tabset>        
    </div>
</div>
