angular.module('SportWrench').component('ttlForm', {
    templateUrl: 'public/redis/ttl-form/ttl-form.html',
    bindings: {
        key: '<',
        name: '<',
        value: '=',
    },
    controller: ['RedisService', TtlForm],
});

function TtlForm(RedisService) {
    this.oldValue = this.value;
    this.isUpdating = false;

    this.isChanged = () => {
        return this.value !== this.oldValue;
    };

    this.isUpdateButtonDisabled = () => {
        return this.isUpdating;
    };

    this.update = () => {
        if(this.isUpdating || !this.isChanged()) {
            return false;
        }
        this.isUpdating = true;
        RedisService.updateTtl(this.key, this.value)
            .then(() => {
                this.oldValue = this.value;
                this.isUpdating = false;
            })
            .catch(() => {
                this.isUpdating = false;
            });
        return false;
    };
}
