<div class="row">
    <form ng-submit="$ctrl.update()">
        <div class="col-xs-6">
            <label for="ttl-{{$ctrl.key}}">
                <b>{{$ctrl.name}} cache time (sec): </b>
            </label>
        </div>
        <div class="col-xs-4">
            <div class="form-group">
                <input
                    id="ttl-{{$ctrl.key}}"
                    type="number"
                    min="0"
                    step="any"
                    class="form-control"
                    ng-model="$ctrl.value"
                    placeholder="Cache livetime, sec. ..."
                />
            </div>
        </div>
        <div class="col-xs-2">
            <button
                type="submit"
                class="btn btn-success btn-block text-center"
                ng-if="$ctrl.isChanged()"
                ng-disabled="$ctrl.isUpdateButtonDisabled()"
            >
                Set
            </button>
        </div>
    </form>
</div>
