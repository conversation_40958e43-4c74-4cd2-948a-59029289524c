angular.module('SportWrench').controller('InvalidMembersCountController', InvalidMembersCountController);

function InvalidMembersCountController ($scope, $http) {
    $scope.data = {
        events: []
    }

    _load();

    function _load () {
        $http({
            method: 'GET',
            url: '/api/admin/teams/invalid/memberscount'
        }).success(function (data) {
            $scope.data.events = data.events;
        })
    }
}
