angular.module('SportWrench').controller('Events.TicketsController', TicketsController);

function TicketsController($scope, EventsService) {
    var event_id = $scope.$parent.eventsRoot.current;
    $scope.data = {};
    $scope.utils = {
        show: false
    }

    $scope.$watch('$parent.eventsRoot.current', function (val) {
        if(!val) return;
        event_id = val;
        EventsService.tickets(event_id).then(function ok (resp) {
            $scope.data = resp.data && resp.data.event;
            $scope.utils.show = true;
        }, function error() {
            // show error message
        }) 
    })

    $scope.save = function () {
        if(!event_id) return;
        EventsService.saveTicketsFee(event_id, $scope.data).then(function ok () {
            console.log('ok')
        }, function error () {
            console.log('not ok')
        })
    }
}
