angular.module('SportWrench').factory('EventsService', function ($http) {
    return {
        events: function () {
            return $http.get('/api/admin/events');
        },
        tickets: function (event_id) {
            return $http.get('/api/admin/event/' + event_id + '/tickets')
        },
        saveTicketsFee: function (event_id, data) {
            return $http.put('/api/admin/event/' + event_id + '/tickets', data)
        }
    }
})
