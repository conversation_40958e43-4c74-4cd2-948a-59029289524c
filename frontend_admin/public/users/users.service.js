angular.module('SportWrench').service('UsersService', UsersService);

function UsersService ($http, $httpParamSerializerJQLike, $localStorage) {
    this.$http           = $http;
    this.paramSerializer = $httpParamSerializerJQLike;
    this.storage         = $localStorage;
}

Object.defineProperty(UsersService.prototype, 'URL_PREFIX', {
    value         : '/api/admin/user',
    writable      : false,
    configurable  : false
});

UsersService.prototype.getUsersList = function (params) {
    return this.$http.get(`${this.URL_PREFIX}/all`, {
        params 				: params,
        paramSerializer 	: '$httpParamSerializerJQLike'
    }).then(response => response.data && response.data.users);
};

UsersService.prototype.loginAs = function (email, token) {
    return this.$http.post('/api/eo/login', { email, password: token })
        .then(response => {
            return response.data && response.data.data && response.data.data.user
        });
};

UsersService.prototype.addUserDataToStorage = function (loggedInUser) {
    if (loggedInUser) {
        this.storage.user = loggedInUser;
    }
};


