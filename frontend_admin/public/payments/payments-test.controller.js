angular.module('SportWrench').controller('PaymentsTestController', function ($scope, $http) {
    $scope.events = $scope.$parent.events;
    $scope.menu = {
        payments_loaded: false,
        progress_bar: {
            value: 0,
            max: 1000,
            animate: true
        },
        in_progress: false,
        stop_progress: false
    };
    $scope.payments = [];

    var _events_watch = $scope.$watch(function () {
        return $scope.$parent.events
    }, function (newVal, oldVal) {
        if(newVal !== oldVal) {
            $scope.events = newVal;
            _events_watch();
        }        
    });

    $scope.load = function () {
        var url = '/api/admin/payments?' +
                        $.param({event: $scope.menu.event, type: $scope.menu.payment_type});   
        $http({
            method: 'GET',
            url: url
        }).success(function (data) {
            $scope.payments = data.payments;
            $scope.menu.payments_loaded = true;
        })
    }

    $scope.stop = function () {
        $scope.stop_progress = true;        
    }

    $scope.find_refunded = function () {
        $scope.menu.progress_bar.value = 0;
        $scope.stop_progress = false;
        $scope.menu.progress_bar.action = 'Finding refunded payments...';
        $scope.menu.in_progress = true;
        var total = 0;
        for(var i = 0 ; i < $scope.payments.length; ++i)
            if($scope.payments[i].type === 'card') ++total;



        _check_refunded(0, function () {
            $scope.menu.in_progress = false;
        })

        function _check_refunded (index, cb) {
            if(index >= $scope.payments.length)
                return cb();
            if ($scope.stop_progress)
                return cb();
            if($scope.payments[index].type !== 'card') return _check_refunded(++index, cb);

            $http.get('/api/admin/payments/' + $scope.payments[index].id + '/refunded')
                .success(function (data) {
                    $scope.menu.progress_bar.value += $scope.menu.progress_bar.max / total;
                    $scope.payments[index].date_refunded = data.refund.refund_created;
                    $scope.payments[index].amount_refunded = data.refund.refunded_amount;   
                    _check_refunded(++index, cb);
                }
            )
        }
    }

    $scope.$on('$destroy', function() {
        $scope.stop_progress = true; 
    });
})
