angular.module('SportWrench').directive('ticketsShowcase', function ($filter) {
    var DATE_FORMAT     = 'MM/dd/yyyy HH:mm+00';
    var DATE_FORMAT_UI  = 'MM/dd/yyyy HH:mm';
    return {
        restrict: 'E',
        scope: {
            load: '&',
            data: '=statData'
        },
        templateUrl: 'public/payments/transfers/tickets.showcase.html',
        link: function (scope) {
            var dateFilter = $filter('date'), fromDateVal, toDateVal;
            scope.filters   = {};
            scope.utils     = {
                typesQty: scope.data.ticket_types.length
            };

            scope.toggleFrom = function () {
                scope.utils.fromOpen = !scope.utils.fromOpen;
            };

            scope.toggleTo = function () {
                scope.utils.toOpen = !scope.utils.toOpen;
            };

            scope.evBalanceClass = function (balance) {
                return {
                    green       : (balance.collected >= 0),
                    red         : (balance.collected < 0)
                };
            };

            scope.trTypeIcon = function (t) {
                return {
                    fa                          : true,
                    'fa-cc-stripe text-info'    : (t.type === 'stripe'),
                    'fa-university'             : (t.type === 'check')
                };
            };

            scope.toTransferClass = function (toTransferAmount) {
                return {
                    'bg-info'       : true,
                    'text-danger'   : (toTransferAmount < 0)
                };
            };

            scope.isFiltersClear = function () {
                return !(fromDateVal || toDateVal);
            };
        }
    };
});

