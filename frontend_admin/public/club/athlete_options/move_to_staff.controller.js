angular.module('SportWrench').controller('Club.Athletes.MoveToStaff', MoveToStaff);

function MoveToStaff($scope) {
    $scope.move_data = {};

    $scope.teams = [];
    $scope.roles = [];

    $scope.updated = false;

    var stopTeamsWatcher = $scope.$parent.$watch('teams', function (newVal) {
        if(!newVal || !newVal.length) return;
        $scope.teams = newVal;
        stopTeamsWatcher();
    });

    var stopRolesWatcher = $scope.$parent.$watch('roles', function (newVal) {
        if(!newVal || !newVal.length) return;
        $scope.roles = newVal;
        stopRolesWatcher();
    });

    $scope.done = function () {
        $scope.updated = true;
    }

    $scope.move = function () {
        $scope.updated = false;
        $scope.$parent.move_to_staff($scope.move_data, $scope.done);
    }
}
