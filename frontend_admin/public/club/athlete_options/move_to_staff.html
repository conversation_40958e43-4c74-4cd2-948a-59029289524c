<p><strong>Move to staff:</strong></p>
<form class="form-inline" name="move_form">

    <div class="row">
        <div class="col-sm-4">
            <div class="form-group">
              <label class="sr-only" for="master_team">Team</label>
              <select 
                    class="form-control"
                    ng-options="t.master_team_id as t.team_name for t in teams | orderBy:'team_name'"
                    ng-model="move_data.master_team_id"
                    id="master_team"
                    >              
                <option ng-selected value="">Select Team...</option>  
              </select>          
          </div>
        </div>
        <div class="col-sm-4">
            <div class="form-group">
              <label class="sr-only" for="staff_roles">Roles</label>
              <select 
                    class="form-control"
                    ng-options="r.id as r.name for r in roles | orderBy:'name'"
                    ng-model="move_data.staff_role"
                    id="staff_roles">  
                <option ng-selected value="">Select Role...</option>            
              </select>          
          </div>
        </div>
    </div>
    <div class="row row-space">
        <div class="col-sm-4">
            <div class="form-group">
              <label class="sr-only" for="bg_screening">BG Screening</label>
              <input 
                  type="number" 
                  class="form-control" 
                  id="bg_screening" 
                  placeholder="BG Screening..."             
                  ng-model="move_data.bg_screening">
          </div>
        </div>
        <div class="col-sm-4">
            <div class="form-group">
              <label class="sr-only" for="certification">Certification</label>
              <input 
                  type="text" 
                  class="form-control" 
                  id="certification"   
                  placeholder="Certification..."             
                  ng-model="move_data.cert">
          </div>
        </div>
        <div class="col-sm-3">
            <button class="btn btn-primary" ng-click="move();">Save</button>
            <i class="fa fa-check green" ng-if="updated"></i>
        </div>
    </div>   
</form>
