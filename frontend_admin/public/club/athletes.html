<div class="row">
    <div class="col-sm-12">
        <p class="lead">Search Club Athletes:</p>
        <form class="form-inline" name="search_form">
            <div class="form-group">
                <label class="sr-only" for="athlete_first">First</label>
                <input 
                    type="text" 
                    class="form-control" 
                    id="athlete_first" 
                    placeholder="First name..."
                    ng-model="search.first">
            </div>
            <div class="form-group">
                <label class="sr-only" for="athlete_last">Last</label>
                <input 
                    type="text" 
                    class="form-control" 
                    id="athlete_last" 
                    placeholder="Last name..."
                    ng-model="search.last">
            </div>
            <div class="form-group">
                <label class="sr-only" for="athlete_email">Email</label>
                <input 
                    type="text" 
                    class="form-control" 
                    id="athlete_email" 
                    placeholder="<EMAIL>..."
                    ng-model="search.email">
            </div>
            <div class="form-group">
                <label class="sr-only" for="athlete_usav">USAV Code</label>
                <input 
                    type="text" 
                    class="form-control" 
                    id="athlete_usav"
                    placeholder="USAV code..."
                    ng-model="search.usav">
            </div>
            
            <button 
                type="submit" 
                class="btn btn-primary"
                ng-click="findAthletes()">Find</button>
            <span class="badge badge-dark" ng-if="athletes.length">{{athletes.length}}</span>
        </form>

        <table class="table table-condensed" ng-if="athletes.length">
            <thead>
                <tr>
                    <th>Id</th>
                    <th>First</th>
                    <th>Last</th>
                    <th>Age</th>
                    <th>USAV</th>
                    <th>Club</th>
                    <th>Email</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="a in athletes" class="pointer" ng-click="openOptionsModal(a)">
                    <td>{{a.master_athlete_id}}</td>
                    <td>{{a.first}}</td>
                    <td>{{a.last}}</td>
                    <td>{{a.age || 'N/A'}}</td>
                    <td>{{a.organization_code}}</td>
                    <td>{{a.club_name}}</td>
                    <td>{{a.email}}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
