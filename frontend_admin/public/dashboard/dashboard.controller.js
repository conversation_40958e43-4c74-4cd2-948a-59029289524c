angular.module('SportWrench').controller('DashboardCtrl', DashboardCtrl);

function DashboardCtrl($scope, $http, $window, HOME_PAGE_URL) {
    $scope.ping = {
        officialsSchedule: {
            apiPing: null,
            error: null
        }
    }

    //$window.document.cookie = 'remember_me=70c2e0af4123844da4c605c668d1fa946a45055d63e45ab8dfa11f49370f6c54, domain=dev.sportwrench.com';
    //$window.document.cookie = 'sails.sid=s%3A8FZTUd0hOqkQg8BuMGtaU_5M.NJBfnHRhfckE7Uv39USEr6zWv6zqJWbO7pbpJ9miTZE, domain=dev.sportwrench.com';
    $scope.testScheduleAPI = function () {
        $http.get(HOME_PAGE_URL + '/api/schedule/21').success(function () {
            $scope.ping.officialsSchedule.apiPing = true;
            $scope.ping.officialsSchedule.error = null
        }).error(function (data) {        
            $scope.ping.officialsSchedule.apiPing = false;
            $scope.ping.officialsSchedule.error = data;
        })
    }    
}
