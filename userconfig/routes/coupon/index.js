const Controller = 'Event/CouponController';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/coupon/ticket-types Coupon Ticket Types
     * @apiDescription Returns event ticket types available for coupons
     * @apiGroup Coupon
     *
     */
    'GET /api/event/:event/coupon/ticket-types': `${Controller}.getTicketTypes`,

    /**
     *
     * @api {post} /api/event/:event/coupon/generation-task Coupon Generation Task Creation
     * @apiDescription Creates coupons generation task
     * @apiGroup Coupon
     *
     */
    'POST /api/event/:event/coupon/generation-task': `${Controller}.createGenerationTask`,

    /**
     *
     * @api {get} /api/event/:event/coupon/list Coupon List
     * @apiDescription Returns coupons list on event
     * @apiGroup Coupon
     *
     */
    'GET /api/event/:event/coupon/list': `${Controller}.getCouponsList`,

    /**
     *
     * @api {put} /api/event/:event/coupons Coupons Update
     * @apiDescription Updates coupons
     * @apiGroup Coupon
     *
     */
    'PUT /api/event/:event/coupons': `${Controller}.updateCoupons`,

    /**
     *
     * @api {put} /api/event/:event/coupon/:coupon/active Coupon Active Status Change
     * @apiDescription Updates coupon active status
     * @apiGroup Coupon
     *
     */
    'PUT /api/event/:event/coupon/:coupon/active': `${Controller}.updateCouponActiveStatus`,

    /**
     *
     * @api {put} /api/event/:event/coupons/sending Coupon Sending
     * @apiDescription Sends coupons
     * @apiGroup Coupon
     *
     */
    'PUT /api/event/:event/coupons/sending': `${Controller}.sendCoupons`,

    /**
     *
     * @api {post} /api/event/:event/coupon Coupon Creation
     * @apiDescription Creates coupon
     * @apiGroup Coupon
     *
     */
    'POST /api/event/:event/coupon': `${Controller}.createCoupon`,

    /**
     *
     * @api {get} /api/event/:event/coupon/:code/tickets Coupon Bought Tickets List
     * @apiDescription Bought tickets list using specific coupon
     * @apiGroup Coupon
     *
     */
    'GET /api/event/:event/coupon/:code/tickets': `${Controller}.boughtTicketsList`,
};
