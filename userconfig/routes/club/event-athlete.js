

module.exports = {
    /**
     *
     * @api {post} /api/club/event/:event/athlete/:athlete/withdraw Athlete Withdrawal
     * @apiDescription Removes athlete from team's roster
     * @apiGroup Club Event Athlete
     *
     */
    'post /api/club/event/:event/athlete/:athlete/withdraw': 'Club/RosterAthleteController.withdraw',

    /**
     *
     * @api {get} /api/club/event/:event/athlete/:athlete/info Athlete Info
     * @apiDescription Gets roster athlete data
     * @apiGroup Club Event Athlete
     *
     */
    'get /api/club/event/:event/athlete/:athlete/info': 'Club/RosterAthleteController.getAthlete',

    /**
     *
     * @api {put} /api/club/event/:event/athlete/:athlete/update Athlete Update
     * @apiDescription Updates roster athlete data
     * @apiGroup Club Event Athlete
     *
     */
    'put /api/club/event/:event/athlete/:athlete/update': 'Club/RosterAthleteController.update',

    /**
     *
     * @api {post} /api/club/event/:event/athlete/:athlete/reinstate Athlete Reinstate
     * @apiDescription Reinstate athlete in team
     * @apiGroup Club Event Athlete
     *
     */
    'post /api/club/event/:event/athlete/:athlete/reinstate': 'Club/RosterAthleteController.reinstate',

    /**
     *
     * @api {post} /api/v2/club/athletes/events_list Athlete Events
     * @apiDescription Gets athletes' assigned events
     * @apiGroup Club Event Athlete
     *
     */
    'post /api/v2/club/athletes/events_list' : 'Club/RosterAthleteController.getAssignedEvents',
}
