

module.exports = {
    /**
     *
     * @api {get} /api/club/event/:event/staff/:staff/info Staff Info
     * @apiDescription Gets roster staff Data
     * @apiGroup Club Event Staff
     *
     */
    'get /api/club/event/:event/staff/:staff/info': 'Club/RosterStaffController.findRosterStaff',

    /**
     *
     * @api {get} /api/club/event/:event/staff/:staff/primary-teams Staff Primary Teams
     * @apiDescription Gets staff primary teams list
     * @apiGroup Club Event Staff
     *
     */
    'get /api/club/event/:event/staff/:staff/primary-teams': 'Club/RosterStaffController.findStaffPrimaryTeams',

    /**
     *
     * @api {put} /api/club/event/:event/staff/:staff/update Staff Update
     * @apiDescription Updates roster staffer
     * @apiGroup Club Event Staff
     *
     */
    'put /api/club/event/:event/staff/:staff/update': 'Club/RosterStaffController.update',

    /**
     *
     * @api {post} /api/club/event/:event/staff/:staff/withdraw Staff Withdraw
     * @apiDescription Removes staffer from team's roster
     * @apiGroup Club Event Staff
     *
     */
    'post /api/club/event/:event/staff/:staff/withdraw': 'Club/RosterStaffController.withdraw',

    /**
     *
     * @api {post} /api/club/event/:event/staff/:staff/reinstate Staff Reinstate
     * @apiDescription Reinstate staff in team
     * @apiGroup Club Event Staff
     *
     */
    'post /api/club/event/:event/staff/:staff/reinstate': 'Club/RosterStaffController.reinstate',

    /**
     *
     * @api {post} /api/v2/club/staff/events_list Staffer's Events
     * @apiDescription Gets staffer' assigned events
     * @apiGroup Club Event Staff
     *
     */
    'post /api/v2/club/staff/events_list': 'Club/RosterStaffController.findAssignedEvents',
}
