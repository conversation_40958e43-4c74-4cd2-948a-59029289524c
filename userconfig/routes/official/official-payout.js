'use strict';

const OFFICIAL_PAYOUT_CTRL = 'Event/OfficialPayoutController';
const EVENT_OFFICIAL_CTRL = 'Event/OfficialsController';

module.exports = {
    /**
     *
     * @api {put} /api/event/:event/type/:type/payouts/list Payouts List
     * @apiDescription Returns payouts list
     * @apiGroup Official Payout
     *
     */
    'GET /api/event/:event/type/:type/payouts/list': `${OFFICIAL_PAYOUT_CTRL}.getPayouts`,

    /**
     *
     * @api {post} /api/event/:event/type/:type/:id/payouts Mark Payout Paid
     * @apiDescription Mark a specific payout as paid
     * @apiGroup Official Payout
     *
     */
    'POST /api/event/:event/type/:type/:id/payouts': `${OFFICIAL_PAYOUT_CTRL}.markPayout`,

    /**
     *
     * @api {get} /api/event/:event/type/:type/:id/payouts Payout Info
     * @apiDescription Returns a specific payout info
     * @apiGroup Official Payout
     *
     */
    'GET /api/event/:event/type/:type/:id/payouts': `${OFFICIAL_PAYOUT_CTRL}.payoutsInfo`,

    /**
     *
     * @api {put} /api/event/:event/officials/:official/payout Create Stripe Payout
     * @apiDescription Creates Stripe payout
     * @apiGroup Official Payout
     *
     */
    'put /api/event/:event/officials/:official/payout': `${EVENT_OFFICIAL_CTRL}.makePayout`,

    /**
     *
     * @api {get} /api/event/:event/officials/payouts/history Payouts History
     * @apiDescription Returns payouts history
     * @apiGroup Official Payout
     *
     */
    'get /api/event/:event/officials/payouts/history': `${EVENT_OFFICIAL_CTRL}.getPayoutsHistory`,

    /**
     *
     * @api {get} /api/event/:event/officials/payouts/dashboard_url Stripe Dashboard URL
     * @apiDescription Returns officials Stripe dashboard link
     * @apiGroup Official Payout
     *
     */
    'get /api/event/:event/officials/payouts/dashboard_url': `${EVENT_OFFICIAL_CTRL}.getOfficialStripeDashboardURL`,

    /**
     *
     * @api {get} /api/event/:event/officials/payouts/create_express_account_url Generate Express Stripe Account Creation Link
     * @apiDescription Returns generated express Stripe account creation link
     * @apiGroup Official Payout
     *
     */
    'get /api/event/:event/officials/payouts/create_express_account_url':
        `${EVENT_OFFICIAL_CTRL}.generateExpressAcccountCreationLink`,
    /**
     *
     * @api {get} /api/event/:event/officials/payouts/export Export Officials Payouts
     * @apiDescription Export staffers payouts list to XLSX file
     * @apiGroup Officials
     *
     */
    'get /api/event/:event/officials/payouts/export': `${OFFICIAL_PAYOUT_CTRL}.export`,
};
