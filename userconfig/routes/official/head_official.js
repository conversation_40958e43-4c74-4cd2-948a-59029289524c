'use strict';

const ctrlPath = 'Official/OfficialsController';

const routes = {
    /**
     *
     * @api {get} /api/official/event/:event/officials Event Officials List
     * @apiDescription Returns officials list assigned on a specific event
     * @apiGroup Head Official
     *
     */
	'get /api/official/event/:event/officials': 'index',

    /**
     *
     * @api {get} /api/official/event/:event/official/:official Event Official
     * @apiDescription Returns official assigned on a specific event
     * @apiGroup Head Official
     *
     */
    'get /api/official/event/:event/official/:official': 'find',

    /**
     *
     * @api {get} /api/official/event/:event/officials/export Export Officials List
     * @apiDescription Exports officials list to XLSX file
     * @apiGroup Head Official
     *
     */
    'get /api/official/event/:event/officials/export': 'export_to_excel',

    /**
     *
     * @api {put} /api/official/event/:event/official/:official/update Update Registration Data
     * @apiDescription Updates official's registration data
     * @apiGroup Head Official
     *
     */
    'put /api/official/event/:event/official/:official/update': 'update',

    /**
     *
     * @api {put} /api/official/event/:event/officials/update Update Registration Data (Group)
     * @apiDescription Updates official's registration data for officials group
     * @apiGroup Head Official
     *
     */
    'put /api/official/event/:event/officials/update': 'updateGroup',

    /**
     * @api {put} /api/official/event/:event/official/:official/additional-restrictions Update Additional Restrictions
     * @apiDescription Updates official's additional restrictions
     * @apiGroup Head Official
     */
    'put /api/official/event/:event/official/:official/additional-restrictions': 'updateAdditionalRestrictions'
};

module.exports = Object.keys(routes).reduce((_routes, route) => {
	_routes[route] = `${ctrlPath}.${routes[route]}`;
	return _routes;
}, {});
