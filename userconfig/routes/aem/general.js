'use strict';

const CTRL = 'AEM/GeneralController.';

module.exports = {
    /**
     *
     * @api {get} /api/aem/editor-token BeeEditor token
     * @apiDescription Returns BeeEditor plugin token
     * @apiGroup AEM General
     *
     */
	'GET /api/aem/editor-token' : `${CTRL}getEditorToken`,

    /**
     *
     * @api {get} /api/aem/group/:group/types Template Group Types List
     * @apiDescription Returns email template group types list
     * @apiGroup AEM General
     *
     */
	'GET /api/aem/group/:group/types': `${CTRL}getGroupTypes`,

    /**
     *
     * @api {get} /api/aem/group/:group/variables Template Group Variables List
     * @apiDescription Returns email template group variables list
     * @apiGroup AEM General
     *
     */
	'GET /api/aem/group/:group/variables': `${CTRL}getVariables`,

    /**
     *
     * @api {get} /api/aem/basic-layouts Basic Layouts List
     * @apiDescription Returns basic template layouts list
     * @apiGroup AEM General
     *
     */
	'GET /api/aem/basic-layouts': `${CTRL}getBasicLayouts`,

    /**
     *
     * @api {post} /api/aem/mass-sending Mass Email Sending
     * @apiDescription Creates email sending system job
     * @apiGroup AEM General
     *
     */
    'POST /api/aem/mass-sending': `${CTRL}massSending`
};
