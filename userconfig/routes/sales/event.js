

module.exports = {
    /**
     *
     * @api {get} /api/sales/events Events List
     * @apiDescription Returns events list
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/events': 'SalesManager/EventController.index',

    /**
     *
     * @api {get} /api/sales/event/:event Event Info
     * @apiDescription Returns a specific event data
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/event/:event': 'SalesManager/EventController.get_event',

    /**
     *
     * @api {get} /api/sales/events/published/short Published Events List (Short Data)
     * @apiDescription Returns published events list short data
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/events/published/short': 'SalesManager/EventController.published_events',

    /**
     *
     * @api {get} /api/sales/events/report Booths Report
     * @apiDescription Returns booths report for all events
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/events/report': 'SalesManager/EventController.report',

    /**
     *
     * @api {get} /api/sales/event/:event/report Events Booths Report
     * @apiDescription Returns booths report for a specific event
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/event/:event/report': 'SalesManager/EventController.details',

    /**
     *
     * @api {get} /api/sales/event/:event/sponsor/export Exports Booths Data
     * @apiDescription Exports booths data
     * @apiGroup Sales Event
     *
     */
    'GET /api/sales/event/:event/sponsor/export': 'SalesManager/EventController.export',
}
