const CONTROLLER = 'Sponsor/ReceiptController';

module.exports = {
    /**
     *
     * @api {get} /api/sponsor/event/:event/receipts/:receipt Sponsor Receipt Info
     * @apiDescription Returns specific sponsor receipt info
     * @apiGroup Sponsor Event Receipt
     *
     */
    'GET /api/sponsor/event/:event/receipts/:receipt': `${CONTROLLER}.getReceiptInfo`,

    /**
     *
     * @api {put} /api/sponsor/event/:event/receipts/:receipt Update Sponsor Receipt
     * @apiDescription Updates specific sponsor receipt
     * @apiGroup Sponsor Event Receipt
     *
     */
    'PUT /api/sponsor/event/:event/receipts/:receipt': `${CONTROLLER}.updateReceipt`,

    /**
     *
     * @api {post} /api/sponsor/event/:event/receipts Create Sponsor Receipt
     * @apiDescription Creates new sponsor receipt
     * @apiGroup Sponsor Event Receipt
     *
     */
    'POST /api/sponsor/event/:event/receipts': `${CONTROLLER}.createReceipt`
};
