

module.exports = {
    /**
     *
     * @api {get} /api/sponsor/profile Own Profile Info
     * @apiDescription Returns user's own profile info
     * @apiGroup Sponsor Profile
     *
     */
    'GET /api/sponsor/profile': 'Sponsor/ProfileController.find',

    /**
     *
     * @api {get} /api/sponsor/profile/:sponsor Sponsor Profile Data
     * @apiDescription Returns specific sponsor profile data
     * @apiGroup Sponsor Profile
     *
     */
    'GET /api/sponsor/profile/:sponsor': 'Sponsor/ProfileController.find',

    /**
     *
     * @api {post} /api/sponsor/profile Create Sponsor Profile
     * @apiDescription Creates sponsor profile
     * @apiGroup Sponsor Profile
     *
     */
    'POST /api/sponsor/profile': 'Sponsor/ProfileController.create',

    /**
     *
     * @api {put} /api/sponsor/profile/:sponsor Update Sponsor Profile
     * @apiDescription Updates specific sponsor profile
     * @apiGroup Sponsor Profile
     *
     */
    'PUT /api/sponsor/profile/:sponsor': 'Sponsor/ProfileController.update',

    /**
     *
     * @api {put} /api/sponsor/profile Update Sponsor Profile (Own)
     * @apiDescription Updates own sponsor profile
     * @apiGroup Sponsor Profile
     *
     */
    'PUT /api/sponsor/profile': 'Sponsor/ProfileController.update',

    /**
     *
     * @api {get} /api/sponsor/:sponsor/info Sponsor Profile Info
     * @apiDescription Returns specific sponsor profile info
     * @apiGroup Sponsor Profile
     *
     */
    'GET /api/sponsor/:sponsor/info': 'Sponsor/ProfileController.info',
}
