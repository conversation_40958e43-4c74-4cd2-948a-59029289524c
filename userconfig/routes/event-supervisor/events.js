'use strict';

const CTRL = 'Supervisor/EventController.';

const routes = {
    /**
     *
     * @api {get} /api/event-supervisor/events Events List
     * @apiDescription Returns events list
     * @apiGroup Event Supervisor
     *
     */
	'GET /api/event-supervisor/events': `${CTRL}eventsList`,

    /**
     *
     * @api {get} /api/event-supervisor/events/:event/stripe-account Event Stripe Account Info
     * @apiDescription Returns stripe account info for a specific event
     * @apiGroup Event Supervisor
     *
     */
	'GET /api/event-supervisor/events/:event/stripe-account': `${CTRL}stripeAccountInfo`,

    /**
     *
     * @api {get} /api/event-supervisor/events/:event/monetary Event Monetary Info
     * @apiDescription Returns event monetary info (fees and percents)
     * @apiGroup Event Supervisor
     *
     */
	'GET /api/event-supervisor/events/:event/monetary': `${CTRL}eventMonetaryInfo`,

    /**
     *
     * @api {post} /api/event-supervisor/events/:event/monetary Event Monetary Info Update
     * @apiDescription Updates event monetary info (fees and percents)
     * @apiGroup Event Supervisor
     *
     */
	'POST /api/event-supervisor/events/:event/monetary': `${CTRL}updateMonetary`
};

module.exports = routes;
