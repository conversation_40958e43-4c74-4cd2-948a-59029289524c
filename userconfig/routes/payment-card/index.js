const CTRL = 'PaymentCardController.';

module.exports = {
    /**
     *
     * @api {get} /api/eo/payment-card Cards List
     * @apiDescription Returns payment cards list
     * @apiGroup Payment Card
     *
     */
    'GET /api/eo/payment-card': `${CTRL}list`,

    /**
     *
     * @api {post} /api/eo/payment-card Card Creation
     * @apiDescription Creates payment card
     * @apiGroup Payment Card
     *
     */
    'POST /api/eo/payment-card': `${CTRL}add`,

    /**
     *
     * @api {post} /api/eo/payment-bank-account Bank Account Creation
     * @apiDescription Creates payment bank account
     * @apiGroup Payment Card
     *
     */
    'POST  /api/eo/payment-bank-account': `${CTRL}addBankAccount`,

     /**
     *
     * @api {post} /api/eo/payment-bank-account/session Bank Account Session Creation
     * @apiDescription Creates payment bank account session
     * @apiGroup Payment Card
     *
     */
     'POST  /api/eo/payment-bank-account/session': `${CTRL}createFinancialConnectionSession`,

    
    /**
     *
     * @api {delete} /api/eo/payment-card Card Remove
     * @apiDescription Removes payment card
     * @apiGroup Payment Card
     *
     */
    'DELETE /api/eo/payment-card': `${CTRL}remove`,

    /**
     *
     * @api {get} /api/eo/payment-card/settings Stripe Setup Intent Creation
     * @apiDescription Creates Stripe setup intent for payment method saving
     * @apiGroup Payment Card
     *
     */
    'GET /api/eo/payment-card/settings': `${CTRL}settings`,

    /**
     *
     * @api {put} /api/eo/payment-card/default Set Default Card
     * @apiDescription Marks payment card as default
     * @apiGroup Payment Card
     *
     */
    'PUT /api/eo/payment-card/default': `${CTRL}setDefault`
}
