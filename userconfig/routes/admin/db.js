
const DB_CTRL = 'Admin/DbController';

module.exports = {
    /**
     *
     * @api {get} /api/admin/db-connections DB Connections Quantity
     * @apiDescription Returns DB connections quantity
     * @apiGroup Admin DB
     *
     */
    'GET /api/admin/db-connections': `${DB_CTRL}.connectionsQty`,

    /**
     *
     * @api {get} /api/admin/db/replace/linebreaks Remove Linebreaks from Results Tables
     * @apiDescription Removes line breaks from all fields in tables 'courts', 'matches', 'poolbrackets', 'rounds', 'results'
     * @apiGroup Admin DB
     *
     */
    'GET /api/admin/db/replace/linebreaks': `${DB_CTRL}.replaceLineBreaks`,

    /**
     *
     * @api {post} /api/admin/db/copy-prod Copy Event from Prod (not used)
     * @apiDescription Copies event from prod (not used)
     * @apiGroup Admin DB
     *
     */
    'POST /api/admin/db/copy-prod': {
        controller: DB_CTRL,
        action: 'copyProdDB',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/db/copy-prod/:table Copy DB Table Data from Prod
     * @apiDescription Copies table data from prod
     * @apiGroup Admin DB
     *
     */
    'POST /api/admin/db/copy-prod/:table': {
        controller: DB_CTRL,
        action: 'copyProdDBTable',
        csrf: false,
    },
}
