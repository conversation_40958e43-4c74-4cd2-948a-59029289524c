
const CLUB_MEMBERS_CTRL = 'Admin/ClubMembersController';

module.exports = {
    /**
     *
     * @api {post} /api/admin/club/master/copy-member/:from(athlete|staff)/:to(athlete|staff) Copy Athlete to Staff or reverse
     * @apiDescription Copies master athlete to master staff table or reverse
     * @apiGroup Admin Club Members
     *
     */
    'POST /api/admin/club/master/copy-member/:from/:to': {
        controller: CLUB_MEMBERS_CTRL,
        action: 'createOppositeRoleForMember',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/club/master/athlete/tostaff Move Athlete to Staff
     * @apiDescription Moves master athlete to master staff table
     * @apiGroup Admin Club Members
     *
     */
    'POST /api/admin/club/master/athlete/tostaff': {
        controller: CLUB_MEMBERS_CTRL,
        action: 'masterAthleteToStaff',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/admin/club/master/athlete/find Master Athletes Search
     * @apiDescription Returns master athletes list by selected fields values
     * @apiGroup Admin Club Members
     *
     */
    'GET /api/admin/club/master/athlete/find': `${CLUB_MEMBERS_CTRL}.findMasterAthletes`,

    /**
     *
     * @api {get} /api/admin/club/staff/roles Staff Roles List
     * @apiDescription Returns club staffers roles list
     * @apiGroup Admin Club Members
     *
     */
    'GET /api/admin/club/staff/roles': `${CLUB_MEMBERS_CTRL}.staffRoles`,

    /**
     *
     * @api {get} /api/admin/club/:club/teams Teams List
     * @apiDescription Returns club teams list
     * @apiGroup Admin Club Members
     *
     */
    'GET /api/admin/club/:club/teams': `${CLUB_MEMBERS_CTRL}.clubTeams`,

    /**
     *
     * @api {get} /api/admin/club/members/find Club Members Search
     * @apiDescription Returns club members
     * @apiGroup Admin Club Members
     *
     */
    'GET /api/admin/club/members/find': `${CLUB_MEMBERS_CTRL}.findMembers`,

    /**
     *
     * @api {post} /api/admin/club/athletes/update-height Update athletes height
     * @apiDescription Updates athletes without height_int
     * @apiGroup Admin Club Members
     *
     */
    'POST /api/admin/club/athletes/update-height': {
        controller: CLUB_MEMBERS_CTRL,
        action: 'updateAthleteHeight',
        csrf: false,
    }
}
