
const STATS_CTRL = 'Admin/StatisticsController.';

module.exports = {
    /**
     *
     * @api {get} /api/admin/sw-statistics/events-list Events List
     * @apiDescription Returns events list for on statistic page
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/events-list': `${STATS_CTRL}getEventsList`,

    /**
     *
     * @api {get} /api/admin/sw-statistics/event/:event/stats Event Statistic
     * @apiDescription Returns event statistic data
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/event/:event/stats': `${STATS_CTRL}getEventStatisitcs`,

    /**
     *
     * @api {get} /api/admin/sw-statistics/stripe SW Stripe Platform Statistic
     * @apiDescription Returns Stripe SW platform account statistic
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/stripe': `${STATS_CTRL}getStripeAccStatistics`,

    /**
     *
     * @api {get} /api/admin/sw-statistics/stripe/financial-report Stripe Financial Report (not used?)
     * @apiDescription Generates Stripe financial statistic report (not used?) - mostly the same as route below
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/stripe/financial-report': `${STATS_CTRL}getFinancialReport`,

    /**
     *
     * @api {post} /api/admin/sw-statistics/stripe/financial-data Stripe Financial Data Request
     * @apiDescription Generates Stripe financial statistic data
     * @apiGroup Admin Statistic
     *
     */
    'POST /api/admin/sw-statistics/stripe/financial-data': `${STATS_CTRL}createFinancialDataRequest`,

    /**
     *
     * @api {get} /api/admin/sw-statistics/stripe/financial-data/:report_id Stripe Financial Data Job Result
     * @apiDescription Returns Stripe financial statistic data job result
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/stripe/financial-data/:report_id': `${STATS_CTRL}getFinancialDataRequestResult`,

    /**
     *
     * @api {get} /api/admin/sw-statistics/stripe/financial-export Stripe Financial Data Export
     * @apiDescription Generates Stripe financial statistic data export file
     * @apiGroup Admin Statistic
     *
     */
    'GET /api/admin/sw-statistics/stripe/financial-export': `${STATS_CTRL}getFinancialExport`,
}
