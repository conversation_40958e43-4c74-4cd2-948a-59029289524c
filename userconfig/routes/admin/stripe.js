
const STRIPE_CTRL = 'Admin/StripeAccountsController';

module.exports = {
    /**
     *
     * @api {get} /api/admin/payments/stripe/migrate-to-managed Migrate Stripe Recipients to Managed Accounts
     * @apiDescription Stripe recipients migration to managed accounts
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/migrate-to-managed': `${STRIPE_CTRL}.migrateFromRecipients`,

    /**
     *
     * @api {get} /api/admin/payments/stripe/connected-accounts Stripe Connected Accounts List
     * @apiDescription Returns all connected to SW Stripe platform accounts
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/connected-accounts': `${STRIPE_CTRL}.listConnectedAccounts`,

    /**
     *
     * @api {post} /api/admin/payments/stripe/create-managed Create Stripe Managed Account
     * @apiDescription Creates Stripe managed account
     * @apiGroup Admin Stripe
     *
     */
    'POST /api/admin/payments/stripe/create-managed': {
        controller: STRIPE_CTRL,
        action: 'createManagedAccount',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/payments/stripe/transfer-amount Create Stripe Transfer
     * @apiDescription Creates Stripe transfer
     * @apiGroup Admin Stripe
     *
     */
    'POST /api/admin/payments/stripe/transfer-amount': {
        controller: STRIPE_CTRL,
        action: 'createTransfer',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/payments/stripe/save-check Save Check to Transfers
     * @apiDescription Creates transfer with "check" type
     * @apiGroup Admin Stripe
     *
     */
    'POST /api/admin/payments/stripe/save-check': {
        controller: STRIPE_CTRL,
        action: 'saveCheck',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/admin/payments/stripe/:account/events-list Stripe Account Events List
     * @apiDescription Returns events list using specific Stripe account
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/:account/events-list': `${STRIPE_CTRL}.eventsList`,

    /**
     *
     * @api {get} /api/admin/payments/stripe/event/:event/stats Event Ticket Statistic
     * @apiDescription Returns tickets statistic for a specific event
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/event/:event/stats': `${STRIPE_CTRL}.eventStats`,

    /**
     *
     * @api {get} /api/admin/payments/stripe/account/:account/history Stripe Account Transfers List
     * @apiDescription Returns transfers list for a specific Stripe account
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/account/:account/history': `${STRIPE_CTRL}.accountHistory`,

    /**
     *
     * @api {post} /api/admin/payments/stripe/account/:account/docs Upload Stripe Account Document
     * @apiDescription Uploads documents to Stripe Account
     * @apiGroup Admin Stripe
     *
     */
    'POST /api/admin/payments/stripe/account/:account/docs': {
        controller: STRIPE_CTRL,
        action: 'uploadAccountDocs',
        csrf: false,
    },

    /**
     *
     * @api {delete} /api/admin/payments/stripe/account/:account Remove Managed Account
     * @apiDescription Removes managed Stripe account
     * @apiGroup Admin Stripe
     *
     */
    'DELETE /api/admin/payments/stripe/account/:account': {
        controller: STRIPE_CTRL,
        action: 'removeManagedAccount',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/admin/payments/stripe/account/sw-acc/balance SW Stripe Platform Balance
     * @apiDescription Retrieve SW Platform balance value from Stripe API
     * @apiGroup Admin Stripe
     *
     */
    'GET /api/admin/payments/stripe/account/sw-acc/balance': `${STRIPE_CTRL}.retrieveSWAccBalance`,
}
