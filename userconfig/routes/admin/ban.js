'use strict';

const BAN_CTRL   = 'Admin/BanController.';

module.exports = {
    /**
     *
     * @api {get} /api/admin/ban/email Banned Emails List
     * @apiDescription Returns banned emails list
     * @apiGroup Admin Ban
     *
     */
    'GET /api/admin/ban/email': `${BAN_CTRL}listEmail`,

    /**
     *
     * @api {get} /api/admin/v2/ban/email/ Banned Emails List V2
     * @apiDescription Returns banned emails list V2
     * @apiGroup Admin Ban
     *
     */
     'GET /api/admin/v2/ban/email': `${BAN_CTRL}listEmailV2`,
    
    /**
     *
     * @api {get} /api/admin/ban/fingerprint Banned Card Fingerprints List
     * @apiDescription Returns banned card fingerprints list
     * @apiGroup Admin Ban
     *
     */
    'GET /api/admin/ban/fingerprint': `${BAN_CTRL}listFingerprint`,

    /**
     *
     * @api {get} /api/admin/ban/fingerprint Banned Card Fingerprints List
     * @apiDescription Returns banned card fingerprints list
     * @apiGroup Admin Ban
     *
     */
     'GET /api/admin/v2/ban/fingerprint': `${BAN_CTRL}listFingerprintV2`,

    /**
     *
     * @api {post} /api/admin/ban/email Add Email to Ban List
     * @apiDescription Adds email to ban list
     * @apiGroup Admin Ban
     *
     */
    'POST /api/admin/ban/email': `${BAN_CTRL}addEmail`,

    /**
     *
     * @api {post} /api/admin/ban/fingerprint Add Fingerprint to Ban List
     * @apiDescription Adds fingerprint to ban list
     * @apiGroup Admin Ban
     *
     */
    'POST /api/admin/ban/fingerprint': `${BAN_CTRL}addFingerprint`,

    /**
     *
     * @api {delete} /api/admin/ban/email/:email Remove Email from Ban List
     * @apiDescription Removes email from ban list
     * @apiGroup Admin Ban
     *
     */
    'DELETE /api/admin/ban/email/:email': `${BAN_CTRL}removeEmail`,

    /**
     *
     * @api {delete} /api/admin/ban/fingerprint/:fingerprint Remove Fingerprint from Ban List
     * @apiDescription Removes fingerprint from ban list
     * @apiGroup Admin Ban
     *
     */
    'DELETE /api/admin/ban/fingerprint/:fingerprint': `${BAN_CTRL}removeFingerprint`,
};
