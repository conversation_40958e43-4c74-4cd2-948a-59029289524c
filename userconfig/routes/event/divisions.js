'use strict';

const
	CTRL = 'Event/DivisionController.'

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/divisions Event Divisions List
     * @apiDescription Returns list of event divisions
     * @apiGroup Event Division
     *
     */
	'get /api/event/:event/divisions': `${CTRL}all`,

    /**
     *
     * @api {post} /api/event/:event/division Event Division Creation
     * @apiDescription Creates an event division
     * @apiGroup Event Division
     *
     */
    'post /api/event/:event/division': `${CTRL}create`,

    /**
     *
     * @api {get} /api/event/:event/division/:division Event Division Info
     * @apiDescription Returns a specific event division
     * @apiGroup Event Division
     *
     */
    'get /api/event/:event/division/:division': `${CTRL}find`,

    /**
     *
     * @api {put} /api/event/:event/division/:division Event Division Update
     * @apiDescription Updates a specific event division
     * @apiGroup Event Division
     *
     */
    'put /api/event/:event/division/:division': `${CTRL}update`,

    /**
     *
     * @api {put} /api/event/:event/division/:division/full Event Division Mark as Full
     * @apiDescription Marks a division as full
     * @apiGroup Event Division
     *
     */
    'put /api/event/:event/division/:division/full': `${CTRL}markFull`,

    /**
     *
     * @api {delete} /api/event/:event/division/:division/remove Event Division Remove
     * @apiDescription Removes division
     * @apiGroup Event Division
     *
     */
    'delete /api/event/:event/division/:division/remove': `${CTRL}remove`
}
