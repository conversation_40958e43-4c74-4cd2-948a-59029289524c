'use strict';

const CTRL = 'Event/StaffController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/staff Staffers List
     * @apiDescription Returns event staffers list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/staff': `${CTRL}getAll`,

    /**
     *
     * @api {get} /api/event/:event/clothing/:role Staff/Officials Clothing List
     * @apiDescription Returns event staffers or officials clothing list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/clothing/:role': `${CTRL}getClothingList`,

    /**
     *
     * @api {post} /api/event/:event/clothing/:role/export Export Staff/Officials Clothing List
     * @apiDescription Exports event staffers or officials clothing list
     * @apiGroup Event Staff
     *
     */
    'post /api/event/:event/clothing/:role/export': `${CTRL}exportClothingList`,

    /**
     *
     * @api {get} /api/event/:event/clothing/:role/export/:id Pick exported Staff/Officials Clothing List
     * @apiDescription Returns exported event staffers or officials clothing list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/clothing/:role/export/:id': `${CTRL}getExportedClothingList`,

    /**
     *
     * @api {get} /api/event/:event/travel/:role Staff/Officials Travel List
     * @apiDescription Returns event staffers or officials travel list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/travel/:role': `${CTRL}getTravelList`,

    /**
     *
     * @api {post} /api/event/:event/travel/:role/export Export Staff/Officials Travel List
     * @apiDescription Exports event staffers or officials travel list
     * @apiGroup Event Staff
     *
     */
    'POST /api/event/:event/travel/:role/export': `${CTRL}exportTravelList`,

    /**
     *
     * @api {get} /api/event/:event/travel/:role/export/:id Pick exported Staff/Officials Travel List
     * @apiDescription Returns exported event staffers or officials travel list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/travel/:role/export/:id': `${CTRL}getExportedTravelList`,

    /**
     *
     * @api {put} /api/event/:event/staff/:staff/update Update Staffer
     * @apiDescription Updates staffer data
     * @apiGroup Event Staff
     *
     */
    'PUT /api/event/:event/staff/:staff/update': `${CTRL}update`,

    /**
     *
     * @api {get} /api/event/:event/staff/:staff Staffer Info
     * @apiDescription Returns staffer data
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/staff/:staff': `${CTRL}getInfo`,

    /**
     *
     * @api {put} /api/event/:event/staffers/update Update Staff/Officials Work Status
     * @apiDescription Updates staffers or officials work status (group update)
     * @apiGroup Event Staff
     *
     */
    'PUT /api/event/:event/staffers/update': `${CTRL}updateRegInfoGroup`,

    /**
     *
     * @api {get} /api/event/:event/staffers/export Export Event Staffers
     * @apiDescription Export event staffers list
     * @apiGroup Event Staff
     *
     */
    'GET /api/event/:event/staffers/export': `${CTRL}exportToExcel`,
};
