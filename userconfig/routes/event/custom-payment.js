'use strict';

const Controller = 'Event/CustomPaymentController.'

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/custom-payment/info Event Custom Payment Method Details
     * @apiDescription Returns custom payment method details
     * @apiGroup Event Custom Payment
     *
     */
    'GET /api/event/:event/custom-payment/info': `${Controller}getEventMethodDetails`,

    /**
     *
     * @api {post} /api/event/:event/custom-payment/pay Event Custom Payment Creation
     * @apiDescription Creates custom payment
     * @apiGroup Event Custom Payment
     *
     */
    'POST /api/event/:event/custom-payment/pay': `${Controller}createCustomPayment`,

    /**
     *
     * @api {get} /api/eo/custom-payment/confirm Event Custom Payments Require Confirmation
     * @apiDescription Returns Custom Payments require confirmation
     * @apiGroup Event Custom Payment
     *
     */
    'GET /api/eo/custom-payment/confirm': `${Controller}getPaymentsRequireConfirmation`,

    /**
     *
     * @api {get} /api/eo/custom-payment/:custom_payment_id/confirm Event Custom Payment Requires Confirmation
     * @apiDescription Returns Custom Payment requires confirmation
     * @apiGroup Event Custom Payment
     *
     */
    'GET /api/eo/custom-payment/:custom_payment_id/confirm': `${Controller}getPaymentRequiresConfirmation`,

    /**
     *
     * @api {post} /api/eo/custom-payment/:custom_payment_id/confirm Event Custom Payment Confirmation
     * @apiDescription Confirm Custom Payment
     * @apiGroup Event Custom Payment
     *
     */
    'POST /api/eo/custom-payment/:custom_payment_id/confirm': `${Controller}confirmPayment`,

    /**
     *
     * @api {get} /api/event/:event/custom-payment/:payment_for/list Event Custom Payments List (Payment For)
     * @apiDescription Returns custom payments list filtered by "Payment For" value
     * @apiGroup Event Custom Payment
     *
     */
    'GET /api/event/:event/custom-payment/:payment_for/list': `${Controller}getPaymentsByPaymentForList`,

    /**
     *
     * @api {delete} /api/eo/custom-payment/:custom_payment_id Event Custom Payment Remove (Pending)
     * @apiDescription Removes pending custom payment
     * @apiGroup Event Custom Payment
     *
     */
    'DELETE /api/eo/custom-payment/:custom_payment_id': `${Controller}removePendingPayment`,
};
