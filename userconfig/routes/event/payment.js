

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/payments Payments List
     * @apiDescription Returns payments list for teams
     * @apiGroup Event Team Payments
     *
     */
    'get /api/event/:event/payments': 'Event/PaymentController.index',

    /**
     *
     * @api {get} /api/event/:event/payment/:purchase/teams Payment Teams List
     * @apiDescription Returns teams list paid by specific payment
     * @apiGroup Event Team Payments
     *
     */
    'get /api/event/:event/payment/:purchase/teams': 'Event/PaymentController.teams',

    /**
     *
     * @api {put} /api/event/:event/payment/:purchase/receive Receive Check
     * @apiDescription Set check received
     * @apiGroup Event Team Payments
     *
     */
    'put /api/event/:event/payment/:purchase/receive': 'Event/PaymentController.receive',

    /**
     *
     * @api {post} /api/event/:event/payment/:purchase/receive Save Note
     * @apiDescription Creates payment note
     * @apiGroup Event Team Payments
     *
     */
    'post /api/event/:event/payment/:purchase/notes': 'Event/PaymentController.save_note',

    /**
     *
     * @api {get} /api/event/:event/payment/:purchase/refund Make Refund
     * @apiDescription Makes a payment refund
     * @apiGroup Event Team Payments
     *
     */
    'get /api/event/:event/payment/:purchase/refund': 'Event/PaymentController.refund',

    /**
     *
     * @api {post} /api/event/:event/payment/:purchase/refund/partial Make Partial Refund
     * @apiDescription Makes a payment partial refund
     * @apiGroup Event Team Payments
     *
     */
    'post /api/event/:event/payment/:purchase/refund/partial': 'Event/PaymentController.partial_refund',

    /**
     *
     * @api {get} /api/event/:event/payments/fingerprint/:fingerprint Fingerprint Charges
     * @apiDescription Returns charges for a specific fingerprint
     * @apiGroup Event Team Payments
     *
     */
    'get /api/event/:event/payments/fingerprint/:fingerprint': 'Event/PaymentController.fingerprintCharges',

    /**
     *
     * @api {put} /api/event/:event/payment/:purchase/item/:purchase_team/replace-team Replace Team In Payment
     * @apiDescription Change one team in payment to another
     * @apiGroup Event Team Payments
     *
     */
    'put /api/event/:event/payment/:purchase/item/:purchase_team/replace-team': 'Event/PaymentController.replaceTeam',

    /**
     *
     * @api {get} /api/event/:event/payment/:purchase/state Payment Data
     * @apiDescription Returns teams payment data
     * @apiGroup Event Team Payments
     *
     */
    'get /api/event/:event/payment/:purchase/state': 'Event/PaymentController.getPaymentState',

    /**
     *
     * @api {post} /api/event/:event/purchase Create Payment
     * @apiDescription Creates team payment
     * @apiGroup Event Team Payments
     *
     */
    'post /api/event/:event/purchase': 'Event/PaymentController.save',

    /**
     *
     * @api {post} /api/event/:event/purchase/:purchase/cancel Cancel Payment
     * @apiDescription Cancels team payment
     * @apiGroup Event Team Payments
     *
     */
    'post /api/event/:event/purchase/:purchase/cancel': 'Event/PaymentController.cancel',
}
