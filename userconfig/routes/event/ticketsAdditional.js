'use strict';

const TICKETS_ADDITIONAL_CTRL = 'Event/TicketsAdditionalController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/tickets/reg/additional Additional Fields List
     * @apiDescription Returns tickets additional fields list
     * @apiGroup Event Ticket Additional Fields
     *
     */
    'get /api/event/:event/tickets/reg/additional': `${TICKETS_ADDITIONAL_CTRL}getAdditionalRegFields`,

    /**
     *
     * @api {post} /api/event/:event/tickets/reg/additional Create Additional Field
     * @apiDescription Creates tickets additional field
     * @apiGroup Event Ticket Additional Fields
     *
     */
    'post /api/event/:event/tickets/reg/additional': `${TICKETS_ADDITIONAL_CTRL}saveAdditionalRegField`,

    /**
     *
     * @api {put} /api/event/:event/tickets/reg/additional Update Additional Field
     * @apiDescription Updates tickets additional field
     * @apiGroup Event Ticket Additional Fields
     *
     */
    'put /api/event/:event/tickets/reg/additional': `${TICKETS_ADDITIONAL_CTRL}updateAdditionalRegFields`,

    /**
     *
     * @api {post} /api/event/:event/tickets/reg/additional_default Creat Default Additional Fields
     * @apiDescription Creates default additional fields list (for camps / showcase events)
     * @apiGroup Event Ticket Additional Fields
     *
     */
    'post /api/event/:event/tickets/reg/additional_default':`${TICKETS_ADDITIONAL_CTRL}setAdditionalDefault`
};
