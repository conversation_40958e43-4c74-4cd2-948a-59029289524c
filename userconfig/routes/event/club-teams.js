
const CLUB_CTRL = 'Event/RosterClubController.';

module.exports = {
    /**
     *
     * @api {get} /api/v2/event/:event/club/:club/teams/staff Event Club Team Staffers List
     * @apiDescription Returns
     * @apiGroup Event Club Team
     *
     */
    'get /api/v2/event/:event/club/:club/teams/staff': `${CLUB_CTRL}teams_members`,

    /**
     *
     * @api {get} /api/v2/event/:event/club/teams/available Event Club Teams List Available to Add to Event
     * @apiDescription Returns team list available to add to the event
     * @apiGroup Event Club Team
     *
     */
    'get /api/v2/event/:event/club/teams/available': `${CLUB_CTRL}availableTeams`,

    /**
     *
     * @api {post} /api/v2/event/:event/club/teams/assign Event Club Team Assign
     * @apiDescription Assigns team on event
     * @apiGroup Event Club Team
     *
     */
    'post /api/v2/event/:event/club/teams/assign' : `${CLUB_CTRL}assignTeam`,

    /**
     *
     * @api {post} /api/v2/event/:event/club/set-local Event Club Set Local
     * @apiDescription Sets club as local
     * @apiGroup Event Club Team
     *
     */
    'post /api/v2/event/:event/club/set-local': `${CLUB_CTRL}setLocal`,
};
