'use strict';

const CTRL = 'Event/TransferController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/transfer/accounts Stripe Account Transfer Info
     * @apiDescription Returns Stripe account transfers list and balance
     * @apiGroup Event Transfer
     *
     */
	'GET /api/event/:event/transfer/accounts': `${CTRL}getAccountsData`,

    /**
     *
     * @api {post} /api/event/:event/transfer/create Create Stripe Transfer
     * @apiDescription Creates Stripe manual payout
     * @apiGroup Event Transfer
     *
     */
	'POST /api/event/:event/transfer/create': `${CTRL}createTransfer`,

    /**
     *
     * @api {get} /api/event/:event/transfer/:type/list Stripe Transfers List
     * @apiDescription Returns Stripe transfers list for a specific payment for type (tickets / booths / teams)
     * @apiGroup Event Transfer
     *
     */
	'GET /api/event/:event/transfer/:type/list': `${CTRL}getTransfersList`,

    /**
     *
     * @api {get} /api/event/:event/transfer/:type/stats Event Statistic
     * @apiDescription Returns Event statistics for a specific payment for type (tickets / booths / teams)
     * @apiGroup Event Transfer
     *
     */
	'GET /api/event/:event/transfer/:type/stats': `${CTRL}getStats`,

    /**
     *
     * @api {get} /api/event/:event/transfer/account/:for Stripe Account Data
     * @apiDescription Returns Stripe account data for a specific payment for (tickets / booths / teams)
     * @apiGroup Event Transfer
     *
     */
    'GET /api/event/:event/transfer/account/:for': `${CTRL}getMerchantAccountData`
}
