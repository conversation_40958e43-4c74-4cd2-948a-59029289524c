'use strict';

const 
    PAYMENTS_CTRL       = 'Event/Tickets/PaymentOperationsController.',
    PARTICIPATION_CTRL  = 'Event/Tickets/ParticipationController.',
    SETTINGS_CTRL       = 'Event/Tickets/SettingsController.',
    TICKET_OPERATIONS_CTRL = 'Event/Tickets/TicketOperationsController.';

module.exports = {
    /**
     *
     * @api {get} api/pass/:event/:purchase Apple Wallet Ticket
     * @apiDescription Returns Apple wallet passcode
     * @apiGroup Event Tickets
     *
     */
    'get /api/pass/:event/:purchase': __getPath('getPass'),

    /**
     *
     * @api {get} /api/event/:event/tickets Ticket Types List
     * @apiDescription Returns event tickets types list
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets': __getPath('all'),

    /**
     *
     * @api {put} /api/event/:event/tickets/update Ticket Type Update
     * @apiDescription Updates event ticket type
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/update': __getPath('updateTicket'),

    /**
     *
     * @api {post} /api/event/:event/tickets/save Ticket Type Creation
     * @apiDescription Creates ticket type
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/save': __getPath('saveTicket'),

    /**
     *
     * @api {post} /api/event/:event/tickets/change/create Create Price Change
     * @apiDescription Creates a ticket type price change
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/change/create': __getPath('savePriceChange'),

    /**
     *
     * @api {put} /api/event/:event/tickets/change/update Update Price Change
     * @apiDescription Updates a ticket type price change
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/change/update': __getPath('updatePriceChange'),

    /**
     *
     * @api {post} /api/event/:event/tickets/change/remove Remove Price Change
     * @apiDescription Removes a ticket type price change
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/change/remove': __getPath('removePriceChange'),

    /**
     *
     * @api {get} /api/event/:event/reports/tickets Ticket Statistic Data
     * @apiDescription Returns tickets statistic data
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/reports/tickets': __getPath('statistics'),

    /**
     *
     * @api {get} /api/event/:event/reports/tickets/export Ticket Statistic Data Export
     * @apiDescription Returns tickets statistic data in file
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/reports/tickets/export': __getPath('statisticsExport'),

    /**
     *
     * @api {get} /api/v2/event/:event/reports/unique-scans/export Ticket Unique Scans Statistic Data Export
     * @apiDescription Returns tickets unique scans statistic data in file
     * @apiGroup Event Tickets
     *
     */
    'get /api/v2/event/:event/reports/unique-scans/export':{
        action: 'v2/event/reports/ticket-scans',
    },
 
    /**
     *
     * @api {get} /api/event/:event/tickets/payments Ticket Payments List
     * @apiDescription Returns ticket payments list
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/payments': __getPath('payments'),

    /**
     *
     * @api {get} /api/event/:event/tickets/payments/map Ticket Payments List On Map
     * @apiDescription Returns ticket payments list for Google Maps
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/payments/map': __getPath('paymentsOnMap'),

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/resend Resend Ticket Receipt
     * @apiDescription Resends ticket receipt
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/resend': __getPath('resendTicketReceipt'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/scan Update Payment Scan
     * @apiDescription Updates payment scan data
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/scan': __getPath('updatePaymentScanner'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/refund Ticket Full Refund
     * @apiDescription Refunds ticket payment fully
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/refund': `${PAYMENTS_CTRL}refund`,

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/history Ticket History
     * @apiDescription Returns ticket's history
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/history': __getPath('getTicketHistory'),

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/charge Charge Info
     * @apiDescription Returns ticket's charge data
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/charge': __getPath('retreiveChargeInfo'),

    /**
     *
     * @api {get} /api/event/:event/tickets/payments/export Payments Export
     * @apiDescription Exports ticket payments
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/payments/export': __getPath('paymentsExport'),

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/payment/info Payment Info
     * @apiDescription Returns ticket payment information
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/payment/info': __getPath('paymentInfo'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/payment/update Payment Info Update
     * @apiDescription Updates ticket payment information
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/payment/update': __getPath('updatePaymentInfo'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/refund/partial Ticket Partial Refund
     * @apiDescription Refunds ticket payment partially
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/refund/partial': `${PAYMENTS_CTRL}partialRefund`,

    /**
     *
     * @api {get} /api/event/:event/tickets/email/text Ticket Settings Additional Data
     * @apiDescription Returns Event Disclaimer / Event Ticket Description / Kiosk Description / Ticket Locations
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/email/text': __getPath('getEmailText'),

    /**
     *
     * @api {post} /api/event/:event/tickets/update/text Update Ticket Settings Additional Data
     * @apiDescription Updates Event Disclaimer / Event Ticket Description / Kiosk Description / Ticket Locations
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/update/text': __getPath('saveTicketsDescriptionFields'),

    /**
     *
     * @api {post} /api/event/:event/tickets/passcodes Update Guru Passcodes
     * @apiDescription Updates ticket Guru App passcodes
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/passcodes': __getPath('updatePasscodes'),

    /**
     *
     * @api {post} /api/event/:event/tickets/passcodes Update Guru Passcodes
     * @apiDescription Updates ticket Guru App passcodes
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/sales-dates': __getPath('changePurchaseDates'),

    /**
     *
     * @api {post} /api/event/:event/tickets/:ticket/discounts/upload Import Discounts List
     * @apiDescription Imports ticket discounts list
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/:ticket/discounts/upload': __getPath('importDiscountsList'),

    /**
     *
     * @api {get} /api/event/:event/tickets/short Ticket Types List (short info)
     * @apiDescription Returns ticket types list with short info data
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/short': __getPath('getEventTicketsShort'),

    /**
     *
     * @api {get} /api/event/:event/tickets/discounts Ticket Discounts List
     * @apiDescription Returns ticket discounts list
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/discounts': __getPath('getDiscountsList'),

    /**
     *
     * @api {get} /api/event/:event/tickets/discounts/:discount/info Ticket Discount Info
     * @apiDescription Returns ticket discount info
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/discounts/:discount/info': __getPath('getDiscountInfo'),

    /**
     *
     * @api {put} /api/event/:event/tickets/discounts/:discount/update Ticket Discount Update
     * @apiDescription Updates ticket discount info
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/discounts/:discount/update': __getPath('updateDiscountInfo'),

    /**
     *
     * @api {get} /api/event/:event/tickets/discounts/templates Ticket Discount Email Templates
     * @apiDescription Returns ticket discount email templates
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/discounts/templates': __getPath('getDiscountsEmailTmpls'),

    /**
     *
     * @api {post} /api/event/:event/tickets/discounts/:discount/remove Ticket Discount Remove
     * @apiDescription Removes ticket discount
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/discounts/:discount/remove': __getPath('removeDiscount'),

    /**
     *
     * @api {get} /api/event/:event/tickets/camps-list Camps List
     * @apiDescription Returns event camps list
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/camps-list': __getPath('campsList'),

    /**
     *
     * @api {post} /api/event/:event/tickets/receive-check/:type Ticket Check Receive
     * @apiDescription Sets ticket check received with payment update
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/receive-check/:type': __getPath('receiveCheck'),

    /**
     *
     * @api {get} /api/event/:event/tickets/:code/history Ticket History
     * @apiDescription Returns a specific ticket history
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/:code/history': __getPath('paymentHistory'),

    /**
     *
     * @api {post} /api/event/:event/tickets/:code/add-note Add Ticket History Note
     * @apiDescription Creates a history note for a ticket
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/:code/add-note': __getPath('addHistoryNote'),

    /**
     *
     * @api {post} /api/:event/tickets/:code/change-type/:type Camp Type Change
     * @apiDescription Changes type for camp
     * @apiGroup Event Tickets
     *
     */
    'post /api/:event/tickets/:code/change-type/:type': __getPath('changeTicketType'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/redeem Ticket Redeem
     * @apiDescription Redeem tickets
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/redeem': __getPath('redeemTickets'),

    /**
     *
     * @api {get} /api/event/:event/reports/camps Camps Statistic
     * @apiDescription Returns camps statistic data
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/reports/camps': __getPath('campsStatistics'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/refund-debt Refund Ticket Payment Debt
     * @apiDescription Refunds debt for a ticket
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/refund-debt': __getPath('refundDebt'),

    /**
     *
     * @api {post} /api/event/:event/tickets/stripe-statement Stripe Statement Update
     * @apiDescription Updates stripe statement in tickets settings
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/stripe-statement': __getPath('saveStripeStatement'),

    /**
     *
     * @api {post} /api/event/:event/tickets/tilled-statement Tilled Statement Update
     * @apiDescription Updates tilled statement in tickets settings
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/tilled-statement': {
        action: 'v2/event/tickets/update-tilled-statement',
    },

    /**
     *
     * @api {get} /api/event/:event/tickets/settings-validate Ticket Settings Validation
     * @apiDescription Validates current event tickets settings
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/settings-validate': __getPath('validateSettings'),

    /**
     *
     * @api {post} /api/event/:event/tickets/publish Publish Ticket Sales
     * @apiDescription Publish ticket sales
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/publish': __getPath('setTicketsPublished'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/payment/void Void Ticket Payment
     * @apiDescription Void ticket purchase
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/payment/void': __getPath('voidPurchase'),

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/waitlist/:status Updates Ticket Waitlist Status
     * @apiDescription Changes ticket waitlist status to selected
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/waitlist/:status': __getPath('changeWaitlistStatus'),

    /**
     *
     * @api {post} /api/event/:event/tickets/discounts/create Create Ticket Discount
     * @apiDescription Creates a discount for a ticket
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/tickets/discounts/create': __getPath('createDiscount'),

    /**
     *
     * @api {put} /api/event/:event/tickets/stripe-key Save Stripe Key
     * @apiDescription Saves stripe key for tickets sales
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/stripe-key': __getPath('saveStripeTicketsKey'),

    /**
     *
     * @api {put} /api/event/:event/tickets/tilled-account Save Tilled Account
     * @apiDescription Saves tilled account for tickets sales
     * @apiGroup Event Tickets
     *
     */
    'PUT /api/event/:event/tickets/tilled-account': {
        action: 'v2/event/tickets/update-tilled-account'
    },

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/send-email Send Email To Customer initiated Dispute
     * @apiDescription Sends email to the customer who initiated dispute for a payment
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/send-email': __getPath('sendMailToCustomer'),

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/emails Ticket Payment sent Emails
     * @apiDescription Returns emails list sent to the specific ticket customer
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/emails': __getPath('getPurchaseSendedEmails'),

    /**
     *
     * @api {put} /api/event/:event/tickets/checkin Update Camp's Checkin
     * @apiDescription Updates camps checkin status
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/checkin': __getPath('checkin'),

    /**
     *
     * @api {get} /api/event/:event/tickets/checkininfo Camp Checkin Info
     * @apiDescription Returns camp's checkin info data
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/checkininfo': __getPath('getTicketsCheckinInfo'),

    /**
     *
     * @api {put} /api/event/:event/ticket/:code/covid-test Update COVID Test Validation
     * @apiDescription Toggle COVID test requirement
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/ticket/:code/covid-test': __getPath('updateCovidTestValidation'),

    /**
     *
     * @api {delete} /api/event/:event/camp/:code/participation Cancel Customer Camp Participation
     * @apiDescription Cancels camp registration for a customer without refund
     * @apiGroup Event Tickets
     *
     */
    'delete /api/event/:event/camp/:code/participation': `${PARTICIPATION_CTRL}cancelParticipation`,

    /**
     *
     * @api {get} /api/event/:event/tickets/pins Return Ticket Settings Pin's
     * @apiDescription Returns ticket settings pin codes (Ticket Guru Lite PIN / Ticket Guru Buy PIN / Ticket Guru Admin PIN)
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/tickets/pins': `${SETTINGS_CTRL}getPins`,

    /**
     *
     * @api {put} /api/event/:event/tickets/pins Update Ticket Settings Pin's
     * @apiDescription Updates ticket settings pin codes (Ticket Guru Lite PIN / Ticket Guru Buy PIN / Ticket Guru Admin PIN)
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/pins': `${SETTINGS_CTRL}updatePins`,

    /**
     *
     * @api {put} /api/event/:event/tickets/tickets-settings Update Ticket Settings
     * @apiDescription Updates ticket settings
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/tickets/tickets-settings': `${SETTINGS_CTRL}updateTicketsSettings`,

    /**
     *
     * @api {post} /api/event/:event/ticket/:code/action/:action Deactivate Ticket Barcode
     * @apiDescription Deactivates ticket barcode
     * @apiGroup Event Tickets
     *
     */
    'post /api/event/:event/ticket/:code/action/:action': `${TICKET_OPERATIONS_CTRL}deactivateTicketBarcode`,

    /**
     *
     * @api {get} /api/event/:event/ticket/:code/event-tickets Get Event Tickets List
     * @apiDescription Returns event tickets list allowed to change for a specific barcode
     * @apiGroup Event Tickets
     *
     */
    'get /api/event/:event/ticket/:code/event-tickets': `${TICKET_OPERATIONS_CTRL}eventTicketTypes`,

    /**
     *
     * @api {put} /api/event/:event/ticket/:code/type Change Ticket's Event Ticket Type
     * @apiDescription Changes event ticket type for a specific ticket
     * @apiGroup Event Tickets
     *
     */
    'put /api/event/:event/ticket/:code/type': `${TICKET_OPERATIONS_CTRL}changeEventTicket`,
};

function __getPath(action) {
    return ('Event/TicketsController.' + action);
}
