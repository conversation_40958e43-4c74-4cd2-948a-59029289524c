const MEMBERS_CTRL = 'Event/ClubMembersController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/team/:team/members Event Club Team Members List
     * @apiDescription Returns members list of the specific club team
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/members': `${MEMBERS_CTRL}getTeamMembers`,

    /**
     *
     * @api {get} /api/event/:event/club/:club/members Event Club Members List
     * @apiDescription Returns members list of the specific club
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/club/:club/members': `${MEMBERS_CTRL}getClubMembers`,

    /**
     *
     * @api {post} /api/event/:event/team/:team/add/member Addition Member To Team
     * @apiDescription Adds member to specific team
     * @apiGroup Event Club Team Members
     *
     */
    'post /api/event/:event/team/:team/add/member': `${MEMBERS_CTRL}addMemberToRoster`,

    /**
     *
     * @api {delete} /api/event/:event/team/:team/member/:member/:type Removing Member From Team
     * @apiDescription Removes member from specific team
     * @apiGroup Event Club Team Members
     *
     */
    'delete /api/event/:event/team/:team/member/:member/:type': `${MEMBERS_CTRL}removeMemberFromRoster`,

    /**
     *
     * @api {put} /api/event/:event/team/:team/staffer/:staffer/update Event Club Team Member Staff Update
     * @apiDescription Updates event club team staff info
     * @apiGroup Event Club Team Members
     *
     */
    'put /api/event/:event/team/:team/staffer/:staffer/update': `${MEMBERS_CTRL}updateStaffer`,

    /**
     *
     * @api {put} /api/event/:event/team/:team/athlete/:athlete/update Event Club Team Member Athlete Update
     * @apiDescription Updates event club team athlete info
     * @apiGroup Event Club Team Members
     *
     */
    'put /api/event/:event/team/:team/athlete/:athlete/update': `${MEMBERS_CTRL}updateAthlete`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/validate-roster Event Club Team Roster Validation
     * @apiDescription Validates team's roster
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/validate-roster': `${MEMBERS_CTRL}validateTeamRoster`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/unlock Event Club Team Roster Unlock
     * @apiDescription Unlocks team's roster
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/unlock': `${MEMBERS_CTRL}unlockTeamRoster`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/lock Event Club Team Roster Lock
     * @apiDescription Locks team's roster
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/lock': `${MEMBERS_CTRL}lockTeamRoster`,

    /**
     *
     * @api {get} /api/event/:event/teams/unlock Event Club Team Roster Unlock (All Teams)
     * @apiDescription Unlocks team's roster for all teams on the event
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/teams/unlock': `${MEMBERS_CTRL}unlockAllTeamsRosterOnEvent`,

    /**
     *
     * @api {get} /api/event/:event/teams/lock Event Club Team Roster Lock (All Teams)
     * @apiDescription Locks team's roster for all teams on the event
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/teams/lock': `${MEMBERS_CTRL}lockAllTeamsRosterOnEvent`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/set-valid Event Club Team Roster Mark Valid
     * @apiDescription Marks team roster as valid
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/set-valid': `${MEMBERS_CTRL}markRosterValid`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/remove-validation Event Club Team Roster Remove Valid Mark
     * @apiDescription Removes team roster valid mark
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/remove-validation': `${MEMBERS_CTRL}removeRosterValidMark`,

    /**
     *
     * @api {get} /api/event/:event/club/:club/:memberType(athlete|staff)/:member/sync Event Club Team Webpoint Sync (Not Used!)
     * @apiDescription Loads staffer or athlete data from Webpoint
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/club/:club/:memberType(athlete|staff)/:member/sync': `${MEMBERS_CTRL}syncWebpointData`,

    /**
     *
     * @api {post} /api/event/:event/team/:team/online-checkin Event Club Team Online Checkin
     * @apiDescription Do online checkin for specific team
     * @apiGroup Event Club Team Members
     *
     */
    'post /api/event/:event/team/:team/online-checkin': `${MEMBERS_CTRL}onlineTeamCheckin`,

    /**
     *
     * @api {get} /api/event/:event/team/:team/online-checkin/staffers Event Club Team Online Checkin Staffers List
     * @apiDescription Returns online checkin staffers list
     * @apiGroup Event Club Team Members
     *
     */
    'get /api/event/:event/team/:team/online-checkin/staffers': `${MEMBERS_CTRL}getStaffers`,

    /**
     *
     * @api {put} /api/event/:event/staffer/:staffer/checkin/:action(activate|deactivate) Event Club Team Staff Checkin Toggle
     * @apiDescription Toggles staff checkin
     * @apiGroup Event Club Team Members
     *
     */
    'put /api/event/:event/staffer/:staffer/checkin/:action(activate|deactivate)':
        `${MEMBERS_CTRL}toggleStaffCheckinActivation`,
}
