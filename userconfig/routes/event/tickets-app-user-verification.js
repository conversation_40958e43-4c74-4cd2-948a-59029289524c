
module.exports = {
    /**
     *
     * @api {get} /api/tickets-app/event/:event/verification/users Users List waiting Verification Approve
     * @apiDescription Returns list of users who are waiting for verification approve by EO.
     * List returns users bought tickets on a specific event and asked for a approve.
     * @apiGroup Tickets App User Verification
     *
     */
    'GET /api/tickets-app/event/:event/verification/users': {
        action: 'v2/event/tickets-app-user-verification/users-list'
    },

    /**
     *
     * @api {put} /api/tickets-app/event/:event/verification/users/:user_id/:decision User Verification Status Change
     * @apiDescription Verify or decline user's Verification Ask
     * @apiGroup Tickets App User Verification
     *
     */
    'PUT /api/tickets-app/event/:event/verification/users/:user_id/:decision': {
        action: 'v2/event/tickets-app-user-verification/update-verification'
    }
};
