const CTRL = 'EventExhibitorInvoiceController';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/exhibitors/invoices Event Exhibitor Invoices List
     * @apiDescription Returns event exhibitor invoices list
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'GET /api/event/:event/exhibitors/invoices': `${CTRL}.getExhibitorInvoices`,

    /**
     *
     * @api {get} /api/event/:event/exhibitors/invoices/init-data Event Exhibitor Invoice Init Data
     * @apiDescription Returns events data for create new event exhibitor invoice
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'GET /api/event/:event/exhibitors/invoices/init-data': `${CTRL}.getExhibitorInvoiceInitData`,

    /**
     *
     * @api {get} /api/event/:event/exhibitor/:exhibitor/invoice/:invoice Event Exhibitor Invoice Info
     * @apiDescription Returns event exhibitor invoice info
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'GET /api/event/:event/exhibitor/:exhibitor/invoice/:invoice': `${CTRL}.getExhibitorInvoiceInfo`,

    /**
     *
     * @api {post} /api/event/:event/exhibitor/:exhibitor/invoice Event Exhibitor Invoice Create
     * @apiDescription Creates event exhibitor invoice
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'POST /api/event/:event/exhibitor/:exhibitor/invoice': `${CTRL}.createExhibitorInvoice`,

    /**
     *
     * @api {put} /api/event/:event/exhibitor/:exhibitor/invoice/:invoice Event Exhibitor Invoice Update
     * @apiDescription Updates event exhibitor invoice
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'PUT /api/event/:event/exhibitor/:exhibitor/invoice/:invoice': `${CTRL}.updateExhibitorInvoice`,

    /**
     *
     * @api {delete} /api/event/:event/exhibitor/:exhibitor/invoice/:invoice Event Exhibitor Invoice Delete
     * @apiDescription Deletes event exhibitor invoice
     * @apiGroup Event Exhibitors Invoices
     *
     */
    'DELETE /api/event/:event/exhibitor/:exhibitor/invoice/:invoice': `${CTRL}.deleteExhibitorInvoice`,
};
