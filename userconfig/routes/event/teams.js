

module.exports = {
    /**
     *
     * @api {post} /api/v2/event/:event/teams/export Export Teams List
     * @apiDescription Exports teams list on specific event
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/teams/export': 'Event/RosterTeamController.xlsx_export',

    /**
     *
     * @api {post} /api/v2/event/:event/teams/entry/change Teams Entry Change
     * @apiDescription Changes teams entry status (one or multiple teams)
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/teams/entry/change': 'Event/RosterTeamController.change_entry',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/:team/info Team Info
     * @apiDescription Returns data for a specific team
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/:team/info': 'Event/RosterTeamController.find',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/srva-report SRVA Report
     * @apiDescription Generates SRVA report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/srva-report': 'Event/RosterTeamController.srvaReport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/geva-report GEVA Report
     * @apiDescription Generates GEVA report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/geva-report': 'Event/RosterTeamController.gevaExport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/usav-final-report USAV Final Report
     * @apiDescription Generates USAV Final report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/usav-final-report': 'Event/RosterTeamController.usavFinalReport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/finishes-report Finishes Report
     * @apiDescription Generates finishes report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/finishes-report': 'Event/RosterTeamController.finishersReport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/head-to-head-report Head to Head Report
     * @apiDescription Generates head to head report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/head-to-head-report': 'Event/RosterTeamController.headToHeadReport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/head-to-head-report USAV National Ranking System Export
     * @apiDescription Generates USAV National Ranking System Export
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/usav-national-report': 'Event/RosterTeamController.usavNationalReport',


    /**
     *
     * @api {get} /api/v2/event/:event/teams/region-finishes-report Region Finishes Report
     * @apiDescription Generates SRVA report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/region-finishes-report': 'Event/RosterTeamController.regionFinishesReport',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/head-to-head-report Export Members
     * @apiDescription Export team members
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/export/:member_type': 'Event/RosterTeamController.exportMembers',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/download/:fname Download Export File
     * @apiDescription Downloads previously generated report
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/download/:fname': 'Event/RosterTeamController.exportFileDownload',

    /**
     *
     * @api {post} /api/v2/event/:event/teams/change-division Change Division
     * @apiDescription Changes team division
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/teams/change-division': 'Event/RosterTeamController.changeDivision',

    /**
     *
     * @api {put} /api/v2/event/:event/teams/payment-status Update Team Payment Status
     * @apiDescription Updates team payment status
     * @apiGroup Event Teams
     *
     */
    'put /api/v2/event/:event/teams/payment-status': 'Event/RosterTeamController.updatePaymentStatus',

    /**
     *
     * @api {get} /api/v2/event/:event/teams/payment-status Teams Data for Checkin
     * @apiDescription Returns latest teams data for checkin
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams/current-teams': 'Roster_teamController.getLatestTeamsData',

    /**
     *
     * @api {get} /api/v2/event/:event/teams Teams List
     * @apiDescription Returns event teams list
     * @apiGroup Event Teams
     *
     */
    'get /api/v2/event/:event/teams': 'Event/RosterTeamController.all',

    /**
     *
     * @api {post} /api/v2/event/:event/add-teams-manually Create Teams Manually
     * @apiDescription Creates teams manually
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/add-teams-manually': 'Event/RosterTeamController.addTeamsManually',

    /**
     *
     * @api {post} /api/v2/event/:event/add-roster-manually Create Teams Roster Manually
     * @apiDescription Creates teams roster manually
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/add-roster-manually': 'Event/RosterTeamController.addRosterManually',

    /**
     *
     * @api {post} /api/v2/event/:event/import-teams-manually Create Teams Manually (Import)
     * @apiDescription Creates teams manually from imported file
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/import-teams-manually': 'Event/RosterTeamController.importTeamsManually',

    /**
     *
     * @api {post} /api/v2/event/:event/import-members-manually Create Team Roster Manually (Import)
     * @apiDescription Creates teams roster manually from imported file
     * @apiGroup Event Teams
     *
     */
    'post /api/v2/event/:event/import-members-manually': 'Event/RosterTeamController.importMembersManually',

    /**
     *
     * @api {put} /api/event/:event/roster_team/:id Update Team
     * @apiDescription Updates specific team
     * @apiGroup Event Teams
     *
     */
    'put /api/event/:event/roster_team/:id': 'Roster_teamController.update',

    /**
     *
     * @api {put} /api/event/:event/roster_team/:id/update/qualification Update Team Qualification
     * @apiDescription Updates specific team qualification data
     * @apiGroup Event Teams
     *
     */
    'put /api/event/:event/roster_team/:id/update/qualification': 'Roster_teamController.updateQualification',

    /**
     *
     * @api {get} /api/event/:event/teams/checkin Check-In All Teams
     * @apiDescription Check-Ins all accepted teams on event
     * @apiGroup Event Teams
     *
     */
    'get /api/event/:event/teams/checkin': 'Roster_teamController.checkInAll',

    /**
     *
     * @api {post} /api/event/:event/teams/setcheckstatus Update Check-In Team Status
     * @apiDescription Changes event team check-in status
     * @apiGroup Event Teams
     *
     */
    'post /api/event/:event/teams/setcheckstatus': 'Roster_teamController.saveCheckInStatus',

    /**
     *
     * @api {get} /api/event/:event/roster/checkin Print Teams Roster
     * @apiDescription Returns PDF with roster data for all event teams
     * @apiGroup Event Teams
     *
     */
    'get /api/event/:event/roster/checkin': 'Event/RosterTeamController.printRosters',

    /**
     *
     * @api {post} /api/event/:event/roster_team/:id/housing Update Team Housing
     * @apiDescription Updates housing of a specific event team
     * @apiGroup Event Teams
     *
     */
    'post /api/event/:event/roster_team/:id/housing': 'Roster_teamController.update_housing',

    /**
     *
     * @api {get} /api/event/:event/roster_teams/payment Available For Payment Teams List
     * @apiDescription Returns teams list available for payment (not paid)
     * @apiGroup Event Teams
     *
     */
    'get /api/event/:event/roster_teams/payment': 'Roster_teamController.teams_to_pay',

    /**
     *
     * @api {get} /api/event/:event/roster_team/:id/history Team History
     * @apiDescription Returns history data for a specific team
     * @apiGroup Event Teams
     *
     */
    'get /api/event/:event/roster_team/:id/history': 'Roster_teamController.team_history',

    /**
     *
     * @api {get} /api/event/:event/roster_team/:id/booking Housing Booking Data
     * @apiDescription Returns housing booking data for a specific team
     * @apiGroup Event Teams
     *
     */
    'get /api/event/:event/roster_team/:id/booking': 'Roster_teamController.booking',

    /**
     *
     * @api {get} /api/roster_team/:id/history/:ths_id THS History Data
     * @apiDescription Returns THS history data for a specific team
     * @apiGroup Event Teams
     *
     */
    'get /api/roster_team/:id/history/:ths_id': 'Roster_teamController.history', // todo - change

    /**
     *
     * @api {put} /api/event/:event/roster_team/update/qualification Set Bid Accepted For Teams
     * @apiDescription Saves bid agreement data for specific teams
     * @apiGroup Event Teams
     *
     */
    'put /api/event/:event/roster_team/update/qualification': 'Roster_teamController.acceptBidForTeams',
}
