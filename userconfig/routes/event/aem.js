'use strict';

const CTRL = 'Event/AEMController.';

module.exports = {

    /**
     *
     * @api {get} /api/event/:event/aem/init Event AEM Initial Data
     * @apiDescription Returns event emails initial data (EO's templates list and BeeEditor's config)
     * @apiGroup Event AEM
     *
     */
	'GET /api/event/:event/aem/init': `${CTRL}getInitData`,

    /**
     *
     * @api {get} /api/event/:event/aem/tmpl/:tmpl Event AEM Template
     * @apiDescription Returns event email template
     * @apiGroup Event AEM
     *
     */
	'GET /api/event/:event/aem/tmpl/:tmpl': `${CTRL}getTmpl`,

    /**
     *
     * @api {put} /api/event/:event/aem/tmpl/:tmpl Event AEM Template update
     * @apiDescription Updates event email template
     * @apiGroup Event AEM
     *
     */
	'PUT /api/event/:event/aem/tmpl/:tmpl': `${CTRL}saveTmpl`,

    /**
     *
     * @api {post} /api/event/:event/aem/tmpl Event AEM Template creation
     * @apiDescription Creates event email template
     * @apiGroup Event AEM
     *
     */
	'POST /api/event/:event/aem/tmpl': `${CTRL}createTmpl`,

    /**
     *
     * @api {get} /api/event/:event/aem/preview/:tmpl Event AEM Template Preview
     * @apiDescription Returns event email template HTML (for preview)
     * @apiGroup Event AEM
     *
     */
	'GET /api/event/:event/aem/preview/:tmpl': `${CTRL}getTemplateHTML`,

    /**
     *
     * @api {post} /api/event/:event/aem/tmpl/:tmpl/duplicate Event AEM Template Duplicate
     * @apiDescription Duplicates event email template
     * @apiGroup Event AEM
     *
     */
	'POST /api/event/:event/aem/tmpl/:tmpl/duplicate': `${CTRL}duplicateTemplate`,

    /**
     *
     * @api {post} /api/event/:event/aem/tmpl/:tmpl/assign Event AEM Template Add To Trigger
     * @apiDescription Assigns event email template to the trigger action
     * @apiGroup Event AEM
     *
     */
	'POST /api/event/:event/aem/tmpl/:tmpl/assign': `${CTRL}assignToTrigger`,

    /**
     *
     * @api {get} /api/event/:event/aem/sending/:group/available-tmpls Event AEM Templates By Group
     * @apiDescription Returns event email templates associated with specific group
     * @apiGroup Event AEM
     *
     */
	'GET /api/event/:event/aem/sending/:group/available-tmpls': `${CTRL}teamsTemplates`,

    /**
     *
     * @api {delete} /api/event/:event/aem/sending/:group/available-tmpls Event AEM Template Delete
     * @apiDescription Deletes event email template
     * @apiGroup Event AEM
     *
     */
	'DELETE /api/event/:event/aem/tmpl/:tmpl/remove': `${CTRL}deleteTemplate`,

    /**
     *
     * @api {post} /api/event/:event/aem/tmpl/:tmpl/send Event AEM Template Test Sending
     * @apiDescription Sends test email using specific template
     * @apiGroup Event AEM
     *
     */
	'POST /api/event/:event/aem/tmpl/:tmpl/send': `${CTRL}testSend`,

    /**
     *
     * @api {post} /api/event/:event/aem/tmpl/:tmpl/send Event AEM Template Sending
     * @apiDescription Sends email template
     * @apiGroup Event AEM
     *
     */
	'POST /api/event/:event/aem/:group/send-email': `${CTRL}generalSend`,

    /**
     *
     * @api {get} /api/event/:event/aem/tmpl/:tmpl/send Event AEM Template Mass Sending
     * @apiDescription Sends multiple emails
     * @apiGroup Event AEM
     *
     */
    'GET /api/event/:event/aem/mass-send': `${CTRL}massSend`,

    /**
     *
     * @api {get} /api/event/:event/aem/retrieve-jobs/:template Event AEM Template Sending Jobs
     * @apiDescription Returns event email sending jobs list for specific emails
     * @apiGroup Event AEM
     *
     */
    'GET /api/event/:event/aem/retrieve-jobs/:template': `${CTRL}retrieveJobs`,
};
