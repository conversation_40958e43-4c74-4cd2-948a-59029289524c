module.exports = {
    /**
     *
     * @api {put} /api/housing/note/:note/update Update Team Note
     * @apiDescription Updates event team history note
     * @apiGroup Housing
     *
     */
    'put /api/housing/note/:note/update': 'Public/TeamsController.update_note',

    /**
     *
     * @api {post} /api/housing/events/:event/teams/:team/notes Create Team Note
     * @apiDescription Creates event team history note
     * @apiGroup Housing
     *
     */
    'post /api/housing/events/:event/teams/:team/notes': 'Public/TeamsController.add_note',

    /**
     *
     * @api {get} /api/housing/events/:event/teams/:team/notes Team Notes List
     * @apiDescription Returns event team history notes list
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/:event/teams/:team/notes': 'Public/TeamsController.get_notes',

    /**
     *
     * @api {get} /api/housing/events Events List
     * @apiDescription Returns events list
     * @apiGroup Housing
     *
     */
    'get /api/housing/events': 'Public/EventController.index',

    /**
     *
     * @api {get} /api/public/current-event Events List (Current)
     * @apiDescription Returns published events list
     * @apiGroup Housing
     *
     */
    'get /api/public/current-events': 'Public/EventController.currentEventsList',

    /**
     *
     * @api {get} /api/housing/events/:event/teams Event Teams List
     * @apiDescription Returns events teams list
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/:event/teams': 'Public/TeamsController.index',

    /**
     *
     * @api {get} /api/housing/events/:event/teams/export Event Teams Export
     * @apiDescription Exporting events teams
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/:event/teams/export': `Public/TeamsController.export_team_list`,

    /**
     *
     * @api {get} /api/housing/events/:event/teams/:team/contacts Club Contacts
     * @apiDescription Returns event club contacts
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/:event/teams/:team/contacts': 'Public/TeamsController.club_contacts',

    /**
     *
     * @api {get} /api/housing/events/:event/teams/:team/bookings Team's THS Bookings List
     * @apiDescription Returns THS bookings list for a specific team
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/:event/teams/:team/bookings': 'Public/TeamsController.ths_bookings',

    /**
     *
     * @api {get} /api/housing/events/teams/:team/history/:ths/ths Team's THS History
     * @apiDescription Returns THS history (updates) for a specific team
     * @apiGroup Housing
     *
     */
    'get /api/housing/events/teams/:team/history/:ths/ths': 'Public/TeamsController.ths_history',

    /**
     *
     * @api {put} /api/housing/events/:event/teams/:team/status Team Housing Status Update
     * @apiDescription Updates team housing status on event
     * @apiGroup Housing
     *
     */
    'put /api/housing/events/:event/teams/:team/status': 'Public/HousingStatusController.updateTeamStatus',

    /**
     *
     * @api {get} /api/housing/companies Housing Companies List
     * @apiDescription Returns housing companies list
     * @apiGroup Housing
     *
     */
    'get /api/housing/companies': 'Public/HousingCompanyController.getCompanies',
};
