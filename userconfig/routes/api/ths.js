

module.exports = {
    /**
     *
     * @api {get} /api/ths/v1/ping Ping
     * @apiDescription Used to test if API works and has active DB connection
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/ping': 'THS/V1Controller.ping',

    /**
     *
     * @api {get} /api/ths/v1/ping-node PingNode
     * @apiDescription Used to test if API node process works
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/ping-node': 'THS/V1Controller.pingNode',

    /**
     *
     * @api {get} /api/ths/v1/info Test Auth
     * @apiDescription Used to test authentication and to get consumerSecret hash
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/info': 'THS/V1Controller.info',

    /**
     *
     * @api {get} /api/ths/v1/eventlist Events List
     * @apiDescription Used to get events list
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/eventlist': 'THS/V1Controller.eventlist',

    /**
     *
     * @api {get} /api/ths/v1/clubs/:event/:timestamp Clubs List
     * @apiDescription Used to get clubs list
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/clubs/:event/:timestamp': 'THS/V1Controller.clubs',

    /**
     *
     * @api {get} /api/ths/v1/teams/:event/:timestamp/ Teams List
     * @apiDescription Used to get teams list
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/teams/:event/:timestamp': 'THS/V1Controller.teams',

    /**
     *
     * @api {get} /api/ths/v1/teams/:event/:timestamp/:team Team
     * @apiDescription Used to get specific team
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/teams/:event/:timestamp/:team': 'THS/V1Controller.teams',

    /**
     *
     * @api {get} /api/ths/v1/reservations/:event/:timestamp/:reservation Reservations
     * @apiDescription Used to get list of housing reservations
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/reservations/:event/:timestamp/:reservation': 'THS/V1Controller.reservations',

    /**
     *
     * @api {get} /api/ths/v1/reservations/:timestamp/:reservation Event Reservations
     * @apiDescription Used to get list of event housing reservations
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/reservations/:timestamp/:reservation': 'THS/V1Controller.reservations',

    /**
     *
     * @api {get} /api/ths/v1/changes/:event/:timestamp Changes List
     * @apiDescription Used to get last housing changes on event
     * @apiGroup THS
     *
     */
    'GET /api/ths/v1/changes/:event/:timestamp': 'THS/V1Controller.changes',

    /**
     *
     * @api {get} /api/ths/v1/bookings Bookings List
     * @apiDescription Used to get housing bookings list
     * @apiGroup THS
     *
     */
    'POST /api/ths/v1/bookings': {
        controller: 'THS/V1Controller',
        action: 'bookings',
        csrf: false
    },
}
