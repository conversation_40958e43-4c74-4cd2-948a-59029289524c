

module.exports = {
    /**
     *
     * @api {get} /api/ua/export/events Events
     * @apiDescription Export all events
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/events': 'API/UAExportController.events',

    /**
     *
     * @api {get} /api/ua/export/:event/:gender/clubs Event Clubs
     * @apiDescription Export all event clubs
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/:gender/clubs': 'API/UAExportController.clubs',

    /**
     *
     * @api {get} /api/ua/export/:event/:gender/teams Event Teams
     * @apiDescription Export all event teams
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/:gender/teams': 'API/UAExportController.teams',

    /**
     *
     * @api {get} /api/ua/export/:event/:gender/athletes Event Athletes
     * @apiDescription Export all event athletes
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/:gender/athletes': 'API/UAExportController.athletes',

    /**
     *
     * @api {get} /api/ua/export/:event/:gender/staff Event Staffers
     * @apiDescription Export all event staffers
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/:gender/staff': 'API/UAExportController.staff',

    /**
     *
     * @api {get} /api/ua/export/:event/:gender/schedule Event Schedule
     * @apiDescription Export event schedule
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/:gender/schedule': 'API/UAExportController.schedule',

    /**
     *
     * @api {get} /api/ua/export/:event/locations Event Locations
     * @apiDescription Export event schedule
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/:event/locations': 'API/UAExportController.findEventLocations',

    /**
     *
     * @api {get} /api/ua/export/member/:usav/find USAV Member (Deprecated)
     * @apiDescription Finds USAV Member
     * @apiGroup UA Export
     *
     */
    'GET /api/ua/export/member/:usav/find': 'API/UAExportController.findUSAVData',
}
