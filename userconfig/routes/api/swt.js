

module.exports = {
    'GET /api/swt/ping': 'API/Tickets/SWTApiController.ping',
    'POST /api/swt/lookup': {
        controller: 'API/Tickets/SWTApiController',
        action: 'lookup',
        csrf: false
    },
    'POST /api/swt/test': {
        controller: 'API/Tickets/SWTApiController',
        action: 'test',
        csrf: false
    },
    'POST /api/swt/recent': {
        controller: 'API/Tickets/SWTApiController',
        action: 'recent',
        csrf: false
    },
    'POST /api/swt/scan': {
        controller: 'API/Tickets/SWTApiController',
        action: 'scan',
        csrf: false
    },
    'POST /api/swt/redeem': {
        controller: 'API/Tickets/SWTApiController',
        action: 'redeem',
        csrf: false
    },
    'POST /api/swt/buy': {
        controller: 'API/Tickets/SWTApiController',
        action: 'buy',
        csrf: false
    },
    'GET /api/swt/events': 'API/Tickets/SWTApiController.events',
    'GET /api/swt/debug': 'API/Tickets/SWTApiController.debugQuery',
    'POST /api/swt/log_error': {
        controller: 'API/Tickets/SWTApiController',
        action: 'appErrorLogger',
        csrf: false
    },
    'POST /api/swt/wristband': {
        controller: 'API/Tickets/SWTApiController',
        action: 'assignWristbandSerial',
        csrf: false
    },
    'POST /api/swt/pending/recent': {
        controller: 'API/Tickets/SWTApiController',
        action: 'pendingPurchases',
        csrf: false
    },
    'POST /api/swt/pending/void': {
        controller: 'API/Tickets/SWTApiController',
        action: 'voidPending',
        csrf: false
    },
    'POST /api/swt/pending/pay' : {
        controller: 'API/Tickets/SWTApiController',
        action: 'payForPendingPayment',
        csrf: false
    },
    'GET /api/swt/event/:event/csv': 'API/Tickets/SWTApiController.getScanHistoryCsv',
}
