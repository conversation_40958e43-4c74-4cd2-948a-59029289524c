const CTRL = 'API/EventCheckin/EventCheckinOfficialController';

/**
 * @apiDefine ErrorResponses
 * @apiErrorExample {json} Validation Error Response:
 *     HTTP/1.1 400 Validation Error
 *     {
 *       "success": false,
 *       "error": "Checkin type not valid"
 *     }
 * @apiErrorExample {json} Internal Error Response:
 *     HTTP/1.1 500 Internal Error
 *     {
 *       "success": false,
 *       "error": "Internal Error"
 *     }
 */

module.exports = {
    /**
     *
     * @api {get} /api/event-checkin/type/:type/event/:event/scan/:barcode Scan Official QR code
     * @apiDescription Scans Officials QR code
     * @apiGroup Event Official Checkin
     *
     * @apiParam (url params) {String="official"} type Checkin type
     * @apiParam (url params) {Number} event Event Identifier
     * @apiParam (url params) {String} barcode Officials barcode string from QR code
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "official": {
     *          "name": "<PERSON>",
     *          "head_official": false
     *       }
     *     }
     *
     * @apiUse ErrorResponses
     */
    'get /api/event-checkin/type/:type/event/:event/scan/:barcode': `${CTRL}.scan`,

    /**
     *
     * @api {get} /api/event-checkin/type/:type/events Events with Officials
     * @apiDescription Events list with officials
     * @apiGroup Event Official Checkin
     *
     * @apiParam (url params) {String="official"} type Checkin type
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     [{
     *       "event_id": "123455",
     *       "name": "Event With Officials 1990",
     *       "short_name": "EWO 1990",
     *       "date_start": "Nov 07, 2021",
     *       "date_end": "Nov 10, 2021"
     *     },{
     *       "event_id": "22255",
     *       "name": "Event With Officials 2000",
     *       "short_name": "EWO 2000",
     *       "date_start": "Nov 17, 2021",
     *       "date_end": "Nov 22, 2021"
     *     }]
     *
     * @apiUse ErrorResponses
     */
    'get /api/event-checkin/type/:type/events': `${CTRL}.events`,
};
