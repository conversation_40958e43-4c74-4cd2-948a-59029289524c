

module.exports = {

    /**
     *
     * @api {get} /api/swr/check-pin/:event/:pin Check Pin
     * @apiDescription Checks official's pin
     * @apiGroup SWR - SW Results
     *
     */
    'GET /api/swr/check-pin/:event/:pin': 'API/SWResultsController.check_pin',

    /**
     *
     * @api {get} /api/swr/get-event-courts/:event/:day Courts
     * @apiDescription Returns courts for specific event day
     * @apiGroup SWR - SW Results
     *
     */
    'GET /api/swr/get-event-courts/:event/:day': 'API/SWResultsController.get_courts',

    /**
     *
     * @api {get} /api/swr/get-matches/:event/:day/:court Matches
     * @apiDescription Returns matches for specific event day court
     * @apiGroup SWR - SW Results
     *
     */
    'GET /api/swr/get-matches/:event/:day/:court': 'API/SWResultsController.get_matches',

    /**
     *
     * @api {get} /api/swr/check-match/:event/:barcode/:match Match
     * @apiDescription Returns match by ID or barcode
     * @apiGroup SWR - SW Results
     *
     */
    'GET /api/swr/check-match/:event/:barcode/:match': 'API/SWResultsController.check_match',

    /**
     *
     * @api {post} /api/swr/post-results Post Results
     * @apiDescription Save match results
     * @apiGroup SWR - SW Results
     *
     */
    'POST /api/swr/post-results': {
        controller: 'API/SWResultsController',
        action: 'post_results',
        csrf: false
    },
}
