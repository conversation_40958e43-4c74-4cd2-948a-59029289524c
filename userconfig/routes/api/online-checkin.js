let CTRL_PATH	= 'API/OnlineCheckin/CheckinScanController',
	URL_PREFIX 	= '/api/online-checkin/:event';

let routes = {};

/**
 *
 * @api {get} /api/online-checkin/:event Events List
 * @apiDescription Returns events list
 * @apiGroup Online Checkin
 *
 */
routes[`GET ${URL_PREFIX}`] = `${CTRL_PATH}.events`;

/**
 *
 * @api {get} /api/online-checkin/:event/scan/:barcode` Scan Barcode
 * @apiDescription Scans barcode
 * @apiGroup Online Checkin
 *
 */
routes[`GET ${URL_PREFIX}/scan/:barcode`] = `${CTRL_PATH}.scan`;

/**
 *
 * @api {post} /api/online-checkin/:event/checkin/:barcode Check-in
 * @apiDescription Check-in
 * @apiGroup Online Checkin
 *
 */
routes[`POST ${URL_PREFIX}/checkin/:barcode`] = {
    controller: CTRL_PATH,
    action: 'checkin',
    csrf: false
};

module.exports = routes;
