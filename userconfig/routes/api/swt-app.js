

module.exports = {
    /**
     *
     * @api {post} /api/swt-app/v1/wallet/ticket/link Create sharing link
     * @apiDescription Creates ticket sharing link
     * @apiGroup SW Ticket App
     *
     * @apiBody {Number} ticket_barcode - Ticket's barcode number (9 digits). Example: 123123123
     *
     * @apiSuccess (200) {String} link_content link content for ticket sharing
     * @apiError (400) {String} validation Validation error message text
     * @apiError (500) {String} message Server error message text
     *
     * @apiSuccessExample {json} Successful link content creation
     *  HTTP/1.1 200 OK
     *  {
     *      "link_content": "MS4wXzE2XzM3MDI4NTYwNF8xNjc5MDQ4NTc1NDA5"
     *  }
     *
     * @apiErrorExample {json} Ticket Barcode invalid type:
     *  HTTP/1.1 400 Bad Request
     *  {
     *     "validation": "Ticket barcode should be a number"
     *  }
     *
     * @apiErrorExample {json} Server Error:
     *  HTTP/1.1 500 Internal Server Error
     *  {
     *      "message": "Link not created"
     *  }
     *
     * @apiErrorExample {json} :
     *  HTTP/1.1 500 Internal Server Error (invalid body object)
     *  {
     *     "expose": true,
     *     "statusCode": 400,
     *     "status": 400,
     *     "body": "{\n    \"ticket_barcode\": ''\n}",
     *     "type": "entity.parse.failed"
     * }
     *
     */
    'POST /api/swt-app/v1/wallet/ticket/link': {
        action: 'v2/API/swt-app/wallet/ticket/create-link',
        csrf: false
    },


    /**
     *
     * @api {get} /tickets/share/:shareCode Renders ticket share page with fallback to ticket page url
     * @apiDescription Renders page that redirects user to app if it is installed, otherwise fallback to ticket page url
     * @apiGroup SW Ticket App
     *
     */
    'GET /tickets/share/:shareCode': {
        action: 'v2/API/swt-app/tickets/ticket-share-link',
        csrf: false
    },

    /**
     *
     * @api {post} /api/swt-app/v1/wallet/ticket/link Add ticket to wallet
     * @apiDescription Adds ticket to user's wallet
     * @apiGroup SW Ticket App
     *
     * @apiBody {Number} link_id - Shared link ID. Example: 1
     * @apiBody {Number} ticket_barcode - Ticket's barcode number (9 digits). Example: 123123123
     *
     * @apiSuccess (200) {Number} ticket_barcode Ticket barcode number
     * @apiSuccess (200) {Boolean} is_own Defines if ticket is own (purchased) ticket
     * @apiSuccess (200) {String} first Ticket Holder first name
     * @apiSuccess (200) {String} last Ticket Holder last name
     * @apiSuccess (200) {String} ticket_type Ticket Type - daily / weekend
     * @apiSuccess (200) {Boolean} scannable Defines if ticket is allowed to be scanned
     * @apiSuccess (200) {Boolean} is_scanned Defines if ticket was scanned
     * @apiSuccess (200) {Object} valid_dates Ticket valid dates
     * @apiSuccess (200) {Number} event_id Ticket's event ID
     *
     * @apiError (400) {String} validation Validation error message text
     * @apiError (500) {String} message Server error message text
     *
     * @apiSuccessExample {json} Successful ticket sharing (addition to user's wallet)
     *  HTTP/1.1 200 OK
     *  {
     *     "ticket_barcode": 370285604,
     *     "is_own": false,
     *     "first": "John",
     *     "last": "Stone",
     *     "ticket_type": "daily",
     *     "scannable": true,
     *     "is_scanned": false,
     *     "valid_dates": {},
     *     "event_id": 20221
     *  }
     *
     * @apiErrorExample {json} Ticket Not Found Response:
     *  HTTP/1.1 400 Bad Request
     *  {
     *      "validation": "Ticket not found"
     *  }
     *
     * @apiErrorExample {json} User trying to add own ticket to wallet:
     *  HTTP/1.1 400 Bad Request
     *  {
     *      "validation": "Own ticket can't be added to the wallet"
     *  }
     *
     * @apiErrorExample {json} Ticket not added error:
     *  HTTP/1.1 500 Internal Server Error
     *  {
     *      "message": "Ticket not added"
     *  }
     *
     * @apiErrorExample {json} :
     *  HTTP/1.1 500 Internal Server Error (invalid body object)
     *  {
     *     "expose": true,
     *     "statusCode": 400,
     *     "status": 400,
     *     "body": "{\n    \"ticket_barcode\": '',\n    \"link_id\": 3\n}",
     *     "type": "entity.parse.failed"
     * }
     *
     */
    'POST /api/swt-app/v1/wallet/ticket': {
        action: 'v2/API/swt-app/wallet/ticket/add',
        csrf: false
    },

    /**
     *
     * @api {post} /api/swt-app/v1/user/recognition/init Init verification session
     * @apiDescription Initialize verification session for rekognition service
     * @apiGroup SW Ticket App
     *
     */
    'POST /api/swt-app/v1/user/recognition/init': {
        action: 'v2/API/swt-app/recognition/init',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/swt-app/v1/user/recognition/verify Verify rekognition action
     * @apiDescription Verify rekognition action. Validates image for given action
     * @apiGroup SW Ticket App
     *
     */
    'POST /api/swt-app/v1/user/recognition/verify': {
        action: 'v2/API/swt-app/recognition/verify',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/swt-app/v1/events Returns list of events with schedule
     * @apiDescription Returns list of events where user has purchased ticket. Only events with schedule are shown
     * @apiGroup SW Ticket App
     *
     */
    'GET /api/swt-app/v1/events': {
        action: 'v2/API/swt-app/events/list'
    },

    /**
     *
     * @api {get} /api/swt-app/v1/tickets/events List of Events with tickets.
     * @apiDescription Returns list of events with tickets that user has purchased. Only events with purchased tickets are returned.
     * @apiGroup SW Ticket App
     *
     */
    'GET /api/swt-app/v1/tickets/events': {
        action: 'v2/API/swt-app/tickets/events',
    },

    /**
     *
     * @api {get} /api/swt-app/v1/tickets/:barcode/detail Detail of ticket by barcode
     * @apiDescription Returns ticket details by givend barcode.
     * @apiGroup SW Ticket App
     *
     */
    'GET /api/swt-app/v1/tickets/:barcode/detail': {
        action: 'v2/API/swt-app/tickets/detail',
    },

    /**
     *
     * @api {post} /api/swt-app/v1/user/signup Signup API for SW Ticket App
     * @apiDescription Signup for spectator role.
     * @apiGroup SW Ticket App
     *
     */
    'POST /api/swt-app/v1/user/signup': {
        action: 'v2/API/swt-app/user/signup',
        csrf: false,
    },
    /**
     *
     * @api {post} /api/swt-app/v1/user/signin Signin API for SW Ticket App
     * @apiDescription Signin for spectator role.
     * @apiGroup SW Ticket App
     *
     */
    'POST /api/swt-app/v1/user/signin': {
        action: 'v2/API/swt-app/user/signin',
        csrf: false,
    },
};
