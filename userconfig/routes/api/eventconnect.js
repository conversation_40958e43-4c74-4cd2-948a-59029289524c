module.exports = {

    /**
     *
     * @api {get} /api/eventconnect/v1/events Events List
     * @apiDescription Used to get the list of events
     * @apiGroup EventConnect
     *
     */
    "GET   /api/eventconnect/v1/events": { action: "v2/API/eventconnect/events" },

    /**
     *
     * @api {get} /api/eventconnect/v1/teams/:event/:timestamp Teams List
     * @apiDescription Used to get the list of teams for a specific event and timestamp
     * @apiGroup EventConnect
     *
     */
    "GET   /api/eventconnect/v1/teams/:event/:timestamp": { action: "v2/API/eventconnect/teams" },

    /**
     *
     * @api {get} /api/eventconnect/v1/teams/:event/:timestamp/:team Team Details
     * @apiDescription Used to get details of a specific team by event, timestamp, and team ID
     * @apiGroup EventConnect
     *
     */
    "GET   /api/eventconnect/v1/teams/:event/:timestamp/:team": { action: "v2/API/eventconnect/teams" }

};
