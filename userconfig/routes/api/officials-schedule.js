'use strict';

const
	CTRL = 'API/Officials/OfficialsScheduleController.';

module.exports = {
    /**
     *
     * @api {get} /api/schedule/:event/courts_matches/:day/hour/:hour/hours/:hours Court Matches
     * @apiDescription List of courts and their matches schedule, hours, divisions
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event/courts_matches/:day/hour/:hour/hours/:hours': `${CTRL}courts_matches`,

    /**
     *
     * @api {get} /api/schedule/:event Event Info
     * @apiDescription Event info including officials ranks and event dates
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event': `${CTRL}eventInfo`,

    /**
     *
     * @api {get} /api/schedule/:event/officials-list Officials List
     * @apiDescription List of officials
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event/officials-list': `${CTRL}officialsList`,

    /**
     *
     * @api {post} /api/schedule/:event/assign Manage Officials Schedule
     * @apiDescription Manages Officials Schedule: removes/inserts officials assignment to a selected match
     * @apiGroup Official Schedule
     *
     */
    'post /api/schedule/:event/assign': `${CTRL}assign`,

    //TODO: why POST?
    /**
     *
     * @api {post} /api/schedule/:event/official-remove Remove Official from Match
     * @apiDescription Removes official from match
     * @apiGroup Official Schedule
     *
     */
    'post /api/schedule/:event/official-remove': `${CTRL}removeOfficial`,

    /**
     *
     * @api {post} /api/schedule/:event/publish/:publish/day/:day/from/:from/to/:to Publish Schedule
     * @apiDescription Publish/unpublish assigned officials schedule to live
     * @apiGroup Official Schedule
     *
     */
    'post /api/schedule/:event/publish/:publish/day/:day/from/:from/to/:to': `${CTRL}publishSchedule`,

    /**
     *
     * @api {get} /api/schedule/:event/export Schedule Export
     * @apiDescription Exporting schedule
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event/export': `${CTRL}scheduleExport`,

    /**
     *
     * @api {get} /api/schedule/:event/test-reversed Court Matches (reversed) TEST
     * @apiDescription Returns court matches (reversed ?) - looks like it is used for testing
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event/test-reversed': `${CTRL}courtsMatchesReversed`,

    //TODO: need to check route logic
    /**
     *
     * @api {post} /api/schedule/:event/apply-pattern Apply Officials Group Pattern
     * @apiDescription Apply Officials Group Pattern
     * @apiGroup Official Schedule
     *
     */
    'post /api/schedule/:event/apply-pattern': `${CTRL}applyPattern`,

    /**
     *
     * @api {get} /api/schedule/:event/assignments/:date Officials Event Schedule
     * @apiDescription Return officials schedule for a specific date
     * @apiGroup Official Schedule
     *
     */
    'get /api/schedule/:event/assignments/:date': `${CTRL}assignmentsList`,

    //TODO: why POST?
    /**
     *
     * @api {post} /api/schedule/:event/courts_matches/:day/courts Get Courts
     * @apiDescription Returns courts data (teams / officials / etc.)
     * @apiGroup Official Schedule
     *
     */
    'post /api/schedule/:event/courts_matches/:day/courts': `${CTRL}courts`,

    /**
     *
     * @api {get} /api/schedule/:event/moved-matches Moved Matches
     * @apiDescription Moved matches
     * @apiGroup Official Schedule
     *
     */
    'GET /api/schedule/:event/moved-matches': `${CTRL}movedMatches`
}

//'get /api/schedule/events': `${CTRL}eventsList`,
