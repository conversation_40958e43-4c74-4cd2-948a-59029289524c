

module.exports = {
    /**
     *
     * @api {get} /api/user User Info
     * @apiDescription Returns user profile info
     * @apiGroup User
     *
     */
    'get /api/user': 'UserController.find',

    /**
     *
     * @api {put} /api/user User Update
     * @apiDescription Updates user profile data
     * @apiGroup User
     *
     */
    'put /api/user': 'UserController.update',

    /**
     *
     * @api {get} /api/auth User Auth
     * @apiDescription Authenticates user
     * @apiGroup User
     *
     */
    'get /api/auth': 'UserController.auth',

    /**
     *
     * @api {get} /api/activation User Activation
     * @apiDescription Activates user account
     * @apiGroup User
     *
     */
    'get /api/activation': 'SignupController.activate',

    /**
     *
     * @api {post} /api/eo/login EO sign-in (as CD)
     * @apiDescription Sign-In EO as CD
     * @apiGroup User
     *
     */
    // 'post /api/eo/login': 'SigninController.eo_signin',
    'post /api/eo/login': { 
        controller: 'SigninController', 
        action: 'eo_signin',
        csrf: false
    },

    /**
     *
     * @api {put} /api/v2/user Update user
     * @apiDescription Update user
     * @apiGroup User
     *
     */
    "PUT /api/v2/user": { action: "v2/user/update" },

    /**
     *
     * @api {delete} /api/v2/user Delete user
     * @apiDescription Delete user
     * @apiGroup User
     *
     */
    "DELETE /api/v2/user": { action: "v2/user/delete", csrf: false },
}
