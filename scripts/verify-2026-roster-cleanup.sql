-- Verification script for 2026 roster cleanup
-- Run this BEFORE the migration to see what will be affected

-- 1. Count of 2026 events
SELECT 'Total 2026 Events' as description, COUNT(*) as count
FROM event
WHERE season = 2026;

-- 2. Count of roster teams in 2026 events (open vs locked)
SELECT
    'Roster Teams in 2026 Events' as description,
    CASE
        WHEN rt.locked IS TRUE THEN 'Locked (will NOT be cleared)'
        ELSE 'Open (WILL be cleared)'
    END as roster_status,
    COUNT(*) as count
FROM roster_team rt
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
GROUP BY rt.locked IS TRUE
ORDER BY rt.locked IS TRUE;

-- 3. Count of athletes that will be archived (open rosters only)
SELECT 'Athletes in Open 2026 Rosters (WILL BE ARCHIVED)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 4. Count of athletes that will NOT be archived (locked rosters)
SELECT 'Athletes in Locked 2026 Rosters (will NOT be archived)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND rt.locked IS TRUE
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 5. Count of staff that will be archived (open rosters only)
SELECT 'Staff in Open 2026 Rosters (WILL BE ARCHIVED)' as description, COUNT(*) as count
FROM roster_staff_role rsr
INNER JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND rsr.deleted IS NULL
AND rsr.deleted_by_user IS NULL;

-- 6. Count of staff that will NOT be archived (locked rosters)
SELECT 'Staff in Locked 2026 Rosters (will NOT be archived)' as description, COUNT(*) as count
FROM roster_staff_role rsr
INNER JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND rt.locked IS TRUE
AND rsr.deleted IS NULL
AND rsr.deleted_by_user IS NULL;

-- 7. Sample of events that will be affected (including event 25997 mentioned)
SELECT
    e.event_id,
    e.long_name,
    e.season,
    COUNT(CASE WHEN rt.locked IS TRUE THEN 1 END) as locked_teams,
    COUNT(CASE WHEN rt.locked IS NOT TRUE OR rt.locked IS NULL THEN 1 END) as open_teams,
    COUNT(ra.roster_athlete_id) as total_athletes,
    COUNT(rsr.roster_staff_role_id) as total_staff
FROM event e
LEFT JOIN roster_team rt ON rt.event_id = e.event_id
LEFT JOIN roster_athlete ra ON ra.roster_team_id = rt.roster_team_id
    AND ra.deleted IS NULL AND ra.deleted_by_user IS NULL
LEFT JOIN roster_staff_role rsr ON rsr.roster_team_id = rt.roster_team_id
    AND rsr.deleted IS NULL AND rsr.deleted_by_user IS NULL
WHERE e.season = 2026
GROUP BY e.event_id, e.long_name, e.season
HAVING COUNT(rt.roster_team_id) > 0
ORDER BY e.event_id = 25997 DESC, e.event_id
LIMIT 10;

-- 8. Verification for other seasons (should be 0 affected)
SELECT 'Athletes in Non-2026 Events (should be 0 affected)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season != 2026
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 9. Specific check for event 25997 (mentioned in requirements)
SELECT
    'Event 25997 Details' as description,
    e.event_id,
    e.long_name,
    e.season,
    COUNT(CASE WHEN rt.locked IS TRUE THEN 1 END) as locked_teams,
    COUNT(CASE WHEN rt.locked IS NOT TRUE OR rt.locked IS NULL THEN 1 END) as open_teams_to_clear,
    COUNT(CASE WHEN (rt.locked IS NULL OR rt.locked IS NOT TRUE) AND ra.roster_athlete_id IS NOT NULL THEN 1 END) as athletes_to_archive,
    COUNT(CASE WHEN (rt.locked IS NULL OR rt.locked IS NOT TRUE) AND rsr.roster_staff_role_id IS NOT NULL THEN 1 END) as staff_to_archive
FROM event e
LEFT JOIN roster_team rt ON rt.event_id = e.event_id
LEFT JOIN roster_athlete ra ON ra.roster_team_id = rt.roster_team_id
    AND ra.deleted IS NULL AND ra.deleted_by_user IS NULL
LEFT JOIN roster_staff_role rsr ON rsr.roster_team_id = rt.roster_team_id
    AND rsr.deleted IS NULL AND rsr.deleted_by_user IS NULL
WHERE e.event_id = 25997
GROUP BY e.event_id, e.long_name, e.season;

-- 10. Count of master athletes that will be archived (2026 season, open rosters only)
SELECT 'Master Athletes for 2026 Open Rosters (WILL BE ARCHIVED)' as description, COUNT(*) as count
FROM master_athlete ma
WHERE ma.season = 2026
AND ma.deleted IS NULL
AND ma.master_team_id IN (
    SELECT DISTINCT rt.master_team_id
    FROM roster_team rt
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
    AND rt.master_team_id IS NOT NULL
);

-- 11. Count of master staff that will be archived (2026 season, open rosters only)
SELECT 'Master Staff for 2026 Open Rosters (WILL BE ARCHIVED)' as description, COUNT(*) as count
FROM master_staff ms
WHERE ms.season = 2026
AND ms.deleted IS NULL
AND ms.master_staff_id IN (
    SELECT DISTINCT msr.master_staff_id
    FROM master_staff_role msr
    INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
    AND rt.master_team_id IS NOT NULL
);

-- 12. Count of master athletes that will NOT be archived (locked rosters)
SELECT 'Master Athletes for 2026 Locked Rosters (will NOT be archived)' as description, COUNT(*) as count
FROM master_athlete ma
WHERE ma.season = 2026
AND ma.deleted IS NULL
AND ma.master_team_id IN (
    SELECT DISTINCT rt.master_team_id
    FROM roster_team rt
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND rt.locked IS TRUE
    AND rt.master_team_id IS NOT NULL
);

-- 13. Count of master staff that will NOT be archived (locked rosters)
SELECT 'Master Staff for 2026 Locked Rosters (will NOT be archived)' as description, COUNT(*) as count
FROM master_staff ms
WHERE ms.season = 2026
AND ms.deleted IS NULL
AND ms.master_staff_id IN (
    SELECT DISTINCT msr.master_staff_id
    FROM master_staff_role msr
    INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND rt.locked IS TRUE
    AND rt.master_team_id IS NOT NULL
);
