-- Post-migration verification script for 2026 roster cleanup
-- Run this AFTER the migration to confirm the results

-- 1. Verify no active athletes remain in open 2026 rosters
SELECT 'Active Athletes in Open 2026 Rosters (should be 0)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 2. Verify no active staff remain in open 2026 rosters
SELECT 'Active Staff in Open 2026 Rosters (should be 0)' as description, COUNT(*) as count
FROM roster_staff_role rsr
INNER JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND rsr.deleted IS NULL
AND rsr.deleted_by_user IS NULL;

-- 3. Count of archived athletes (should match pre-migration count)
SELECT 'Archived Athletes from 2026 Open Rosters' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND ra.deleted_by_user = 'system_archive_2026';

-- 4. Count of archived staff (should match pre-migration count)
SELECT 'Archived Staff from 2026 Open Rosters' as description, COUNT(*) as count
FROM roster_staff_role rsr
INNER JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
AND rsr.deleted_by_user = 'system_archive_2026';

-- 5. Verify locked rosters were NOT affected
SELECT 'Active Athletes in Locked 2026 Rosters (should be unchanged)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND rt.locked IS TRUE
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 6. Verify locked rosters staff were NOT affected
SELECT 'Active Staff in Locked 2026 Rosters (should be unchanged)' as description, COUNT(*) as count
FROM roster_staff_role rsr
INNER JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season = 2026
AND rt.locked IS TRUE
AND rsr.deleted IS NULL
AND rsr.deleted_by_user IS NULL;

-- 7. Verify other seasons were NOT affected
SELECT 'Active Athletes in Non-2026 Events (should be unchanged)' as description, COUNT(*) as count
FROM roster_athlete ra
INNER JOIN roster_team rt ON rt.roster_team_id = ra.roster_team_id
INNER JOIN event e ON e.event_id = rt.event_id
WHERE e.season != 2026
AND ra.deleted IS NULL
AND ra.deleted_by_user IS NULL;

-- 8. Specific verification for event 25997
SELECT
    'Event 25997 Post-Migration Status' as description,
    e.event_id,
    e.long_name,
    e.season,
    COUNT(CASE WHEN rt.locked IS TRUE THEN 1 END) as locked_teams,
    COUNT(CASE WHEN rt.locked IS NOT TRUE OR rt.locked IS NULL THEN 1 END) as open_teams,
    COUNT(CASE WHEN (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        AND ra.deleted IS NULL AND ra.deleted_by_user IS NULL THEN 1 END) as active_athletes_in_open_teams,
    COUNT(CASE WHEN (rt.locked IS NULL OR rt.locked IS NOT TRUE)
        AND rsr.deleted IS NULL AND rsr.deleted_by_user IS NULL THEN 1 END) as active_staff_in_open_teams,
    COUNT(CASE WHEN ra.deleted_by_user = 'system_archive_2026' THEN 1 END) as archived_athletes,
    COUNT(CASE WHEN rsr.deleted_by_user = 'system_archive_2026' THEN 1 END) as archived_staff
FROM event e
LEFT JOIN roster_team rt ON rt.event_id = e.event_id
LEFT JOIN roster_athlete ra ON ra.roster_team_id = rt.roster_team_id
LEFT JOIN roster_staff_role rsr ON rsr.roster_team_id = rt.roster_team_id
WHERE e.event_id = 25997
GROUP BY e.event_id, e.long_name, e.season;

-- 9. Verify no active master athletes remain for 2026 open rosters
SELECT 'Active Master Athletes for 2026 Open Rosters (should be 0)' as description, COUNT(*) as count
FROM master_athlete ma
WHERE ma.season = 2026
AND ma.deleted IS NULL
AND ma.master_team_id IN (
    SELECT DISTINCT rt.master_team_id
    FROM roster_team rt
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
    AND rt.master_team_id IS NOT NULL
);

-- 10. Verify no active master staff remain for 2026 open rosters
SELECT 'Active Master Staff for 2026 Open Rosters (should be 0)' as description, COUNT(*) as count
FROM master_staff ms
WHERE ms.season = 2026
AND ms.deleted IS NULL
AND ms.master_staff_id IN (
    SELECT DISTINCT msr.master_staff_id
    FROM master_staff_role msr
    INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND (rt.locked IS NULL OR rt.locked IS NOT TRUE)
    AND rt.master_team_id IS NOT NULL
);

-- 11. Count of archived master athletes (should match pre-migration count)
SELECT 'Archived Master Athletes for 2026 Open Rosters' as description, COUNT(*) as count
FROM master_athlete ma
WHERE ma.season = 2026
AND ma.deleted_by_user = 'system_archive_2026';

-- 12. Count of archived master staff (should match pre-migration count)
SELECT 'Archived Master Staff for 2026 Open Rosters' as description, COUNT(*) as count
FROM master_staff ms
WHERE ms.season = 2026
AND ms.deleted_by_user = 'system_archive_2026';

-- 13. Verify master athletes in locked rosters were NOT affected
SELECT 'Active Master Athletes for 2026 Locked Rosters (should be unchanged)' as description, COUNT(*) as count
FROM master_athlete ma
WHERE ma.season = 2026
AND ma.deleted IS NULL
AND ma.master_team_id IN (
    SELECT DISTINCT rt.master_team_id
    FROM roster_team rt
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND rt.locked IS TRUE
    AND rt.master_team_id IS NOT NULL
);

-- 14. Verify master staff in locked rosters were NOT affected
SELECT 'Active Master Staff for 2026 Locked Rosters (should be unchanged)' as description, COUNT(*) as count
FROM master_staff ms
WHERE ms.season = 2026
AND ms.deleted IS NULL
AND ms.master_staff_id IN (
    SELECT DISTINCT msr.master_staff_id
    FROM master_staff_role msr
    INNER JOIN roster_team rt ON rt.master_team_id = msr.master_team_id
    INNER JOIN event e ON e.event_id = rt.event_id
    WHERE e.season = 2026
    AND rt.locked IS TRUE
    AND rt.master_team_id IS NOT NULL
);

-- 13. Summary of all archived records
SELECT
    'Summary: Total Archived Records' as description,
    (SELECT COUNT(*) FROM roster_athlete WHERE deleted_by_user = 'system_archive_2026') as roster_athletes_archived,
    (SELECT COUNT(*) FROM roster_staff_role WHERE deleted_by_user = 'system_archive_2026') as roster_staff_archived,
    (SELECT COUNT(*) FROM master_athlete WHERE deleted_by_user = 'system_archive_2026') as master_athletes_archived,
    (SELECT COUNT(*) FROM master_staff WHERE deleted_by_user = 'system_archive_2026') as master_staff_archived;
