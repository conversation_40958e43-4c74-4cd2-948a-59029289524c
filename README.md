# SportWrench Application

## Local development
### Dependencies
* NodeJS 16.0.0
* PostgreSQL 15 
* Redis 3.2.11

### Local setup
Run docker to setup local database and redis

```docker-compose -f docker-compose.local.yml up -d```

Restore database from dump (ask for dump file from team)

```docker cp your_dump_file.sql sw-db:/your_dump_file.sql```

```docker exec -it sw-db psql -U postgres -d sw_dev -f /your_dump_file.sql```

Create local config files

```touch config/local.js```

Ask for config/local.js file from team and replace just created file with it

Create dev connection file (Optional, so can be skipped it, could be loaded from .env as a default value)

```mkdir ../connection && cp dev.connection.example.js ../connection/dev.js```

Rename .env.example to .env 
```cp .env.example .env```

### Install dependencies

```npm ci```

``` npx bower install ```

### Run Backend
 
```npm start```
or
```nodemon app.js```

For nodemon's settings details see the nodemon.json

### Run frontend

Frontend: ```npm run start:frontend```

Events: ```npm run start:frontend:events``` (optional)

Admin: ```npm run start:frontend:admin``` (optional)

For local development use ```npm run start:frontend``` to make AEM Basic Layouts work.

### Run tests
Restore test database from dump (ask for dump file from team)

```docker cp your_test_db_dump_file.sql sw-db:/your_test_db_dump_file.sql```

```docker exec -it sw-db psql -U postgres -d sw_dev_test -f /your_test_db_dump_file.sql```

```npm run test```

## SailsJS generate commands
### action:
```sails generate action v2/API/some-action```
### helpers:
```sails generate helper housing/api-auth```
